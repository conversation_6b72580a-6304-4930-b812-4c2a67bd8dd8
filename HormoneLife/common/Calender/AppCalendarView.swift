//
//  AppCalendarView.swift
//  ISCCamera
//
//  Created by Tank on 2024/6/2.
//

import Foundation
import UIKit
import JTAppleCalendar
import SnapKit

enum CalendarDataType {
    case success, fail, waitting, unknown
}

enum CalendarDayViewType {
    case off, holiday, unknown
}

class AppCalendarView: UIView {
    
    enum DisableDate {
        case prevDate, nextDate , none
    }
    
    var weekArray = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday",]
    var currentCalendar = Calendar.current
    var isNextMonthEnable = false
    var isPreMonthEnable = false
    var beforeCurrentMonth: Int = 1 
    var afterCurrentMonth: Int = 1
    var disableDate : DisableDate = .nextDate
    
    private lazy var buttonPrev: UIButton = {
        let b = UIButton().makelayoutable()
        b.setImage(UIImage(named: "calendarLeftArow"), for: .normal)
        b.setTitleColor(.black, for: .normal)
        b.addTarget(self, action: #selector(self.buttonPrevHandler(_:)), for: .touchUpInside)
        return b
    }()
    
    private lazy var buttonNext: UIButton = {
        let b = UIButton().makelayoutable()
        b.setImage(UIImage(named: "calendarRightArow"), for: .normal)
        b.setTitleColor(.black, for: .normal)
        b.addTarget(self, action: #selector(self.buttonNextHandler(_:)), for: .touchUpInside)
        return b
    }()

    lazy var yearTitleLabel : UILabel = {
        let v = UILabel()
        v.font = UIFont.boldGilroyFont(18)
        v.textColor = AppCalendarConfig.labelYearTitleTextColor
        v.textAlignment = .center
        return v
    }()
    
    lazy var monthTitleLabel : UILabel = {
        let v = UILabel()
        v.font = UIFont.lightGilroyFont(16)
        v.textColor = AppCalendarConfig.labelMonthTitleTextColor
        v.text = "March"
        v.textAlignment = .center
        return v
    }()
    
    private lazy var todayButton: UIButton = {
        let b = UIButton().makelayoutable()
        b.setTitle("Today", for: .normal)
        b.titleLabel?.font = UIFont.regularGilroyFont(16)
        b.setTitleColor(AppCalendarConfig.labelTodayTitleTextColor, for: .normal)
        b.addTarget(self, action: #selector(self.todayButtonHandler(_:)), for: .touchUpInside)
        return b
    }()

    lazy var viewDaysOfWeek : UIView = {
        let v = UIView()
        return v
    }()
    
    lazy var calendarView : JTACMonthView = {
        let v = JTACMonthView().makelayoutable()
        v.backgroundColor = .white
        v.isUserInteractionEnabled = true
        v.ibCalendarDelegate = self
        v.ibCalendarDataSource = self
        v.showsHorizontalScrollIndicator = false
        v.scrollingMode = .stopAtEachSection
        v.scrollDirection = .horizontal
        v.isScrollEnabled = true
        v.register(AppCalendarCell.self, forCellWithReuseIdentifier: "AppCalendarCell")
        return v
    }()
    
    weak var delegate : AppCalendarViewDelegate?
    /**
     - Returns: Ngày được chọn
     */
    var dateSelected : Date? {
        return self.calendarView.selectedDates.first
    }
    
    var currentYearMonth: String = "" {
        didSet {
            guard currentYearMonth != oldValue else { return }
            delegate?.currentDisplayDate(yyyyMM: currentYearMonth)
        }
    }
    
    init(frame: CGRect, delegate: AppCalendarViewDelegate?) {
        super.init(frame: frame)
        self.delegate = delegate
        setupUI()
    }

    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    private func setupUI() {
        backgroundColor = .white
        setupViews()
        moveToDay()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        addSubview(yearTitleLabel)
        addSubview(monthTitleLabel)
        addSubview(todayButton)
        addSubview(viewDaysOfWeek)
        addSubview(calendarView)
        addSubview(buttonPrev)
        addSubview(buttonNext)
 
        yearTitleLabel.snp.makeConstraints { (make) -> Void in
            make.top.equalToSuperview().offset(20)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
        }
        
        monthTitleLabel.snp.makeConstraints { (make) -> Void in
            make.top.equalTo(yearTitleLabel.snp.bottom).offset(16)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
        }
        
        todayButton.snp.makeConstraints { make in
            make.centerY.equalTo(monthTitleLabel)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(24)
            make.width.equalTo(60)
        }
        
        // create day of week
        viewDaysOfWeek.snp.makeConstraints { (make) -> Void in
            make.top.equalTo(monthTitleLabel.snp.bottom).offset(12)
            make.leading.equalTo(self).offset(20)
            make.trailing.equalTo(self).offset(-20)
        }
        daysOfWeekPrepare()
        
        /// calendar view
        calendarView.snp.makeConstraints { (make) -> Void in
            make.top.equalTo(viewDaysOfWeek.snp.bottom).offset(12)
            make.bottom.equalTo(self).offset(-30)
            make.leading.equalTo(self).offset(20)
            make.trailing.equalTo(self).offset(-20)
        }
        
        NSLayoutConstraint.activate([
            buttonPrev.widthAnchor.constraint(equalToConstant: 32),
            buttonPrev.heightAnchor.constraint(equalToConstant: 32),
            buttonPrev.centerYAnchor.constraint(equalTo: yearTitleLabel.centerYAnchor),
            buttonPrev.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            
            buttonNext.widthAnchor.constraint(equalTo: buttonPrev.widthAnchor),
            buttonNext.heightAnchor.constraint(equalTo: buttonPrev.heightAnchor),
            buttonNext.centerYAnchor.constraint(equalTo: yearTitleLabel.centerYAnchor),
            buttonNext.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20)

        ])
    }
    
    
    private func daysOfWeekPrepare() {
        let stackDaysOfWeek = UIStackView()
        stackDaysOfWeek.distribution = .fillEqually
        stackDaysOfWeek.axis = .horizontal
        
        for item in weekArray {
            let thuLabel = createDayOfWeekLabel(text: item)
            stackDaysOfWeek.addArrangedSubview(thuLabel)
        }
        
        
        viewDaysOfWeek.addSubview(stackDaysOfWeek)
        stackDaysOfWeek.snp.makeConstraints { (make) -> Void in
            make.top.equalTo(viewDaysOfWeek).offset(8)
            make.bottom.equalTo(viewDaysOfWeek).offset(-8)
            make.leading.equalTo(viewDaysOfWeek)
            make.trailing.equalTo(viewDaysOfWeek)
        }
    }
    
    private func createDayOfWeekLabel(text : String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.textAlignment = .center
        label.textColor = AppCalendarConfig.labelDayOfWeekTextColor
        label.font = AppCalendarConfig.labelDayOfWeekFont
        return label
    }
    
    func configure(beforeCurrentMonth: Int, afterCurrentMonth: Int, isNextMonthEnable: Bool, isPreMonthEnable: Bool) {
        self.beforeCurrentMonth = beforeCurrentMonth
        self.afterCurrentMonth = afterCurrentMonth
        self.isPreMonthEnable = isPreMonthEnable
        self.isNextMonthEnable = isNextMonthEnable
        reloadData()
        moveToDay()
    }
    /**
        Load lại data.
     */
    func reloadData() {
        calendarView.reloadData()
    }

    func moveToDay() {
        let dateNow = Date()
        calendarView.selectDates([dateNow], triggerSelectionDelegate: false)
        calendarView.scrollToHeaderForDate(dateNow, withAnimation: false)
        setupViewsOfCalendar(from: dateNow)
        
    }
    
    private func resetCell(cell: AppCalendarCell, cellState: CellState){
        cell.isHidden = false
        cell.isUserInteractionEnabled = true
        cell.dayLabel.text = cellState.text
        cell.selectedView.isHidden = true
        cell.selectedView.layer.sublayers = []
        cell.dayLabel.textColor = AppCalendarConfig.textDeSelectedColor
        cell.circleMarkView.isHidden = true
        cell.heartPinkView.isHidden = true
        
        cell.lh_uitra.isHidden = true
        cell.fsh.isHidden = true
        cell.pdg.isHidden = true
        cell.angleGroupView.isHidden = true
        cell.lhTestDay.isHidden = true
        cell.hcg.isHidden = true
    }
    
    private func handleCellConfiguration(cell: AppCalendarCell?, cellState: CellState, indexPath: IndexPath, date: Date) {
        guard let myCustomCell = cell  else {
            return
        }
        resetCell(cell: myCustomCell, cellState: cellState)
//        if(cellState.dateBelongsTo == .thisMonth || cellState.dateBelongsTo == .previousMonthWithinBoundary || cellState.dateBelongsTo == .followingMonthWithinBoundary){
        if cellState.dateBelongsTo == .thisMonth {
            var enable = true
            var isComingDays = true
            switch self.disableDate {
            case .nextDate:
                if !isNextMonthEnable {
                    isComingDays = !(cellState.date.removeTimeStamp > Date().removeTimeStamp)
                    
                    //enable = !(cellState.date.removeTimeStamp > Date().removeTimeStamp)
                    // is comeing days selecteble
                }
            case .prevDate:
                if isNextMonthEnable {
                    enable = !(cellState.date.removeTimeStamp < Date().removeTimeStamp)
                } else {
                    enable = false
                }
            default:
                break
            }
            myCustomCell.isUserInteractionEnabled = enable
            if enable {
                if cellState.isSelected {
                    /// text color
                    myCustomCell.dayLabel.textColor = AppCalendarConfig.textSelectedColor
                    myCustomCell.dayLabel.font = AppCalendarConfig.labelSeletedDayFont
                    /// background
                    myCustomCell.selectedView.layer.cornerRadius =  myCustomCell.selectedViewSize.height / 2
                    
                    // select color
                    var selectedcolor : UIColor = AppCalendarConfig.defaultSelectedColor
                    if let dayType = delegate?.cellDayType(view: cell, text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date), let color = delegate?.cellSelectedBackgroundColorDayType(view: cell,  text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date)[dayType] {
                        selectedcolor = color
                    }
                    else if let color = delegate?.cellSelectedBackgroundColorDayOfWeek(view: cell, text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date)[date.dayOfWeek]{
                        selectedcolor = color
                    }
                    myCustomCell.selectedView.backgroundColor = selectedcolor
                    myCustomCell.selectedView.isHidden = false
                    
                    /// current date select
                    if Calendar.current.isDateInToday(date) {
                        myCustomCell.selectedView.layer.borderWidth = 0
                    }
                    
                } else {
                    // unselected
                    /// text color
                    var selectedcolor : UIColor = AppCalendarConfig.textDeSelectedColor
                    if let dayType = delegate?.cellDayType(view: cell,  text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date), let color = delegate?.cellTextColorDayType(view: cell,  text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date)[dayType] {
                        selectedcolor = color
                    }
                    else if let color = delegate?.cellColorDayOfWeek(view: cell,  text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date)[date.dayOfWeek]{
                        selectedcolor = color
                    }
                    myCustomCell.dayLabel.textColor = selectedcolor
                    myCustomCell.dayLabel.font = AppCalendarConfig.labelDayFont
                    
                    /// background
                    myCustomCell.selectedView.backgroundColor = AppCalendarConfig.defaultDeSelectedColor
                    myCustomCell.selectedView.isHidden = true
                    
                    /// current date deselect
                    if Calendar.current.isDateInToday(date) {
                        myCustomCell.dayLabel.textColor = .mainTextColor
                        myCustomCell.selectedView.layer.borderWidth = 1
                        myCustomCell.selectedView.layer.borderColor = AppCalendarConfig.textCurrentDayColor.cgColor
                        myCustomCell.selectedView.layer.cornerRadius =  myCustomCell.selectedViewSize.height / 2
                        myCustomCell.selectedView.isHidden = false
                    }
                }
            } else {
                myCustomCell.dayLabel.textColor = AppCalendarConfig.textDisableColor
                myCustomCell.dayLabel.font = AppCalendarConfig.labelDayFont
                myCustomCell.selectedView.backgroundColor = AppCalendarConfig.defaultDeSelectedColor
            }
            
            if let labelTypes = delegate?.cellDayMarkLabelType(view: cell, text: cellState.text, isSelected: cellState.isSelected, indexPath: indexPath, date: date, isEnableDate: isComingDays),
               !labelTypes.isEmpty {
                myCustomCell.setupMarkLabel(dateTypies: labelTypes)
            }
            
            
        } else {
            myCustomCell.isHidden = true
            myCustomCell.isUserInteractionEnabled = false
        }
        
        delegate?.cellConfig(view: cell,  text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date)
    }

    private func setupViewsOfCalendar(from date: Date) {
        currentYearMonth = "\(date.year)-\(date.month)"
        yearTitleLabel.text = "\(date.year)"
        monthTitleLabel.text = date.enMonth
        let month = self.calendarView.numberOfSections(in: self.calendarView)
        if let currentSection = self.calendarView.currentSection() {
            if currentSection < month - 1 {
                buttonNext.isEnabled = true
                buttonNext.setTitleColor(.black, for: .normal)
            } else {
                buttonNext.isEnabled = false
                buttonNext.setTitleColor(.lightGray, for: .normal)
            }
            
            if currentSection > 0 {
                buttonPrev.isEnabled = true
                buttonPrev.setTitleColor(.black, for: .normal)
            } else {
                buttonPrev.isEnabled = false
                buttonPrev.setTitleColor(.lightGray, for: .normal)
            }
        }
    }
    
    @objc private func buttonNextHandler(_ button: UIButton) {
        if let currentSection = self.calendarView.currentSection() {
            self.calendarView.scrollToItem(at: .init(row: 0, section: currentSection + 1), at: .left, animated: true)
        }
       
    }
    @objc private func buttonPrevHandler(_ button: UIButton) {
        if let currentSection = self.calendarView.currentSection() {
            self.calendarView.scrollToItem(at: .init(row: 0, section: currentSection - 1), at: .left, animated: true)
        }
    }
    
    @objc func todayButtonHandler(_ button: UIButton) {
        reloadData()
        moveToDay()
        delegate?.appCalendarView(self, dateSelected: Date())
    }

}
extension AppCalendarView: JTACMonthViewDelegate, JTACMonthViewDataSource  {
    
    func configureCalendar(_ calendar: JTACMonthView) -> ConfigurationParameters {
        let curDate = Date()
        let startDate = curDate.adding(.month, value: -self.beforeCurrentMonth)
        print("afterCurrentMonth === \(afterCurrentMonth)")
        let endDate = curDate.adding(.month, value: self.afterCurrentMonth)

        let parameters = ConfigurationParameters(startDate: startDate,
                                                 endDate: endDate,
                                                 generateInDates: .forAllMonths,
                                                 generateOutDates: .tillEndOfRow)
        return parameters
    }

    func configureVisibleCell(myCustomCell: AppCalendarCell, cellState: CellState, date: Date, indexPath: IndexPath) {
        myCustomCell.dayLabel.text = cellState.text
        handleCellConfiguration(cell: myCustomCell, cellState: cellState, indexPath: indexPath, date: date)
    }
    
    func calendar(_ calendar: JTACMonthView, willDisplay cell: JTACDayCell, forItemAt date: Date, cellState: CellState, indexPath: IndexPath) {
        // This function should have the same code as the cellForItemAt function
        let myCustomCell = cell as! AppCalendarCell
        configureVisibleCell(myCustomCell: myCustomCell, cellState: cellState, date: date, indexPath: indexPath)
    }
    
    func calendar(_ calendar: JTACMonthView, cellForItemAt date: Date, cellState: CellState, indexPath: IndexPath) -> JTACDayCell {
        let myCustomCell = calendar.dequeueReusableCell(withReuseIdentifier: "AppCalendarCell", for: indexPath) as! AppCalendarCell        
        configureVisibleCell(myCustomCell: myCustomCell, cellState: cellState, date: date, indexPath: indexPath)
        myCustomCell.dataTypeColor = delegate?.cellColorDataType(view: myCustomCell, text: cellState.text, isSelected : cellState.isSelected, indexPath: indexPath, date: date) ?? [:]
        return myCustomCell
    }
    
    func calendar(_ calendar: JTACMonthView, didDeselectDate date: Date, cell: JTACDayCell?, cellState: CellState, indexPath: IndexPath) {
        guard let cell  = cell as? AppCalendarCell else { return }
        handleCellConfiguration(cell: cell, cellState: cellState, indexPath: indexPath, date: date)
    }
    
    func calendar(_ calendar: JTACMonthView, didSelectDate date: Date, cell: JTACDayCell?, cellState: CellState, indexPath: IndexPath) {
        guard let cell  = cell as? AppCalendarCell else { return }
        handleCellConfiguration(cell: cell, cellState: cellState, indexPath: indexPath, date: date)
        setupViewsOfCalendar(from: date)
        delegate?.appCalendarView(self, dateSelected: date)
    }
    
    func sizeOfDecorationView(indexPath: IndexPath) -> CGRect {
        let stride = calendarView.frame.width * CGFloat(indexPath.section)
        return CGRect(x: stride + 5, y: 5, width: calendarView.frame.width - 10, height: calendarView.frame.height - 10)
    }
    
    func calendarDidScroll(_ calendar: JTACMonthView) {
        guard let startDate = calendar.visibleDates().monthDates.first?.date else {
            return
        }
        setupViewsOfCalendar(from: startDate)
    }
}
