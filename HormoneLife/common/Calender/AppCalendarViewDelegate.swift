//
//  AppCalendarViewDelegate.swift
//  ISCCamera
//
//  Created by Tank on 2024/6/2.
//

import Foundation
import UIKit
protocol AppCalendarViewDelegate : AnyObject {
    /**
     Chỉnh sửa layout date.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     */
    func cellConfig(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date)
    /**
     Dữ liệu trong ngày.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     - Returns: DS Dữ liệu trong ngày.
     */
    func cellDataType(view: AppCalendarCell?, text: String, isSelected : <PERSON>ol, indexPath: IndexPath, date: Date) -> [CalendarDataType]
    /**
     Loại ngày.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     - Returns: Loại ngày.
     */
    func cellDayType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> CalendarDayViewType
    /**
     Màu chữ theo loại ngày.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     - Returns: Màu chữ theo loại ngày.
     */
    func cellTextColorDayType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDayViewType : UIColor]
    /**
     Màu background theo loại ngày.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     - Returns: Màu background theo loại ngày.
     */
    func cellSelectedBackgroundColorDayType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDayViewType : UIColor]
    /**
     Màu chữ theo ngày trong tuần.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     - Returns: Màu chữ theo ngày trong tuần.
     */
    func cellColorDayOfWeek(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [Date.Day : UIColor]
    /**
     Màu background theo ngày trong tuần.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     - Returns: Màu background theo ngày trong tuần.
     */
    func cellSelectedBackgroundColorDayOfWeek(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [Date.Day : UIColor]
    
    /**
     Màu dữ liệu trong ngày.
     - Parameters:
         - view: Date layout
         - text: ngày (String)
         - indexPath: Vị trí date
         - date: ngày (Date)
         - isSelected: Date có đang được chọn không
     - Returns: Màu dữ liệu trong ngày.
     */
    func cellColorDataType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType : UIColor]
    
    
    /**
     chon ngay.
     - Parameters:
         - dateSelected: ngay chon
     */
    func appCalendarView(_ view : AppCalendarView, dateSelected date: Date)
    
    
    /**
     date type.
     - Parameters:
         - view: Date layout
         - text: type (String)
         - indexPath: index of date
         - date: type (Date)
         - isSelected: Date is seleted
         - isEnableDate: past days is enable, future days is disable
     - Returns: mark label type.
     */
    func cellDayMarkLabelType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date, isEnableDate: Bool) -> [CalendarLabel]
    
    func currentDisplayDate(yyyyMM: String)
}

extension AppCalendarViewDelegate{
    func cellConfig(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) {
        
    }
    func cellDataType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType] {
        return []
    }
    
    func cellDayType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> CalendarDayViewType {
        return .unknown
    }
    
    func appCalendarView(_ view : AppCalendarView, dateSelected date: Date) {
        
    }
    
    func cellTextColorDayType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDayViewType : UIColor] {
        return [:]
    }
    
    func cellSelectedBackgroundColorDayType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDayViewType : UIColor] {
        return [:]
    }
    
    func cellColorDayOfWeek(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [Date.Day : UIColor] {
        return [:]
    }
    
    func cellSelectedBackgroundColorDayOfWeek(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [Date.Day : UIColor] {
        return [:]
    }
    
    func cellColorDataType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType : UIColor] {
        return [:]
    }
    
    func cellDayMarkLabelType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date, isEnableDate: Bool) -> [CalendarLabel] {
        return []
    }
    
    func currentDisplayDate(yyyyMM: String) {}
}
