//
//  AppCalendarConfig.swift
//  ISCCamera
//
//  Created by Tank on 2024/6/2.
//

import Foundation
import UIKit
struct AppCalendarConfig {
    
    static let labelYearTitleTextColor : UIColor = .mainTextColor
    static let labelMonthTitleTextColor: UIColor = UIColor("#514054")
    static let labelTodayTitleTextColor: UIColor = .mainTextColor
    static let labelDayOfWeekTextColor : UIColor = .mainTextColor
    static let labelDayOfWeekFont : UIFont = .boldGilroyFont(9)
    static let labelDayFont : UIFont = .regularGilroyFont(16)
    static let labelSeletedDayFont: UIFont = .boldGilroyFont(16)
    
    static let defaultSelectedColor : UIColor = .themeColor
    static let defaultDeSelectedColor : UIColor = .white
    static let textSelectedColor : UIColor = .mainTextColor
    static let textDeSelectedColor : UIColor = .black
    static let textCurrentDayColor : UIColor = .mainTextColor
    static let textAnotherMonthColor : UIColor = .gray
    static let textDisableColor : UIColor = .gray
    
}
