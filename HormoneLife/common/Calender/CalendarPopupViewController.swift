//
//  CalendarPopupViewController.swift
//  MilkTime
//
//  Created by Match on 2023/7/8.
//

import UIKit
import JTAppleCalendar

protocol CalendarPopupViewControllerDelegate: AnyObject{
    func calendar(date: Date)
}

extension CalendarPopupViewControllerDelegate {
    func calendar(date: Date) {}
}

class CalendarPopupViewController: BaseViewController {
    weak var delegate: CalendarPopupViewControllerDelegate?
    var isNextMonthEnable = false
    var isPreMonthEnable = false
    var beforeCurrentMonth: Int = 1 
    var afterCurrentMonth: Int = 1
    var calendarView: AppCalendarView!
    
    var completion: ((Date) -> Void)?
    
    init(beforeCurrentMonth: Int, afterCurrentMonth: Int, isNextMonthEnable: Bool, isPreMonthEnable: Bool, completion: ((Date) -> Void)? = nil) {
        super.init(nibName: nil, bundle: nil)
        self.beforeCurrentMonth = beforeCurrentMonth
        self.afterCurrentMonth = afterCurrentMonth
        self.isNextMonthEnable = isNextMonthEnable
        self.isPreMonthEnable = isPreMonthEnable
        self.completion = completion
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
    }
    
    func setupViews() {
        calendarView = AppCalendarView().makelayoutable()
        calendarView.configure(beforeCurrentMonth: beforeCurrentMonth, afterCurrentMonth: afterCurrentMonth, isNextMonthEnable: isNextMonthEnable, isPreMonthEnable: isPreMonthEnable)
        calendarView.delegate = self
        view.addSubview(calendarView)
        NSLayoutConstraint.activate([
            calendarView.topAnchor.constraint(equalTo: view.topAnchor),
            calendarView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            calendarView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            calendarView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

}

extension CalendarPopupViewController : AppCalendarViewDelegate{
    func cellDataType(view: AppCalendarCell?,  text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType] {
        if Calendar.current.isDateInToday(date) {
            return [.fail,.success, .waitting]
        }
        return []
    }
    func cellColorDataType(view: AppCalendarCell?,  text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType : UIColor] {
        return [.fail : .red, .success : .green, .waitting : .orange, .unknown : .black]
    }

    func appCalendarView(_ view : AppCalendarView, dateSelected date: Date) {
        delegate?.calendar(date: date + 72000)
        completion?(date + 72000)
        self.dismiss(animated: true)
    }
}
