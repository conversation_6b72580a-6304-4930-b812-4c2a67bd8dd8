//
//  AppCalendarCell.swift
//  ISCCamera
//
//  Created by Tank on 2024/6/2.
//

import Foundation
import UIKit
import JTAppleCalendar

class AppCalendarCell: JTACDayCell {
    let selectedViewSize : CGSize = .init(width: 24, height: 24)
    var dataTypeColor : [CalendarDataType : UIColor] = [:]
    
    lazy var dayLabel: UILabel = {
        let v = UILabel()
        v.textColor = .lightGray
        v.font = AppCalendarConfig.labelDayFont
        v.translatesAutoresizingMaskIntoConstraints = false
        return v
    }()
    
    lazy var selectedView: UIView = {
        let v = UIView()
        v.translatesAutoresizingMaskIntoConstraints = false
        v.backgroundColor = .blue
        return v
    }()
    
    lazy var circleMarkView: UIImageView = {
        let v = UIImageView()
        v.image = UIImage(named: "dotCircleGreen")
        v.translatesAutoresizingMaskIntoConstraints = false
        v.contentMode = .scaleAspectFit
        v.isHidden = true
        return v
    }()
    
    lazy var heartPinkView: UIImageView = {
        let v = UIImageView(frame: CGRectMake(0, 0, 10, 10))
        v.image = UIImage(named: "heartPink")
        //v.translatesAutoresizingMaskIntoConstraints = false
        v.contentMode = .scaleAspectFit
        v.isHidden = true
        return v
    }()
    
    lazy var angleGroupView: UIStackView = {
        let stackview = UIStackView()
        stackview.distribution = .fill
        stackview.alignment = .center
        stackview.axis = .horizontal
        stackview.spacing = 0
        stackview.isHidden = false
        stackview.translatesAutoresizingMaskIntoConstraints = false
        return stackview
    }()
    
    lazy var lh_uitra: UIImageView = {
        let v = UIImageView(frame: CGRectMake(0, 0, 12, 12))
        v.image = UIImage(named: "trangleRed")
        v.contentMode = .scaleAspectFit
        v.isHidden = true
        return v
    }()
    
    lazy var fsh: UIImageView = {
        let v = UIImageView(frame: CGRectMake(0, 0, 12, 12))
        v.image = UIImage(named: "triangleYellow")
        v.contentMode = .scaleAspectFit
        v.isHidden = true
        return v
    }()
    
    lazy var pdg: UIImageView = {
        let v = UIImageView(frame: CGRectMake(0, 0, 12, 12))
        v.image = UIImage(named: "trianglePurple")
        v.contentMode = .scaleAspectFit
        v.isHidden = true
        return v
    }()
    
    lazy var lhTestDay: UIImageView = {
        let v = UIImageView(frame: CGRectMake(0, 0, 12, 12))
        v.image = UIImage(named: "trianglePink")
        v.contentMode = .scaleAspectFit
        v.isHidden = true
        return v
    }()
    
    lazy var hcg: UIImageView = {
        let v = UIImageView(frame: CGRectMake(0, 0, 12, 12))
        v.image = UIImage(named: "trangleCyan")
        v.contentMode = .scaleAspectFit
        v.isHidden = true
        return v
    }()
    
    override func awakeFromNib() {
        super.awakeFromNib()
        prepareUI()
        
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        prepareUI()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        circleMarkView.isHidden = true
        heartPinkView.isHidden = true
        lh_uitra.isHidden = true
        fsh.isHidden = true
        pdg.isHidden = true
        lhTestDay.isHidden = true
        hcg.isHidden = true
        angleGroupView.isHidden = true
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func prepareUI() {
        self.addSubview(selectedView)
        self.addSubview(circleMarkView)
        //self.addSubview(heartPinkView)
        self.addSubview(angleGroupView)
        self.addSubview(dayLabel)
        NSLayoutConstraint.activate([
            selectedView.heightAnchor.constraint(equalToConstant: selectedViewSize.height),
            selectedView.widthAnchor.constraint(equalToConstant: selectedViewSize.width),
            selectedView.centerYAnchor.constraint(equalTo: self.centerYAnchor, constant: -4),
            selectedView.centerXAnchor.constraint(equalTo: self.centerXAnchor),
            
            circleMarkView.heightAnchor.constraint(equalToConstant: selectedViewSize.height),
            circleMarkView.widthAnchor.constraint(equalToConstant: selectedViewSize.height),
            circleMarkView.centerYAnchor.constraint(equalTo: self.centerYAnchor, constant: -4),
            circleMarkView.centerXAnchor.constraint(equalTo: self.centerXAnchor),
            
//            heartPinkView.heightAnchor.constraint(equalToConstant: selectedViewSize.height / 2),
//            heartPinkView.widthAnchor.constraint(equalToConstant: selectedViewSize.height / 2),
//            heartPinkView.topAnchor.constraint(equalTo: circleMarkView.bottomAnchor, constant: 0),
//            heartPinkView.centerXAnchor.constraint(equalTo: self.centerXAnchor),
            
            angleGroupView.heightAnchor.constraint(equalToConstant: selectedViewSize.height / 2),
            angleGroupView.widthAnchor.constraint(greaterThanOrEqualToConstant: selectedViewSize.height / 2),
            angleGroupView.topAnchor.constraint(equalTo: circleMarkView.bottomAnchor, constant: 0),
            angleGroupView.centerXAnchor.constraint(equalTo: self.centerXAnchor),
            
            dayLabel.centerYAnchor.constraint(equalTo: self.centerYAnchor, constant: -4),
            dayLabel.centerXAnchor.constraint(equalTo: self.centerXAnchor),
        ])
    }
    
    func setupMarkLabel(dateTypies: [CalendarLabel] = []) {
        circleMarkView.isHidden = true
        heartPinkView.isHidden = true
        lh_uitra.isHidden = true
        fsh.isHidden = true
        pdg.isHidden = true
        lhTestDay.isHidden = true
        hcg.isHidden = true
        angleGroupView.isHidden = true
        
        dateTypies.forEach { type in
            switch type {
            case .period, .fertile, .ovulation, .exp_period, .exp_fertile, .exp_ovulation:
                circleMarkView.isHidden = false
                circleMarkView.image = UIImage(named: type.imageName)
            case .sex:
//                heartPinkView.isHidden = false
//                heartPinkView.image = UIImage(named: type.imageName)
                
                angleGroupView.addArrangedSubview(heartPinkView)
                heartPinkView.isHidden = false
                angleGroupView.isHidden = false
            case .lh_uitra:
//                angleMarkView.isHidden = true
//                angleMarkView.image = UIImage(named: type.imageName)
                
                angleGroupView.addArrangedSubview(lh_uitra)
                lh_uitra.isHidden = false
                angleGroupView.isHidden = false
            case .fsh:
                angleGroupView.addArrangedSubview(fsh)
                fsh.isHidden = false
                angleGroupView.isHidden = false
            case .pdg:
                angleGroupView.addArrangedSubview(pdg)
                pdg.isHidden = false
                angleGroupView.isHidden = false
            case .lh_testDay:
                angleGroupView.addArrangedSubview(lhTestDay)
                lhTestDay.isHidden = false
                angleGroupView.isHidden = false
            case .hcg_testDay:
                angleGroupView.addArrangedSubview(hcg)
                hcg.isHidden = false
                angleGroupView.isHidden = false
            }
        }
    }
}
