//
//  GoalSettingViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/29.
//

import UIKit

class GoalSettingViewController: BaseViewController, CreateAccountPopupViewControllerDelegate {

    @IBOutlet weak var conceptionCell: UIView!
    @IBOutlet weak var cycleTrackingCell: UIView!
    
    @IBOutlet weak var conceptionLabel: UILabel!
    @IBOutlet weak var cycleLabel: UILabel!
    
    var role: RoleType = .getPregnant
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .themeColor
        title = "Basic Info"
        checkLoginAsGuest()
        setActions()
        setupCopy()
    }
    
    private func setupCopy() {
        if UserDefaults.standard.isFristTimeChooseGoal {
            conceptionLabel.text = "Try to Conceive"
            UserDefaults.standard.isFristTimeChooseGoal = false
        } else {
            conceptionLabel.text = "Conception"
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = false
    }
    
    private func checkLoginAsGuest() {
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount else {
            return
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [weak self] in
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self?.navigationController?.present(vc, animated: true)
        }
    }
    
    func didTapCreateAccount() {
        let signUpVC = SignUpViewController()
        navigationController?.pushViewController(signUpVC, animated: true)
    }
    
    func didTapClosePopup() {
        //setupGuidelinePage()
    }
    
    private func setActions() {
        let tapToConception = UITapGestureRecognizer(target: self, action: #selector(didTapConceptionCell))
        conceptionCell.addGestureRecognizer(tapToConception)
        
        let tapToCycleTracking = UITapGestureRecognizer(target: self, action: #selector(didTapCycleTrackingCell))
        cycleTrackingCell.addGestureRecognizer(tapToCycleTracking)
    }
    
    @objc func didTapConceptionCell() {
        role = .getPregnant
        gotoBirthdaySettingVC()
    }
    
    @objc func didTapCycleTrackingCell() {
        role = .cycleTracking
        gotoBirthdaySettingVC()
    }
    
    func gotoBirthdaySettingVC() {
        UserInteractor.userUpdateRole(role) { success in
            if success {
                UserInteractor.getUserInfo { info in
                    guard let userInfo = info else { return }
                    
                    let user = LoginUser(deviceId: UserDefaults.standard.deviceID, token: UserDefaults.standard.userToken, userInfo: userInfo)
                    Interface.shared().loggedInUser = user
                    
                    if UserDefaults.standard.isLoginAsGuestWithoutAccount == true {
                        let birthdayVC = BirthdaySettingViewController(self.role)
                        self.hidesBottomBarWhenPushed = true
                        self.navigationController?.pushViewController(birthdayVC, animated: true)
                        self.hidesBottomBarWhenPushed = true
                    } else {
                        guard let user = Interface.shared().loggedInUser?.userInfo, let trackMethod = user.userBusinessConfigVO.trackMethod, trackMethod.count > 0 else {
                            let birthdayVC = BirthdaySettingViewController(self.role)
                            self.navigationController?.pushViewController(birthdayVC, animated: true)
                            return
                        }
                        currentWindow?.rootViewController = TabBarController()
                        
                    }
                }
                
            }
        }
    }
}
