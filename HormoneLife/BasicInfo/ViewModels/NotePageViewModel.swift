//
//  NotePageViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/5.
//

import UIKit
import HandyJSON

class NotePageViewModel: BaseViewModel {

    var dataSource = [NotePageListModel]()
    var notePageModel: NotePageModel?
    
    func getNotesPage(refresh: Bool, userId: String?, imageUrls: String?, markTime: String? = nil, success: @escaping (_ success: NotePageModel?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        if refresh == true {
            begingIndex = 1
        } else {
            begingIndex += 1
        }
        var temParams = [String : String]()
        if let userId = userId {
            temParams["userId"] = userId
        }
        if let imageUrls = imageUrls {
            temParams["imageUrls"] = imageUrls
        }
        if let markTime = markTime {
            temParams["markTime"] = markTime
        }
        
        var params = ["page": self.begingIndex,
                      "pageSize": self.pageSize,
                      "param": temParams] as [String : Any]
        
        
        NoteInteractor.getUserNotesList(params: params) { result in
            let dataList = (result?.list ?? [NotePageListModel]()).sorted {
                $0.markTime ?? "0" >= $1.markTime ?? "0"
            }
            
            if refresh == true {
                self.dataSource.removeAll()
            }
            if (dataList.count) < self.pageSize {
                self.hasMoreData = false
            } else {
                self.hasMoreData = true
            }
            if refresh == true {
                self.dataSource = dataList//result?.list ?? [NotePageListModel]()
            } else {
                self.dataSource.append(contentsOf: dataList)//result?.list ?? [NotePageListModel]())
            }
            success(result)
        } failure: { error in
            if self.begingIndex > 1 {
                self.begingIndex -= 1
            }
            failure(error)
        }
    }
}

struct NotePageModel : HandyJSON {
    var list: [NotePageListModel]?
    var page = 1
    var pageSize = 10
    var totalCount = 0
    var totalPage = 0
}

struct NotePageListModel : HandyJSON {
    var content: String?
    var id: String?
    var imageUrls: String?
    var days : String?
    var markTime: String?
    var medicationHistory: String?
    
    var userId: String?
    var userName: String?
    var vitaminHistory: String?
}

