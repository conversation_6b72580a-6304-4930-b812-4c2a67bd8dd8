//
//  NoteDetailViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/7.
//

import UIKit
import HandyJSON

class NoteDetailViewModel: BaseViewModel {

    var noteDetailModel: NotePageListModel?
    
    var dataSource = [NoteDetailModel]()
    
    func handleNoteMessage(model: NotePageListModel) {
        self.dataSource.removeAll()
        var oneM = NoteDetailModel()
        oneM.title = "Say something for the record"
        oneM.content = model.content
        
        var twoM = NoteDetailModel()
        twoM.title = "Medication history"
        twoM.content = model.medicationHistory
        
        var threeM = NoteDetailModel()
        threeM.title = "Vitamin history"
        threeM.content = model.vitaminHistory
        
        var fourM = NoteDetailModel()
        fourM.title = model.markTime
        fourM.content = model.markTime
        fourM.imageUrls = model.imageUrls
        fourM.type = 1
        fourM.days = model.days
        self.dataSource.append(contentsOf: [oneM, twoM, threeM, fourM])
    }
    
    func getNotesDetail(id: String, success: @escaping (_ success: NotePageListModel?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        NoteInteractor.getUserNotesDetail(id: id) { result in
            self.noteDetailModel = result
            
            self.handleNoteMessage(model: result!)
            success(result)
        } failure: { error in
            failure(error)
        }

    }
}

struct NoteDetailModel : HandyJSON {
    var type : Int = 0 //
    var title: String?
    var content: String?
    var imageUrls : String?
    var days: String?
}
