//
//  AddNoteViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/5.
//

import UIKit

class AddNoteViewModel: BaseViewModel {
    func addNote(content: String?, imageUrls: String?, markTime: String?, medicationHistory: String?, vitaminHistory: String?, noteId: String?, success: @escaping (_ success: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        var params = [String : String]()
        if let content = content {
            params["content"] = content
        }
        if let imageUrls = imageUrls {
            params["imageUrls"] = imageUrls
        }
        if let markTime = markTime {
            params["markTime"] = markTime
        }
       
        if let medicationHistory = medicationHistory {
            params["medicationHistory"] = medicationHistory
        }
        if let vitaminHistory = vitaminHistory {
            params["vitaminHistory"] = vitaminHistory
        }
        
        if let noteId = noteId {
            params["id"] = noteId
            if let userid = Interface.shared().loggedInUser?.userInfo.id {
                params["userId"] = userid
            }
            NoteInteractor.updateNote(params: params) { result in
                success(result)
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: "user_note_update_success"), object: nil, userInfo: nil)
            } failure: { error in
                failure(error)
            }
        } else {
            NoteInteractor.addNote(params: params) { result in
                success(result)
            } failure: { error in
                failure(error)
            }
        }
    }
}
