//
//  SystemTagViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/5.
//

import UIKit
import HandyJSON

class SystemTagViewModel: BaseViewModel {
    
    var dataSource = [SystemTagsModel]()
    
    var selectDataSource = [String]()
    var unselectDataSource = [String]()
    
    func getNotesPage(success: @escaping (_ success: [SystemTagsModel]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        SystemInteractor.getSystemTags(params: nil) { result in
            self.dataSource = result
            
            self.handleSelectDatas()
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    func addUserTags(name: String, createUserId: String, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        SystemInteractor.addUserTags(params: ["name": name, "createUserId": createUserId]) { result in
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    func selectTags(tagIdList: [String], success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        SystemInteractor.saveUserTags(params: ["tagIdList": tagIdList]) { result in
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    func handleSelectDatas() {
        self.selectDataSource.removeAll()
        self.unselectDataSource.removeAll()
        for item in self.dataSource {
            if item.selected {
                self.selectDataSource.append("\(item.name ?? ""),\(item.tagId ?? "")")
            } else {
                self.unselectDataSource.append("\(item.name ?? ""),\(item.tagId ?? "")")
            }
        }
    }
}

class SystemTagsModel : HandyJSON {
    var createBy: String?
    var createTime: String?
    
    var id: String?
    var isDefault: String?
    var name: String?
    var selected : Bool = false
    var tagId: String?
    
    required init() {
        
    }
}
