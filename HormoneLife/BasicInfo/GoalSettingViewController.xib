<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
        <array key="gilroy regular.otf">
            <string><PERSON><PERSON>-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="GoalSettingViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="conceptionCell" destination="FwP-TF-U2v" id="Gzg-ep-lyo"/>
                <outlet property="conceptionLabel" destination="TSe-l9-MFA" id="VTs-fn-IwH"/>
                <outlet property="cycleLabel" destination="9ab-2S-ip7" id="69d-qv-vog"/>
                <outlet property="cycleTrackingCell" destination="7f5-gW-jOk" id="4fi-3J-Bf5"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="FyU-q1-iBQ">
                    <rect key="frame" x="20" y="79" width="353" height="312"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Which goal below fits you?" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lHL-EG-KCo">
                            <rect key="frame" x="98.333333333333329" y="0.0" width="156.33333333333337" height="24"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="24" id="GEC-UF-tMO"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="13"/>
                            <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FwP-TF-U2v">
                            <rect key="frame" x="0.0" y="44" width="353" height="124"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="aq4-h9-gJH">
                                    <rect key="frame" x="20" y="28" width="313" height="68"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="o8s-by-Z6a">
                                            <rect key="frame" x="0.0" y="8.6666666666666536" width="125" height="50.666666666666657"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Conception" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TSe-l9-MFA">
                                                    <rect key="frame" x="0.0" y="0.0" width="125" height="23.333333333333332"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="20"/>
                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="I want to have a baby" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LQL-Ys-kag">
                                                    <rect key="frame" x="0.0" y="35.333333333333343" width="125" height="15.333333333333336"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="13"/>
                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                        </stackView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kzO-S0-GG9">
                                            <rect key="frame" x="245" y="0.0" width="68" height="68"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="goal4" translatesAutoresizingMaskIntoConstraints="NO" id="rsy-HC-bxF">
                                                    <rect key="frame" x="0.0" y="0.0" width="68" height="68"/>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="goal3" translatesAutoresizingMaskIntoConstraints="NO" id="udL-ze-RlC">
                                                    <rect key="frame" x="4" y="4" width="60" height="60"/>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="goal2" translatesAutoresizingMaskIntoConstraints="NO" id="NsE-C3-hvq">
                                                    <rect key="frame" x="9" y="9" width="50" height="50"/>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="goal1" translatesAutoresizingMaskIntoConstraints="NO" id="gIM-a6-f86">
                                                    <rect key="frame" x="18" y="18" width="32" height="32"/>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="NsE-C3-hvq" firstAttribute="leading" secondItem="kzO-S0-GG9" secondAttribute="leading" constant="9" id="1bu-5Y-IaJ"/>
                                                <constraint firstItem="udL-ze-RlC" firstAttribute="top" secondItem="kzO-S0-GG9" secondAttribute="top" constant="4" id="25c-Ya-kA4"/>
                                                <constraint firstAttribute="trailing" secondItem="gIM-a6-f86" secondAttribute="trailing" constant="18" id="2yO-7X-Lc1"/>
                                                <constraint firstAttribute="bottom" secondItem="gIM-a6-f86" secondAttribute="bottom" constant="18" id="3p4-YQ-wju"/>
                                                <constraint firstItem="rsy-HC-bxF" firstAttribute="leading" secondItem="kzO-S0-GG9" secondAttribute="leading" id="CfY-Ug-nBt"/>
                                                <constraint firstAttribute="trailing" secondItem="rsy-HC-bxF" secondAttribute="trailing" id="Iig-iA-v1N"/>
                                                <constraint firstAttribute="bottom" secondItem="NsE-C3-hvq" secondAttribute="bottom" constant="9" id="Kv6-jr-Qca"/>
                                                <constraint firstItem="gIM-a6-f86" firstAttribute="leading" secondItem="kzO-S0-GG9" secondAttribute="leading" constant="18" id="Mgx-4q-Zfz"/>
                                                <constraint firstItem="gIM-a6-f86" firstAttribute="top" secondItem="kzO-S0-GG9" secondAttribute="top" constant="18" id="PIu-c4-ghy"/>
                                                <constraint firstItem="udL-ze-RlC" firstAttribute="leading" secondItem="kzO-S0-GG9" secondAttribute="leading" constant="4" id="QDd-bc-McQ"/>
                                                <constraint firstItem="NsE-C3-hvq" firstAttribute="top" secondItem="kzO-S0-GG9" secondAttribute="top" constant="9" id="R3d-Bh-CeE"/>
                                                <constraint firstAttribute="trailing" secondItem="NsE-C3-hvq" secondAttribute="trailing" constant="9" id="YPH-I6-kH9"/>
                                                <constraint firstAttribute="bottom" secondItem="udL-ze-RlC" secondAttribute="bottom" constant="4" id="b7y-0a-tvH"/>
                                                <constraint firstAttribute="bottom" secondItem="rsy-HC-bxF" secondAttribute="bottom" id="fVb-cE-SWS"/>
                                                <constraint firstItem="rsy-HC-bxF" firstAttribute="top" secondItem="kzO-S0-GG9" secondAttribute="top" id="jrX-b9-4QN"/>
                                                <constraint firstAttribute="width" constant="68" id="s9u-CB-kf8"/>
                                                <constraint firstAttribute="trailing" secondItem="udL-ze-RlC" secondAttribute="trailing" constant="4" id="uea-Yj-Yj9"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="aq4-h9-gJH" firstAttribute="leading" secondItem="FwP-TF-U2v" secondAttribute="leading" constant="20" id="4R1-dU-wfw"/>
                                <constraint firstAttribute="height" constant="124" id="4tk-oL-FEv"/>
                                <constraint firstItem="aq4-h9-gJH" firstAttribute="top" secondItem="FwP-TF-U2v" secondAttribute="top" constant="28" id="5XQ-Ri-HjC"/>
                                <constraint firstAttribute="trailing" secondItem="aq4-h9-gJH" secondAttribute="trailing" constant="20" id="Tt1-qd-9ig"/>
                                <constraint firstAttribute="bottom" secondItem="aq4-h9-gJH" secondAttribute="bottom" constant="28" id="xAc-aj-byg"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7f5-gW-jOk">
                            <rect key="frame" x="0.0" y="188" width="353" height="124"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="viS-DY-eSo">
                                    <rect key="frame" x="20" y="28" width="313" height="68"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="OzF-So-0Bg">
                                            <rect key="frame" x="0.0" y="8.6666666666666821" width="210" height="50.666666666666657"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Cycle Tracking" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9ab-2S-ip7">
                                                    <rect key="frame" x="0.0" y="0.0" width="210" height="23.333333333333332"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="20"/>
                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="I want to know more about my body" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="976-Fr-tZC">
                                                    <rect key="frame" x="0.0" y="35.333333333333314" width="210" height="15.333333333333336"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="13"/>
                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                        </stackView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="X2m-z1-VaP">
                                            <rect key="frame" x="245" y="0.0" width="68" height="68"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="hua4" translatesAutoresizingMaskIntoConstraints="NO" id="7OZ-vq-5hf">
                                                    <rect key="frame" x="0.0" y="0.0" width="68" height="68"/>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="hua3" translatesAutoresizingMaskIntoConstraints="NO" id="TlH-9b-GEn">
                                                    <rect key="frame" x="4" y="4" width="60" height="60"/>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="hua2" translatesAutoresizingMaskIntoConstraints="NO" id="5SC-xw-fzq">
                                                    <rect key="frame" x="9" y="9" width="50" height="50"/>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="hua1" translatesAutoresizingMaskIntoConstraints="NO" id="aeA-zA-dm8">
                                                    <rect key="frame" x="18" y="18" width="32" height="32"/>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="aeA-zA-dm8" firstAttribute="leading" secondItem="X2m-z1-VaP" secondAttribute="leading" constant="18" id="1TF-LX-NJu"/>
                                                <constraint firstAttribute="width" constant="68" id="2HL-os-fZF"/>
                                                <constraint firstAttribute="trailing" secondItem="5SC-xw-fzq" secondAttribute="trailing" constant="9" id="6Z1-Ol-XdY"/>
                                                <constraint firstAttribute="trailing" secondItem="TlH-9b-GEn" secondAttribute="trailing" constant="4" id="9Pr-KT-qhq"/>
                                                <constraint firstItem="TlH-9b-GEn" firstAttribute="leading" secondItem="X2m-z1-VaP" secondAttribute="leading" constant="4" id="BKz-Br-p4e"/>
                                                <constraint firstItem="5SC-xw-fzq" firstAttribute="top" secondItem="X2m-z1-VaP" secondAttribute="top" constant="9" id="H5j-2u-dkv"/>
                                                <constraint firstAttribute="trailing" secondItem="aeA-zA-dm8" secondAttribute="trailing" constant="18" id="Kde-th-Fjy"/>
                                                <constraint firstAttribute="trailing" secondItem="7OZ-vq-5hf" secondAttribute="trailing" id="Mxn-fQ-Zr6"/>
                                                <constraint firstItem="TlH-9b-GEn" firstAttribute="top" secondItem="X2m-z1-VaP" secondAttribute="top" constant="4" id="eEj-NT-CCP"/>
                                                <constraint firstItem="5SC-xw-fzq" firstAttribute="leading" secondItem="X2m-z1-VaP" secondAttribute="leading" constant="9" id="fkb-tD-MRP"/>
                                                <constraint firstAttribute="bottom" secondItem="aeA-zA-dm8" secondAttribute="bottom" constant="18" id="llI-pE-rZk"/>
                                                <constraint firstAttribute="bottom" secondItem="7OZ-vq-5hf" secondAttribute="bottom" id="nrZ-0i-AHF"/>
                                                <constraint firstItem="aeA-zA-dm8" firstAttribute="top" secondItem="X2m-z1-VaP" secondAttribute="top" constant="18" id="qLD-p5-1K1"/>
                                                <constraint firstItem="7OZ-vq-5hf" firstAttribute="leading" secondItem="X2m-z1-VaP" secondAttribute="leading" id="qqh-mG-4NI"/>
                                                <constraint firstAttribute="bottom" secondItem="5SC-xw-fzq" secondAttribute="bottom" constant="9" id="rSi-eM-7Hg"/>
                                                <constraint firstAttribute="bottom" secondItem="TlH-9b-GEn" secondAttribute="bottom" constant="4" id="xhN-PV-cic"/>
                                                <constraint firstItem="7OZ-vq-5hf" firstAttribute="top" secondItem="X2m-z1-VaP" secondAttribute="top" id="z35-PV-BMM"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="viS-DY-eSo" secondAttribute="trailing" constant="20" id="7Ii-F7-L9u"/>
                                <constraint firstAttribute="bottom" secondItem="viS-DY-eSo" secondAttribute="bottom" constant="28" id="Dt6-q4-PXj"/>
                                <constraint firstItem="viS-DY-eSo" firstAttribute="top" secondItem="7f5-gW-jOk" secondAttribute="top" constant="28" id="bQI-Yb-K4Y"/>
                                <constraint firstItem="viS-DY-eSo" firstAttribute="leading" secondItem="7f5-gW-jOk" secondAttribute="leading" constant="20" id="g1a-i2-AcY"/>
                                <constraint firstAttribute="height" constant="124" id="tYp-TC-pwu"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstItem="7f5-gW-jOk" firstAttribute="leading" secondItem="FyU-q1-iBQ" secondAttribute="leading" id="Mqm-Sn-eEa"/>
                        <constraint firstAttribute="trailing" secondItem="FwP-TF-U2v" secondAttribute="trailing" id="P9i-gb-7Yg"/>
                        <constraint firstAttribute="bottom" secondItem="7f5-gW-jOk" secondAttribute="bottom" id="WlW-tf-Rem"/>
                        <constraint firstItem="FwP-TF-U2v" firstAttribute="leading" secondItem="FyU-q1-iBQ" secondAttribute="leading" id="d6q-xI-Cp5"/>
                        <constraint firstAttribute="trailing" secondItem="7f5-gW-jOk" secondAttribute="trailing" id="jML-Su-prN"/>
                    </constraints>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="FyU-q1-iBQ" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="20" id="Ao7-g9-NQl"/>
                <constraint firstItem="FyU-q1-iBQ" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="20" id="YCO-4l-FYF"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="FyU-q1-iBQ" secondAttribute="trailing" constant="20" id="f8f-1n-s8b"/>
            </constraints>
            <point key="canvasLocation" x="132" y="-11"/>
        </view>
    </objects>
    <resources>
        <image name="goal1" width="32" height="32"/>
        <image name="goal2" width="50" height="50"/>
        <image name="goal3" width="60" height="60"/>
        <image name="goal4" width="68" height="68"/>
        <image name="hua1" width="32" height="32"/>
        <image name="hua2" width="50" height="50"/>
        <image name="hua3" width="60" height="60"/>
        <image name="hua4" width="68" height="68"/>
    </resources>
</document>
