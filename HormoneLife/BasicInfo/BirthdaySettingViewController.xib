<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BirthdaySettingViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="avatar" destination="IwT-0A-3Y3" id="4T4-av-5KX"/>
                <outlet property="birthdayInputField" destination="Ni4-d1-DLF" id="JfK-mD-tPO"/>
                <outlet property="birthdaySelectView" destination="sRg-60-OLi" id="bV7-aS-5K6"/>
                <outlet property="menstrualCycleField" destination="2b9-K2-33L" id="qLh-v5-hXv"/>
                <outlet property="ovulationStackView" destination="nQG-PM-2j6" id="Bft-Ai-kvA"/>
                <outlet property="periodStartDateField" destination="Dkm-DU-c7m" id="nhp-Uo-WGd"/>
                <outlet property="periodStartDateView" destination="oUt-zJ-7td" id="E9B-lK-2g8"/>
                <outlet property="peroidLengthField" destination="v3g-1f-R7f" id="gr7-D3-Ksb"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" contentInsetAdjustmentBehavior="never" translatesAutoresizingMaskIntoConstraints="NO" id="RKq-1o-mvS">
                    <rect key="frame" x="0.0" y="59" width="393" height="701"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TJn-aO-lj6">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="1061.3333333333333"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Nlx-sK-Okr">
                                    <rect key="frame" x="24" y="20.000000000000057" width="345" height="1021.3333333333335"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3QB-vf-zy0">
                                            <rect key="frame" x="0.0" y="0.0" width="345" height="116"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="22" translatesAutoresizingMaskIntoConstraints="NO" id="gd3-af-Ldv">
                                                    <rect key="frame" x="10" y="16" width="325" height="84"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="When is your birthday?" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9sc-FE-pX9">
                                                            <rect key="frame" x="0.0" y="0.0" width="325" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="tnY-JK-RKz"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="14"/>
                                                            <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="BAx-25-9Fa">
                                                            <rect key="frame" x="0.0" y="42" width="325" height="42"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="L5R-yB-8Yc">
                                                                    <rect key="frame" x="0.0" y="0.0" width="42" height="42"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="peopleCircle" translatesAutoresizingMaskIntoConstraints="NO" id="IwT-0A-3Y3">
                                                                            <rect key="frame" x="0.0" y="0.0" width="42" height="42"/>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                                    <integer key="value" value="21"/>
                                                                                </userDefinedRuntimeAttribute>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="trailing" secondItem="IwT-0A-3Y3" secondAttribute="trailing" id="5c8-lN-esr"/>
                                                                        <constraint firstItem="IwT-0A-3Y3" firstAttribute="top" secondItem="L5R-yB-8Yc" secondAttribute="top" id="BEL-7l-3cI"/>
                                                                        <constraint firstAttribute="width" constant="42" id="Og5-93-4Qc"/>
                                                                        <constraint firstAttribute="height" constant="42" id="ZaF-x4-RZj"/>
                                                                        <constraint firstAttribute="bottom" secondItem="IwT-0A-3Y3" secondAttribute="bottom" id="f3a-Z8-nKG"/>
                                                                        <constraint firstItem="IwT-0A-3Y3" firstAttribute="leading" secondItem="L5R-yB-8Yc" secondAttribute="leading" id="zCX-Pb-PLk"/>
                                                                    </constraints>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sRg-60-OLi">
                                                                    <rect key="frame" x="60" y="0.0" width="265" height="42"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="rYf-Nr-ago">
                                                                            <rect key="frame" x="16" y="8" width="233" height="26"/>
                                                                            <subviews>
                                                                                <textField opaque="NO" tag="100" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Select your birthday" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Ni4-d1-DLF">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="207" height="26"/>
                                                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <textInputTraits key="textInputTraits"/>
                                                                                </textField>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="calendarIcon" translatesAutoresizingMaskIntoConstraints="NO" id="bqj-2H-NdT">
                                                                                    <rect key="frame" x="211" y="0.0" width="22" height="26"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="22" id="m5Q-ed-85B"/>
                                                                                        <constraint firstAttribute="height" constant="26" id="yJS-lv-3Wj"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstItem="rYf-Nr-ago" firstAttribute="leading" secondItem="sRg-60-OLi" secondAttribute="leading" constant="16" id="77B-HX-ohl"/>
                                                                        <constraint firstAttribute="bottom" secondItem="rYf-Nr-ago" secondAttribute="bottom" constant="8" id="BhO-5e-tDi"/>
                                                                        <constraint firstItem="rYf-Nr-ago" firstAttribute="top" secondItem="sRg-60-OLi" secondAttribute="top" constant="8" id="TzX-A9-koN"/>
                                                                        <constraint firstAttribute="trailing" secondItem="rYf-Nr-ago" secondAttribute="trailing" constant="16" id="nZU-tu-hcU"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="4"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="116" id="7Lq-sj-Wo6"/>
                                                <constraint firstItem="gd3-af-Ldv" firstAttribute="top" secondItem="3QB-vf-zy0" secondAttribute="top" constant="16" id="JWD-Bd-EPm"/>
                                                <constraint firstAttribute="trailing" secondItem="gd3-af-Ldv" secondAttribute="trailing" constant="10" id="P6Y-uu-njl"/>
                                                <constraint firstAttribute="bottom" secondItem="gd3-af-Ldv" secondAttribute="bottom" constant="16" id="oqD-1g-Qem"/>
                                                <constraint firstItem="gd3-af-Ldv" firstAttribute="leading" secondItem="3QB-vf-zy0" secondAttribute="leading" constant="10" id="sZm-uy-64C"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WzY-SR-b8S">
                                            <rect key="frame" x="0.0" y="135.99999999999997" width="345" height="465.33333333333326"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="w0o-8o-muy">
                                                    <rect key="frame" x="24" y="20" width="297" height="425.33333333333331"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HgH-5C-jFL">
                                                            <rect key="frame" x="0.0" y="0.0" width="297" height="75.333333333333329"/>
                                                            <attributedString key="attributedText">
                                                                <fragment content="Which of the following methods have you used to track your ovulation? (Multiple choice)">
                                                                    <attributes>
                                                                        <color key="NSColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" metaFont="system" size="14"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U8M-ks-3Av">
                                                            <rect key="frame" x="0.0" y="95.333333333333314" width="297" height="330"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="nQG-PM-2j6">
                                                                    <rect key="frame" x="0.0" y="0.0" width="297" height="330"/>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="nQG-PM-2j6" secondAttribute="trailing" id="cHz-Er-AAU"/>
                                                                <constraint firstItem="nQG-PM-2j6" firstAttribute="leading" secondItem="U8M-ks-3Av" secondAttribute="leading" id="pFL-3f-AVk"/>
                                                                <constraint firstAttribute="bottom" secondItem="nQG-PM-2j6" secondAttribute="bottom" id="whv-at-pKD"/>
                                                                <constraint firstItem="nQG-PM-2j6" firstAttribute="top" secondItem="U8M-ks-3Av" secondAttribute="top" id="y9w-r0-9sF"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="w0o-8o-muy" firstAttribute="top" secondItem="WzY-SR-b8S" secondAttribute="top" constant="20" id="3R7-K5-f2E"/>
                                                <constraint firstAttribute="trailing" secondItem="w0o-8o-muy" secondAttribute="trailing" constant="24" id="3k2-R9-ZDn"/>
                                                <constraint firstAttribute="bottom" secondItem="w0o-8o-muy" secondAttribute="bottom" constant="20" id="mZ8-FZ-kME"/>
                                                <constraint firstItem="w0o-8o-muy" firstAttribute="leading" secondItem="WzY-SR-b8S" secondAttribute="leading" constant="24" id="ydl-Lh-eIq"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6ky-MH-Mb9">
                                            <rect key="frame" x="0.0" y="621.33333333333337" width="345" height="400"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="yeB-Ma-bKB">
                                                    <rect key="frame" x="14" y="20" width="317" height="360"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="3Hp-fw-PMk">
                                                            <rect key="frame" x="0.0" y="0.0" width="317" height="173.33333333333334"/>
                                                            <subviews>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4DZ-AP-A6T" userLabel="When is the start date of your period? lt's okay to guess">
                                                                    <rect key="frame" x="0.0" y="0.0" width="317" height="125.33333333333333"/>
                                                                    <attributedString key="attributedText">
                                                                        <fragment content="When did your last period start? It's okay to guess.">
                                                                            <attributes>
                                                                                <color key="NSColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                <font key="NSFont" metaFont="system" size="14"/>
                                                                                <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                                                            </attributes>
                                                                        </fragment>
                                                                    </attributedString>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oUt-zJ-7td">
                                                                    <rect key="frame" x="0.0" y="137.33333333333326" width="317" height="36"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="mLH-WI-5eE">
                                                                            <rect key="frame" x="12" y="0.0" width="289" height="36"/>
                                                                            <subviews>
                                                                                <textField opaque="NO" tag="101" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Select your period start date" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Dkm-DU-c7m">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="269" height="36"/>
                                                                                    <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <textInputTraits key="textInputTraits"/>
                                                                                </textField>
                                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4Nc-qf-oFi">
                                                                                    <rect key="frame" x="273" y="0.0" width="16" height="36"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" image="calendarIcon"/>
                                                                                </button>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstItem="mLH-WI-5eE" firstAttribute="top" secondItem="oUt-zJ-7td" secondAttribute="top" id="EjU-OU-od5"/>
                                                                        <constraint firstAttribute="bottom" secondItem="mLH-WI-5eE" secondAttribute="bottom" id="JUk-i0-9ep"/>
                                                                        <constraint firstAttribute="trailing" secondItem="mLH-WI-5eE" secondAttribute="trailing" constant="16" id="M5t-zJ-lfk"/>
                                                                        <constraint firstItem="mLH-WI-5eE" firstAttribute="leading" secondItem="oUt-zJ-7td" secondAttribute="leading" constant="12" id="jVI-aH-3E4"/>
                                                                        <constraint firstAttribute="height" constant="36" id="z3q-hL-Oq1"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="4"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="qfU-fr-jPb">
                                                            <rect key="frame" x="0.0" y="193.33333333333326" width="317" height="73.333333333333314"/>
                                                            <subviews>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3PW-cf-w4M">
                                                                    <rect key="frame" x="0.0" y="0.0" width="317" height="25.333333333333332"/>
                                                                    <attributedString key="attributedText">
                                                                        <fragment content="How many days did it last?(Period length)">
                                                                            <attributes>
                                                                                <color key="NSColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                <font key="NSFont" metaFont="system" size="14"/>
                                                                                <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                                                            </attributes>
                                                                        </fragment>
                                                                    </attributedString>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tNI-Ew-tY4">
                                                                    <rect key="frame" x="0.0" y="37.333333333333371" width="317" height="36"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nhR-xg-OG2">
                                                                            <rect key="frame" x="12" y="0.0" width="289" height="36"/>
                                                                            <subviews>
                                                                                <textField opaque="NO" tag="102" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Period Length" textAlignment="natural" clearsOnBeginEditing="YES" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="v3g-1f-R7f">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="273" height="36"/>
                                                                                    <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <textInputTraits key="textInputTraits"/>
                                                                                </textField>
                                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jbc-LK-QaB">
                                                                                    <rect key="frame" x="273" y="0.0" width="16" height="36"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" image="periodIcon"/>
                                                                                    <connections>
                                                                                        <action selector="didTapPeriodLengthIcon:" destination="-1" eventType="touchUpInside" id="E75-3c-sO5"/>
                                                                                    </connections>
                                                                                </button>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstItem="nhR-xg-OG2" firstAttribute="leading" secondItem="tNI-Ew-tY4" secondAttribute="leading" constant="12" id="02v-0h-wpf"/>
                                                                        <constraint firstAttribute="trailing" secondItem="nhR-xg-OG2" secondAttribute="trailing" constant="16" id="9PE-Rf-FWm"/>
                                                                        <constraint firstAttribute="height" constant="36" id="C9E-DR-axq"/>
                                                                        <constraint firstAttribute="bottom" secondItem="nhR-xg-OG2" secondAttribute="bottom" id="FQh-G4-for"/>
                                                                        <constraint firstItem="nhR-xg-OG2" firstAttribute="top" secondItem="tNI-Ew-tY4" secondAttribute="top" id="UYZ-sg-LnZ"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="4"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Nk4-Qg-5XL">
                                                            <rect key="frame" x="0.0" y="286.66666666666663" width="317" height="73.333333333333314"/>
                                                            <subviews>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BQc-ZP-Wow">
                                                                    <rect key="frame" x="0.0" y="0.0" width="317" height="25.333333333333332"/>
                                                                    <attributedString key="attributedText">
                                                                        <fragment content="Aaverage length of your cycle.(Days)">
                                                                            <attributes>
                                                                                <color key="NSColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                <font key="NSFont" metaFont="system" size="14"/>
                                                                                <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                                                            </attributes>
                                                                        </fragment>
                                                                    </attributedString>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bxN-8W-51y">
                                                                    <rect key="frame" x="0.0" y="37.333333333333258" width="317" height="36"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Q2K-v8-zcK">
                                                                            <rect key="frame" x="12" y="0.0" width="289" height="36"/>
                                                                            <subviews>
                                                                                <textField opaque="NO" tag="103" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Cycle Length" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="2b9-K2-33L">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="269" height="36"/>
                                                                                    <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <textInputTraits key="textInputTraits"/>
                                                                                </textField>
                                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Lfb-5F-lEO">
                                                                                    <rect key="frame" x="273" y="0.0" width="16" height="36"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" image="periodIcon"/>
                                                                                    <connections>
                                                                                        <action selector="didTapMenstrualCycleIcon:" destination="-1" eventType="touchUpInside" id="pIx-AI-HHS"/>
                                                                                    </connections>
                                                                                </button>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstItem="Q2K-v8-zcK" firstAttribute="top" secondItem="bxN-8W-51y" secondAttribute="top" id="94k-Rb-61a"/>
                                                                        <constraint firstAttribute="bottom" secondItem="Q2K-v8-zcK" secondAttribute="bottom" id="9JX-zu-afI"/>
                                                                        <constraint firstItem="Q2K-v8-zcK" firstAttribute="leading" secondItem="bxN-8W-51y" secondAttribute="leading" constant="12" id="FTg-LI-zge"/>
                                                                        <constraint firstAttribute="trailing" secondItem="Q2K-v8-zcK" secondAttribute="trailing" constant="16" id="MV1-Rb-0Xo"/>
                                                                        <constraint firstAttribute="height" constant="36" id="woi-QG-nbp"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="4"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="Nk4-Qg-5XL" secondAttribute="bottom" constant="5.6843418860808015e-14" id="Afg-42-jEF"/>
                                                        <constraint firstAttribute="bottom" secondItem="Nk4-Qg-5XL" secondAttribute="bottom" id="ci8-bS-t6g"/>
                                                    </constraints>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="yeB-Ma-bKB" secondAttribute="trailing" constant="14" id="0qq-ce-PMe"/>
                                                <constraint firstItem="yeB-Ma-bKB" firstAttribute="leading" secondItem="6ky-MH-Mb9" secondAttribute="leading" constant="14" id="7XT-0M-eaE"/>
                                                <constraint firstItem="yeB-Ma-bKB" firstAttribute="top" secondItem="6ky-MH-Mb9" secondAttribute="top" constant="20" id="Say-id-4wV"/>
                                                <constraint firstAttribute="bottom" secondItem="yeB-Ma-bKB" secondAttribute="bottom" constant="20" id="hCF-1r-fmJ"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstItem="Nlx-sK-Okr" firstAttribute="leading" secondItem="TJn-aO-lj6" secondAttribute="leading" constant="24" id="Knq-5H-KW7"/>
                                <constraint firstAttribute="bottom" secondItem="Nlx-sK-Okr" secondAttribute="bottom" constant="20" id="Z2v-Is-oo8"/>
                                <constraint firstItem="Nlx-sK-Okr" firstAttribute="top" secondItem="TJn-aO-lj6" secondAttribute="top" constant="20" id="kEG-7E-3VM"/>
                                <constraint firstAttribute="trailing" secondItem="Nlx-sK-Okr" secondAttribute="trailing" constant="24" id="oMu-V9-S8b"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstItem="TJn-aO-lj6" firstAttribute="bottom" secondItem="RKq-1o-mvS" secondAttribute="bottom" id="7Sm-fL-tMY"/>
                        <constraint firstAttribute="bottom" secondItem="TJn-aO-lj6" secondAttribute="bottom" id="GFC-Cx-0zL"/>
                        <constraint firstItem="TJn-aO-lj6" firstAttribute="width" secondItem="RKq-1o-mvS" secondAttribute="width" id="NBf-JE-AlS"/>
                        <constraint firstAttribute="trailing" secondItem="TJn-aO-lj6" secondAttribute="trailing" id="Vof-nn-T7v"/>
                        <constraint firstItem="TJn-aO-lj6" firstAttribute="top" secondItem="RKq-1o-mvS" secondAttribute="top" id="fxz-VU-XsI"/>
                        <constraint firstItem="TJn-aO-lj6" firstAttribute="leading" secondItem="RKq-1o-mvS" secondAttribute="leading" id="r6D-dF-gvt"/>
                    </constraints>
                </scrollView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xky-rP-ziG">
                    <rect key="frame" x="24" y="760" width="345" height="48"/>
                    <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="48" id="v9E-o1-DV3"/>
                    </constraints>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" title="Done"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="doneButtonAction:" destination="-1" eventType="touchUpInside" id="fdA-qi-NJY"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="xky-rP-ziG" firstAttribute="top" secondItem="RKq-1o-mvS" secondAttribute="bottom" id="Ieg-QK-22J"/>
                <constraint firstItem="RKq-1o-mvS" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="KQc-QX-W7f"/>
                <constraint firstItem="RKq-1o-mvS" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="OGE-fg-b21"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="xky-rP-ziG" secondAttribute="trailing" constant="24" id="a4B-Yl-mwr"/>
                <constraint firstItem="xky-rP-ziG" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="24" id="j5I-VE-9gZ"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="xky-rP-ziG" secondAttribute="bottom" constant="10" id="tev-1s-xCR"/>
                <constraint firstItem="RKq-1o-mvS" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="uql-2m-Pvs"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <image name="calendarIcon" width="16" height="16"/>
        <image name="peopleCircle" width="48" height="48"/>
        <image name="periodIcon" width="16" height="16"/>
    </resources>
</document>
