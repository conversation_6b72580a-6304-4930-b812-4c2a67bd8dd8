<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BoundViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="descriptionTitle" destination="2jW-sx-QzI" id="Qok-k2-EdB"/>
                <outlet property="inputTextField" destination="wsI-DZ-mBc" id="Lwl-2X-OOA"/>
                <outlet property="sendButton" destination="nWb-LP-jAN" id="SFK-bC-g26"/>
                <outlet property="submitButton" destination="Lxl-Nm-259" id="yd8-mG-phe"/>
                <outlet property="untieButton" destination="6Fc-4O-5iJ" id="UiY-Bi-ogt"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="30" translatesAutoresizingMaskIntoConstraints="NO" id="VOL-ha-4mT">
                    <rect key="frame" x="30" y="91" width="333" height="295"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2jW-sx-QzI">
                            <rect key="frame" x="0.0" y="0.0" width="333" height="61"/>
                            <string key="text">Please enter your phone number. 
After text verification is complete you may use this number to login.</string>
                            <fontDescription key="fontDescription" name=".AppleSystemUIFont" family=".AppleSystemUIFont" pointSize="17"/>
                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="g1e-ff-fdQ">
                            <rect key="frame" x="0.0" y="91" width="333" height="48"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" translatesAutoresizingMaskIntoConstraints="NO" id="2tk-Ba-nxF">
                                    <rect key="frame" x="20" y="0.0" width="293" height="48"/>
                                    <subviews>
                                        <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Please enter your mobile number" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="wsI-DZ-mBc">
                                            <rect key="frame" x="0.0" y="0.0" width="228.66666666666666" height="48"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="48" id="6Bp-lu-Td2"/>
                                            </constraints>
                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <textInputTraits key="textInputTraits"/>
                                        </textField>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nWb-LP-jAN">
                                            <rect key="frame" x="228.66666666666671" y="0.0" width="64.333333333333343" height="48"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" title="|   Send">
                                                <color key="titleColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                            </state>
                                            <connections>
                                                <action selector="sendButtonDidTap:" destination="-1" eventType="touchUpInside" id="T2K-VJ-ZeS"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="2tk-Ba-nxF" secondAttribute="bottom" id="S0E-GY-4Dj"/>
                                <constraint firstAttribute="height" constant="48" id="fSG-cz-XuH"/>
                                <constraint firstItem="2tk-Ba-nxF" firstAttribute="leading" secondItem="g1e-ff-fdQ" secondAttribute="leading" constant="20" id="pmo-Ug-bPJ"/>
                                <constraint firstAttribute="trailing" secondItem="2tk-Ba-nxF" secondAttribute="trailing" constant="20" id="uVG-fF-reh"/>
                                <constraint firstItem="2tk-Ba-nxF" firstAttribute="top" secondItem="g1e-ff-fdQ" secondAttribute="top" id="uaq-u2-I0r"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Lxl-Nm-259">
                            <rect key="frame" x="0.0" y="169" width="333" height="48"/>
                            <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="48" id="iUS-7e-94z"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Submit"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="submitButtonDidTap:" destination="-1" eventType="touchUpInside" id="Dby-qa-Zwj"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6Fc-4O-5iJ">
                            <rect key="frame" x="0.0" y="247" width="333" height="48"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="48" id="2yV-fM-UsE"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Untie">
                                <color key="titleColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            </state>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="didTapUntieButton:" destination="-1" eventType="touchUpInside" id="8ZM-cU-Qtu"/>
                            </connections>
                        </button>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="6Fc-4O-5iJ" secondAttribute="bottom" id="JL5-mG-dJ1"/>
                    </constraints>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="VOL-ha-4mT" secondAttribute="trailing" constant="30" id="Dod-mj-9jE"/>
                <constraint firstItem="VOL-ha-4mT" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="32" id="Nhw-L8-Agp"/>
                <constraint firstItem="VOL-ha-4mT" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="30" id="dja-VZ-KCD"/>
            </constraints>
            <point key="canvasLocation" x="132" y="-11"/>
        </view>
    </objects>
</document>
