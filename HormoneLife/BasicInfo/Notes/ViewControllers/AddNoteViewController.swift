//
//  AddNoteViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/3.
//

import UIKit
import Photos

protocol AddNoteViewControllerDelegate: AnyObject {
    func didAddNote()
}
class AddNoteViewController: PickPhotoViewController, UITextViewDelegate, PickPhotoDelegate {
    
    let viewModel = AddNoteViewModel()
    
    var noteModel: NotePageListModel?
    var isEditNote = false
    
    @IBOutlet weak var dateSelectLab: UILabel!
    @IBOutlet weak var firstInputView: UITextView!
    @IBOutlet weak var firstInputViewPlaceholder: UIView!
    
    @IBOutlet weak var secondInputView: UITextView!
    @IBOutlet weak var secondInputViewPlaceholder: UIView!
    
    @IBOutlet weak var thirdInputView: UITextView!
    @IBOutlet weak var thirdInputViewPlaceholder: UIView!
    
    @IBOutlet weak var dateButton: UIButton!
    
    @IBOutlet weak var firstPhoto: UIImageView!
    @IBOutlet weak var secondPhoto: UIImageView!
    @IBOutlet weak var thirdPhoto: UIImageView!
    @IBOutlet weak var forthPhoto: UIImageView!
    @IBOutlet weak var fivePhoto: UIImageView!
    @IBOutlet weak var sixPhoto: UIImageView!
    @IBOutlet weak var sevenPhoto: UIImageView!
    @IBOutlet weak var eightPhoto: UIImageView!
    @IBOutlet weak var nightPhoto: UIImageView!
    
    lazy var photoViews = [firstPhoto, secondPhoto, thirdPhoto, forthPhoto, fivePhoto, sixPhoto, sevenPhoto, eightPhoto, nightPhoto]
    
    var addPhotoImg: UIImageView? = nil
    
    var imageUrls = [String : Any]()
    
    weak var delegate: AddNoteViewControllerDelegate?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        pickPhotoDelegate = self
        setNavigationBar()
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapView)))
        
        self.firstInputView.text = self.noteModel?.content
        self.secondInputView.text = self.noteModel?.medicationHistory
        self.thirdInputView.text = self.noteModel?.vitaminHistory
        
        firstInputViewPlaceholder.isHidden =  (self.noteModel?.content?.count ?? 0) > 0
        secondInputViewPlaceholder.isHidden = (self.noteModel?.medicationHistory?.count ?? 0) > 0
        thirdInputViewPlaceholder.isHidden = (self.noteModel?.vitaminHistory?.count ?? 0) > 0
        
        var addTime = Date()
        if isEditNote {
            addTime = self.noteModel?.markTime?.covertDate() ?? Date()
        }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd yyyy"
        let markTime = dateFormatter.string(from: isEditNote ? addTime : Date())
        self.dateSelectLab.text = markTime
        self.dateSelectLab.isUserInteractionEnabled = true
        
        self.dateSelectLab.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(dateLabelAction)))
        
        photoViews.forEach {
            $0?.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapImageButton)))
        }
        
        self.noteModel?.imageUrls?.components(separatedBy: ",").enumerated().forEach { (index, url) in
            if index < photoViews.count {
                photoViews[index]?.sd_setImage(with: URL(string: url))
                photoViews[index]?.isHidden = false
                
                imageUrls["\(photoViews[index]?.tag ?? 1000)"] = url
                
                if let nextBtn = view.viewWithTag(index + 1001) {
                    nextBtn.isHidden = false
                }
            }
        }
    }
    
    private func setNavigationBar() {
        navigationItem.title = "Notes"
        navigationItem.rightBarButtonItem = UIBarButtonItem(image: UIImage(named: "sendIcon"), style: .done, target: self, action: #selector(didTapNavRightButton))
    }
    
    @objc func dateLabelAction() {
        didTapView()
        popCanlendar { date in
            
            let birthday = date.format(with: "MM/dd yyyy")
            self.dateSelectLab.text = birthday
        }
    }
    
    @objc func didTapNavRightButton() {
        didTapView()
        
        showActivityHUD("uploading...")
        
        var urls = [String]()
        let groupQueue = DispatchGroup()
        for photo in self.imageUrls {
            groupQueue.enter()
            if let photoUrl = photo.value as? String {
                urls.append(photoUrl)
                groupQueue.leave()
            } else if let image = photo.value as? UIImage {
                self.uploadViewModel.uploadImages(images: image) {[weak self] (url, fileName)  in
                    groupQueue.leave()//所有回调，包括失败成功都要加
                    urls.append(url)
                } failure: { error in
                    groupQueue.leave()//所有回调，包括失败成功都要加
                }
            }
        }
        
        groupQueue.notify(queue: .main) {
            hideActivityHUD()
            //执行发布
            addNote(with: urls)
        }
        
        func addNote(with imgURLs: [String] = []) {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            var markTime = dateFormatter.string(from: Date())
            
            if let date = self.dateSelectLab.text, date.count > 0 {
                markTime = timeFormat(date: "\(date) \(markTime.components(separatedBy: " ").last ?? "00:00:00")", format: "MM/dd yyyy HH:mm:ss", toFormat: "yyyy-MM-dd HH:mm:ss")
            }
            showActivityHUD("loading...")
            self.viewModel.addNote(content: self.firstInputView.text, imageUrls: imgURLs.joined(separator: ","), markTime: markTime, medicationHistory: self.secondInputView.text, vitaminHistory: self.thirdInputView.text, noteId: self.noteModel?.id) { success in
                hideActivityHUD()
                self.navigationController?.popViewController(animated: true)
                self.delegate?.didAddNote()
            } failure: { error in
                hideActivityHUD()
            }
        }
    }
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    @IBAction func didTapDateButton(_ sender: Any) {
        self.dateLabelAction()
    }
    
    @objc func didTapImageButton(sender: UIGestureRecognizer) {
        didTapView()
        addPhotoImg = sender.view as? UIImageView
        pickPhoto()
    }
    
    func saveImageToPhotosAlbum(_ image: UIImage) {
        PHPhotoLibrary.shared().performChanges({
            let assetChangeRequest = PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { completed, error in
            if completed {
                
            } else {
                
            }
        }
    }
    
    func callBackImage(photo: UIImage) {
        let tag = addPhotoImg?.tag ?? 1000
        self.addPhotoImg?.image = photo
        self.addPhotoImg?.contentMode = .scaleAspectFill
        self.addPhotoImg?.isHidden = false
        
        let nextBtn = view.viewWithTag(tag + 1)
        if let btn = nextBtn {
            btn.isHidden = false
        }
        self.imageUrls["\(tag)"] = photo
        
        /*
        showActivityHUD("uploading...")
        self.uploadViewModel.uploadImages(images: photo) {[weak self] (url, fileName)  in
            print("photo: ", url, fileName, self?.imageUrls)
            self?.imageUrls["\(tag)"] = url
            hideActivityHUD()
        } failure: { error in
            hideActivityHUD()
            showToachMessage(message: "Photo upload time out, please try again later.")
            self.addPhotoImg?.image = UIImage(named: "addPhotoButton")
            self.addPhotoImg?.isHidden = false
            if let btn = nextBtn {
                btn.isHidden = true
            }
            self.view.layoutIfNeeded()
        }
         */
    }
    
    func textViewDidChange(_ textView: UITextView) {
        // textView tag = 200/201/202
        
        switch textView.tag {
        case 200:
            firstInputViewPlaceholder.isHidden = textView.text.count > 0
        case 201:
            secondInputViewPlaceholder.isHidden = textView.text.count > 0
        default:
            thirdInputViewPlaceholder.isHidden = textView.text.count > 0
        }
    }
    
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        if text == "\n" {
            if textView.tag == 200 {
                secondInputView.becomeFirstResponder()
            } else if textView.tag == 201 {
                thirdInputView.becomeFirstResponder()
            } else {
                textView.resignFirstResponder()
            }
        }
        
        return true
    }
}
