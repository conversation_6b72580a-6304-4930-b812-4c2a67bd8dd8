//
//  NoteDetailViewController.swift
//  HormoneLife
//
//  Created by bm on 2024/9/7.
//

import UIKit

class NoteDetailViewController: BaseViewController {
    
    let viewModel = NoteDetailViewModel()
    
    var noteModel: NotePageListModel!
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .themeColor
        tableView.separatorStyle = .none
        
        tableView.register(NotesSectionHeader.self, forHeaderFooterViewReuseIdentifier: "NotesSectionHeader")
        tableView.register(UserNoteCell.classForCoder(), forCellReuseIdentifier: UserNoteCell.description())
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        navigationItem.title = "Notes"
        self.viewModel.noteDetailModel = self.noteModel
        setupUI()
        self.viewModel.handleNoteMessage(model: self.noteModel)
        self.tableView.reloadData()
        self.setNavigationBar()
        NotificationCenter.default.addObserver(self, selector: #selector(requestDetail), name: NSNotification.Name(rawValue: "user_note_update_success"), object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    private func setNavigationBar() {
        
        let imageViewAdd = UIImageView(frame: CGRect(x: 0, y: 0, width: 24, height: 24))
        imageViewAdd.image = UIImage(named: "editPan")
        imageViewAdd.isUserInteractionEnabled = true
        imageViewAdd.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapEdit)))
        
        let stackview = UIStackView(arrangedSubviews: [imageViewAdd])
        stackview.distribution = .fill
        stackview.alignment = .center
        stackview.axis = .horizontal
        stackview.spacing = 22
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: stackview)
    }
    
    @objc func didTapEdit() {
        let addNoteVc = AddNoteViewController(nibName: "AddNoteViewController", bundle: nil)
        addNoteVc.delegate = self
        addNoteVc.noteModel = self.viewModel.noteDetailModel
        addNoteVc.isEditNote = true
        navigationController?.pushViewController(addNoteVc, animated: true)
        
    }
    
    
    @objc func requestDetail() {
        self.viewModel.getNotesDetail(id: self.noteModel.id!) { success in
            
            self.tableView.reloadData()
        } failure: { error in
            
        }
    }
    
    func setupUI() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

extension NoteDetailViewController : UITableViewDelegate, UITableViewDataSource {
    // MARK: - Table view data source
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        // #warning Incomplete implementation, return the number of rows
        return self.viewModel.dataSource.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: UserNoteCell.description()) as! UserNoteCell
        
        cell.refreshDetail(model: self.viewModel.dataSource[indexPath.row])
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return CGFloat.leastNormalMagnitude
    }
}

extension NoteDetailViewController : AddNoteViewControllerDelegate {
    func didAddNote() {
        self.viewModel.getNotesDetail(id: self.noteModel.id!) { success in
            self.tableView.reloadData()
        } failure: { error in
            
        }

    }
    
}
