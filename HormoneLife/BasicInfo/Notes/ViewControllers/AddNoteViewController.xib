<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="AddNoteViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="dateButton" destination="l5I-fT-fel" id="mXW-ZU-vrz"/>
                <outlet property="dateSelectLab" destination="dRg-we-YwS" id="Ktu-Wg-dZ1"/>
                <outlet property="eightPhoto" destination="Zrb-hQ-1kY" id="bF3-0i-jp5"/>
                <outlet property="firstInputView" destination="AkA-t2-GIs" id="TuA-b7-oeR"/>
                <outlet property="firstInputViewPlaceholder" destination="cuC-Gf-eeg" id="kge-FW-Imj"/>
                <outlet property="firstPhoto" destination="O84-ym-Q54" id="BBD-v0-8mU"/>
                <outlet property="fivePhoto" destination="9Uz-ew-h4g" id="WtF-Lp-rbk"/>
                <outlet property="forthPhoto" destination="aXv-qW-3xJ" id="ykG-3U-ahC"/>
                <outlet property="nightPhoto" destination="0tS-HO-p4Q" id="ibd-Fq-tYK"/>
                <outlet property="secondInputView" destination="eZX-FG-3dV" id="vP8-jN-Eod"/>
                <outlet property="secondInputViewPlaceholder" destination="eF3-Cv-dWT" id="0Rz-Jh-LHy"/>
                <outlet property="secondPhoto" destination="Xkj-Fj-aqB" id="u3L-FB-w6Z"/>
                <outlet property="sevenPhoto" destination="Ydc-SJ-cC2" id="6vz-OJ-vQ8"/>
                <outlet property="sixPhoto" destination="ILd-J7-fLq" id="CiF-Yt-5YV"/>
                <outlet property="thirdInputView" destination="lUb-lj-xck" id="sk5-LG-CMq"/>
                <outlet property="thirdInputViewPlaceholder" destination="TE0-uM-RWN" id="Hk1-GA-vx5"/>
                <outlet property="thirdPhoto" destination="Pzg-aI-9ei" id="nVL-hH-6zr"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" contentInsetAdjustmentBehavior="never" translatesAutoresizingMaskIntoConstraints="NO" id="GhA-k9-s9o">
                    <rect key="frame" x="0.0" y="59" width="393" height="759"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rwb-yT-hix" userLabel="contentView">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="875"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Vcj-hk-3Oo">
                                    <rect key="frame" x="20" y="20" width="353" height="825"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KFp-B6-pe1">
                                            <rect key="frame" x="0.0" y="0.0" width="353" height="128"/>
                                            <subviews>
                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" tag="200" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="AkA-t2-GIs">
                                                    <rect key="frame" x="16" y="16" width="321" height="96"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences" returnKeyType="next"/>
                                                    <connections>
                                                        <outlet property="delegate" destination="-1" id="Xdq-nP-bnk"/>
                                                    </connections>
                                                </textView>
                                                <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cuC-Gf-eeg">
                                                    <rect key="frame" x="21" y="23" width="311" height="17"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Say something for the record" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XRe-VB-yGN">
                                                            <rect key="frame" x="0.0" y="0.0" width="185" height="17"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sUk-Kt-mEI">
                                                            <rect key="frame" x="195" y="0.0" width="17" height="17"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="17" id="0Ty-e7-O92"/>
                                                            </constraints>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" image="editPan"/>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="17" id="28b-4v-w9B"/>
                                                        <constraint firstItem="XRe-VB-yGN" firstAttribute="top" secondItem="cuC-Gf-eeg" secondAttribute="top" id="41N-as-DHp"/>
                                                        <constraint firstItem="sUk-Kt-mEI" firstAttribute="leading" secondItem="XRe-VB-yGN" secondAttribute="trailing" constant="10" id="5U5-ee-8GW"/>
                                                        <constraint firstAttribute="bottom" secondItem="sUk-Kt-mEI" secondAttribute="bottom" id="Cmd-br-gUi"/>
                                                        <constraint firstItem="XRe-VB-yGN" firstAttribute="leading" secondItem="cuC-Gf-eeg" secondAttribute="leading" id="Fi8-la-SKJ"/>
                                                        <constraint firstItem="sUk-Kt-mEI" firstAttribute="top" secondItem="cuC-Gf-eeg" secondAttribute="top" id="bFR-By-FvH"/>
                                                        <constraint firstAttribute="bottom" secondItem="XRe-VB-yGN" secondAttribute="bottom" id="ym5-fl-ZiK"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="cuC-Gf-eeg" firstAttribute="leading" secondItem="KFp-B6-pe1" secondAttribute="leading" constant="21" id="H48-VV-U4R"/>
                                                <constraint firstItem="AkA-t2-GIs" firstAttribute="top" secondItem="KFp-B6-pe1" secondAttribute="top" constant="16" id="N7S-2I-fkH"/>
                                                <constraint firstAttribute="height" constant="128" id="Rtu-hq-azb"/>
                                                <constraint firstAttribute="trailing" secondItem="cuC-Gf-eeg" secondAttribute="trailing" constant="21" id="VGW-ae-5DO"/>
                                                <constraint firstAttribute="bottom" secondItem="AkA-t2-GIs" secondAttribute="bottom" constant="16" id="ZyI-Tl-YeJ"/>
                                                <constraint firstItem="cuC-Gf-eeg" firstAttribute="top" secondItem="KFp-B6-pe1" secondAttribute="top" constant="23" id="nkP-fX-ba5"/>
                                                <constraint firstItem="AkA-t2-GIs" firstAttribute="leading" secondItem="KFp-B6-pe1" secondAttribute="leading" constant="16" id="rRa-Ht-va7"/>
                                                <constraint firstAttribute="trailing" secondItem="AkA-t2-GIs" secondAttribute="trailing" constant="16" id="zMb-Mi-yuT"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ERu-L6-bd5">
                                            <rect key="frame" x="0.0" y="144" width="353" height="128"/>
                                            <subviews>
                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" tag="201" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="eZX-FG-3dV">
                                                    <rect key="frame" x="16" y="16" width="321" height="96"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences" returnKeyType="next"/>
                                                    <connections>
                                                        <outlet property="delegate" destination="-1" id="ZyT-Rz-YNT"/>
                                                    </connections>
                                                </textView>
                                                <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eF3-Cv-dWT">
                                                    <rect key="frame" x="21" y="23" width="311" height="17"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Medication history" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vqK-zO-FYc">
                                                            <rect key="frame" x="0.0" y="0.0" width="117.66666666666667" height="17"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1Wu-Vu-8kZ">
                                                            <rect key="frame" x="127.66666666666666" y="0.0" width="17" height="17"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="17" id="O5p-WH-LcT"/>
                                                            </constraints>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" image="editPan"/>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="1Wu-Vu-8kZ" firstAttribute="top" secondItem="eF3-Cv-dWT" secondAttribute="top" id="BfT-f3-Oge"/>
                                                        <constraint firstAttribute="bottom" secondItem="1Wu-Vu-8kZ" secondAttribute="bottom" id="Dxt-t9-m7W"/>
                                                        <constraint firstAttribute="height" constant="17" id="eZt-dK-tDe"/>
                                                        <constraint firstItem="vqK-zO-FYc" firstAttribute="top" secondItem="eF3-Cv-dWT" secondAttribute="top" id="hKG-w2-pm8"/>
                                                        <constraint firstItem="vqK-zO-FYc" firstAttribute="leading" secondItem="eF3-Cv-dWT" secondAttribute="leading" id="ilR-Sg-Zaq"/>
                                                        <constraint firstAttribute="bottom" secondItem="vqK-zO-FYc" secondAttribute="bottom" id="t6h-SU-h7r"/>
                                                        <constraint firstItem="1Wu-Vu-8kZ" firstAttribute="leading" secondItem="vqK-zO-FYc" secondAttribute="trailing" constant="10" id="wiJ-0g-UNr"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="eZX-FG-3dV" secondAttribute="bottom" constant="16" id="EEg-mp-LPJ"/>
                                                <constraint firstItem="eZX-FG-3dV" firstAttribute="top" secondItem="ERu-L6-bd5" secondAttribute="top" constant="16" id="IFN-eC-oeM"/>
                                                <constraint firstAttribute="trailing" secondItem="eZX-FG-3dV" secondAttribute="trailing" constant="16" id="WEF-mE-8Ho"/>
                                                <constraint firstItem="eF3-Cv-dWT" firstAttribute="top" secondItem="ERu-L6-bd5" secondAttribute="top" constant="23" id="aeQ-Oe-Izm"/>
                                                <constraint firstItem="eZX-FG-3dV" firstAttribute="leading" secondItem="ERu-L6-bd5" secondAttribute="leading" constant="16" id="hNX-kc-Rlj"/>
                                                <constraint firstAttribute="height" constant="128" id="ig1-pp-ETr"/>
                                                <constraint firstItem="eF3-Cv-dWT" firstAttribute="leading" secondItem="ERu-L6-bd5" secondAttribute="leading" constant="21" id="uH3-of-b2H"/>
                                                <constraint firstAttribute="trailing" secondItem="eF3-Cv-dWT" secondAttribute="trailing" constant="21" id="zqz-YL-z2I"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lWv-dk-bT2">
                                            <rect key="frame" x="0.0" y="288" width="353" height="128"/>
                                            <subviews>
                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" tag="202" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="lUb-lj-xck">
                                                    <rect key="frame" x="16" y="16" width="321" height="96"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences" returnKeyType="next"/>
                                                    <connections>
                                                        <outlet property="delegate" destination="-1" id="yvm-Yc-5y7"/>
                                                    </connections>
                                                </textView>
                                                <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TE0-uM-RWN">
                                                    <rect key="frame" x="21" y="23" width="311" height="17"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Vitamin history" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rSu-pt-5vz">
                                                            <rect key="frame" x="0.0" y="0.0" width="93" height="17"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9p0-2b-Oss">
                                                            <rect key="frame" x="103" y="0.0" width="17" height="17"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="17" id="GQK-C3-s13"/>
                                                            </constraints>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" image="editPan"/>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="rSu-pt-5vz" firstAttribute="leading" secondItem="TE0-uM-RWN" secondAttribute="leading" id="1S5-Zb-AXc"/>
                                                        <constraint firstItem="rSu-pt-5vz" firstAttribute="top" secondItem="TE0-uM-RWN" secondAttribute="top" id="5Bu-cd-yDP"/>
                                                        <constraint firstItem="9p0-2b-Oss" firstAttribute="leading" secondItem="rSu-pt-5vz" secondAttribute="trailing" constant="10" id="Hb9-xa-pab"/>
                                                        <constraint firstItem="9p0-2b-Oss" firstAttribute="top" secondItem="TE0-uM-RWN" secondAttribute="top" id="XUw-Uh-II2"/>
                                                        <constraint firstAttribute="bottom" secondItem="9p0-2b-Oss" secondAttribute="bottom" id="hA7-ZO-0PF"/>
                                                        <constraint firstAttribute="height" constant="17" id="lnj-RY-uZC"/>
                                                        <constraint firstAttribute="bottom" secondItem="rSu-pt-5vz" secondAttribute="bottom" id="ofg-AB-mSb"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="128" id="0yW-KM-muY"/>
                                                <constraint firstItem="lUb-lj-xck" firstAttribute="top" secondItem="lWv-dk-bT2" secondAttribute="top" constant="16" id="84B-Be-ELG"/>
                                                <constraint firstItem="TE0-uM-RWN" firstAttribute="top" secondItem="lWv-dk-bT2" secondAttribute="top" constant="23" id="FoF-fx-xBE"/>
                                                <constraint firstItem="lUb-lj-xck" firstAttribute="leading" secondItem="lWv-dk-bT2" secondAttribute="leading" constant="16" id="L7R-69-KAM"/>
                                                <constraint firstAttribute="trailing" secondItem="lUb-lj-xck" secondAttribute="trailing" constant="16" id="W7k-yZ-fRs"/>
                                                <constraint firstItem="TE0-uM-RWN" firstAttribute="leading" secondItem="lWv-dk-bT2" secondAttribute="leading" constant="21" id="cEu-5m-X1U"/>
                                                <constraint firstAttribute="trailing" secondItem="TE0-uM-RWN" secondAttribute="trailing" constant="21" id="czj-lF-EZ9"/>
                                                <constraint firstAttribute="bottom" secondItem="lUb-lj-xck" secondAttribute="bottom" constant="16" id="x66-kf-NAC"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wRW-9V-0Br">
                                            <rect key="frame" x="0.0" y="432" width="353" height="393"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="l4q-9j-HJh">
                                                    <rect key="frame" x="16" y="16" width="321" height="361"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="Uln-9y-jD2">
                                                            <rect key="frame" x="0.0" y="0.0" width="321" height="24"/>
                                                            <subviews>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Recording Time" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="f6w-Aj-0EL">
                                                                    <rect key="frame" x="0.0" y="0.0" width="227" height="24"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="02/03 2024" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dRg-we-YwS">
                                                                    <rect key="frame" x="229" y="0.0" width="74" height="24"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="l5I-fT-fel">
                                                                    <rect key="frame" x="305" y="0.0" width="16" height="24"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="16" id="az9-Dc-cr5"/>
                                                                    </constraints>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <state key="normal" image="arowRIght"/>
                                                                    <connections>
                                                                        <action selector="didTapDateButton:" destination="-1" eventType="touchUpInside" id="x8n-0k-24d"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="24" id="TNh-BN-4Yq"/>
                                                            </constraints>
                                                        </stackView>
                                                        <view alpha="0.40000000000000002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eDM-5z-FE0">
                                                            <rect key="frame" x="0.0" y="40" width="321" height="1"/>
                                                            <color key="backgroundColor" red="0.81597435470000002" green="0.79660314320000003" blue="0.76867741349999996" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="1" id="L0l-Kg-5CJ"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hdU-Bi-S6a">
                                                            <rect key="frame" x="0.0" y="57" width="321" height="304"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="14" translatesAutoresizingMaskIntoConstraints="NO" id="T92-Ly-ws1">
                                                                    <rect key="frame" x="0.0" y="0.0" width="321" height="304"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="14" translatesAutoresizingMaskIntoConstraints="NO" id="QJ3-AP-XdI">
                                                                            <rect key="frame" x="0.0" y="0.0" width="321" height="92"/>
                                                                            <subviews>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EHb-Q0-iZ4">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView clipsSubviews="YES" tag="1000" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="O84-ym-Q54">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="bottom" secondItem="O84-ym-Q54" secondAttribute="bottom" id="eUh-b5-C4s"/>
                                                                                        <constraint firstItem="O84-ym-Q54" firstAttribute="top" secondItem="EHb-Q0-iZ4" secondAttribute="top" id="jGi-My-0gt"/>
                                                                                        <constraint firstItem="O84-ym-Q54" firstAttribute="leading" secondItem="EHb-Q0-iZ4" secondAttribute="leading" id="oV6-Dc-4Zq"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="O84-ym-Q54" secondAttribute="trailing" id="trh-Ny-DQo"/>
                                                                                    </constraints>
                                                                                </view>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="I8B-Md-e6P">
                                                                                    <rect key="frame" x="111.66666666666666" y="0.0" width="97.666666666666657" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1001" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="Xkj-Fj-aqB">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstItem="Xkj-Fj-aqB" firstAttribute="leading" secondItem="I8B-Md-e6P" secondAttribute="leading" id="9se-72-JWc"/>
                                                                                        <constraint firstItem="Xkj-Fj-aqB" firstAttribute="top" secondItem="I8B-Md-e6P" secondAttribute="top" id="Rzw-0x-Pig"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="Xkj-Fj-aqB" secondAttribute="bottom" id="ceI-n8-k26"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="Xkj-Fj-aqB" secondAttribute="trailing" id="mcu-Kz-h9f"/>
                                                                                    </constraints>
                                                                                </view>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Kqh-Py-ILC">
                                                                                    <rect key="frame" x="223.33333333333329" y="0.0" width="97.666666666666657" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1002" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="Pzg-aI-9ei">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="bottom" secondItem="Pzg-aI-9ei" secondAttribute="bottom" id="G8x-CB-uaA"/>
                                                                                        <constraint firstItem="Pzg-aI-9ei" firstAttribute="leading" secondItem="Kqh-Py-ILC" secondAttribute="leading" id="N4L-J1-WhE"/>
                                                                                        <constraint firstItem="Pzg-aI-9ei" firstAttribute="top" secondItem="Kqh-Py-ILC" secondAttribute="top" id="V7b-Mz-K8R"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="Pzg-aI-9ei" secondAttribute="trailing" id="yaV-uE-3EX"/>
                                                                                    </constraints>
                                                                                </view>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="92" id="Cna-s8-l1z"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="14" translatesAutoresizingMaskIntoConstraints="NO" id="Sho-g9-B8I">
                                                                            <rect key="frame" x="0.0" y="106" width="321" height="92"/>
                                                                            <subviews>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="r8s-19-J5y">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1003" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="aXv-qW-3xJ">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="trailing" secondItem="aXv-qW-3xJ" secondAttribute="trailing" id="0Vt-Pj-hY0"/>
                                                                                        <constraint firstItem="aXv-qW-3xJ" firstAttribute="leading" secondItem="r8s-19-J5y" secondAttribute="leading" id="59O-C4-bfY"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="aXv-qW-3xJ" secondAttribute="bottom" id="86d-6e-KQX"/>
                                                                                        <constraint firstItem="aXv-qW-3xJ" firstAttribute="top" secondItem="r8s-19-J5y" secondAttribute="top" id="kow-Jv-8MM"/>
                                                                                    </constraints>
                                                                                </view>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="f9E-9u-bqe">
                                                                                    <rect key="frame" x="111.66666666666666" y="0.0" width="97.666666666666657" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1004" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="9Uz-ew-h4g">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstItem="9Uz-ew-h4g" firstAttribute="leading" secondItem="f9E-9u-bqe" secondAttribute="leading" id="DnT-jI-6nb"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="9Uz-ew-h4g" secondAttribute="bottom" id="juY-Xv-kE5"/>
                                                                                        <constraint firstItem="9Uz-ew-h4g" firstAttribute="top" secondItem="f9E-9u-bqe" secondAttribute="top" id="mtI-rG-eHn"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="9Uz-ew-h4g" secondAttribute="trailing" id="wZn-vT-iDG"/>
                                                                                    </constraints>
                                                                                </view>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9IQ-VT-TY7">
                                                                                    <rect key="frame" x="223.33333333333329" y="0.0" width="97.666666666666657" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1005" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="ILd-J7-fLq">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="bottom" secondItem="ILd-J7-fLq" secondAttribute="bottom" id="FNZ-LP-FJ5"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="ILd-J7-fLq" secondAttribute="trailing" id="SoQ-ag-umX"/>
                                                                                        <constraint firstItem="ILd-J7-fLq" firstAttribute="top" secondItem="9IQ-VT-TY7" secondAttribute="top" id="nkd-Hl-wVK"/>
                                                                                        <constraint firstItem="ILd-J7-fLq" firstAttribute="leading" secondItem="9IQ-VT-TY7" secondAttribute="leading" id="vxD-3b-yad"/>
                                                                                    </constraints>
                                                                                </view>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="92" id="oqH-Da-Dic"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="14" translatesAutoresizingMaskIntoConstraints="NO" id="vaq-x7-pMa">
                                                                            <rect key="frame" x="0.0" y="212" width="321" height="92"/>
                                                                            <subviews>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Whe-BO-9lY">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1006" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="Ydc-SJ-cC2">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstItem="Ydc-SJ-cC2" firstAttribute="top" secondItem="Whe-BO-9lY" secondAttribute="top" id="BpH-CX-L2L"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="Ydc-SJ-cC2" secondAttribute="trailing" id="bvD-SS-fae"/>
                                                                                        <constraint firstItem="Ydc-SJ-cC2" firstAttribute="leading" secondItem="Whe-BO-9lY" secondAttribute="leading" id="kOz-o6-c51"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="Ydc-SJ-cC2" secondAttribute="bottom" id="nVI-0c-QTg"/>
                                                                                    </constraints>
                                                                                </view>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kBh-vu-PxW">
                                                                                    <rect key="frame" x="111.66666666666666" y="0.0" width="97.666666666666657" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1007" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="Zrb-hQ-1kY">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="bottom" secondItem="Zrb-hQ-1kY" secondAttribute="bottom" id="D2G-Fp-m1h"/>
                                                                                        <constraint firstItem="Zrb-hQ-1kY" firstAttribute="top" secondItem="kBh-vu-PxW" secondAttribute="top" id="IyR-U0-414"/>
                                                                                        <constraint firstItem="Zrb-hQ-1kY" firstAttribute="leading" secondItem="kBh-vu-PxW" secondAttribute="leading" id="umJ-Lj-Mw8"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="Zrb-hQ-1kY" secondAttribute="trailing" id="yCh-c1-5SU"/>
                                                                                    </constraints>
                                                                                </view>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xv0-lL-j4e">
                                                                                    <rect key="frame" x="223.33333333333329" y="0.0" width="97.666666666666657" height="92"/>
                                                                                    <subviews>
                                                                                        <imageView hidden="YES" clipsSubviews="YES" tag="1008" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addPhotoButton" translatesAutoresizingMaskIntoConstraints="NO" id="0tS-HO-p4Q">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="97.666666666666671" height="92"/>
                                                                                        </imageView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="trailing" secondItem="0tS-HO-p4Q" secondAttribute="trailing" id="Lc7-Nz-Dws"/>
                                                                                        <constraint firstItem="0tS-HO-p4Q" firstAttribute="top" secondItem="Xv0-lL-j4e" secondAttribute="top" id="by0-gj-t1v"/>
                                                                                        <constraint firstItem="0tS-HO-p4Q" firstAttribute="leading" secondItem="Xv0-lL-j4e" secondAttribute="leading" id="dgh-F8-Q69"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="0tS-HO-p4Q" secondAttribute="bottom" id="fdw-Bs-WJ0"/>
                                                                                    </constraints>
                                                                                </view>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="92" id="Tx6-FI-98y"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="T92-Ly-ws1" secondAttribute="trailing" id="GG2-zO-tqQ"/>
                                                                <constraint firstAttribute="bottom" secondItem="T92-Ly-ws1" secondAttribute="bottom" id="OSA-DW-yMF"/>
                                                                <constraint firstItem="T92-Ly-ws1" firstAttribute="top" secondItem="hdU-Bi-S6a" secondAttribute="top" id="Yjv-pc-sNV"/>
                                                                <constraint firstItem="T92-Ly-ws1" firstAttribute="leading" secondItem="hdU-Bi-S6a" secondAttribute="leading" id="xGw-FD-5KS"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="l4q-9j-HJh" firstAttribute="leading" secondItem="wRW-9V-0Br" secondAttribute="leading" constant="16" id="SLB-AY-RYS"/>
                                                <constraint firstAttribute="trailing" secondItem="l4q-9j-HJh" secondAttribute="trailing" constant="16" id="jq7-Km-ogv"/>
                                                <constraint firstItem="l4q-9j-HJh" firstAttribute="top" secondItem="wRW-9V-0Br" secondAttribute="top" constant="16" id="olf-d3-dEH"/>
                                                <constraint firstAttribute="bottom" secondItem="l4q-9j-HJh" secondAttribute="bottom" constant="16" id="xRw-mU-D2k"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="Vcj-hk-3Oo" secondAttribute="trailing" constant="20" id="4Rv-da-iew"/>
                                <constraint firstItem="Vcj-hk-3Oo" firstAttribute="leading" secondItem="Rwb-yT-hix" secondAttribute="leading" constant="20" id="HWS-Mh-3Tu"/>
                                <constraint firstAttribute="bottom" secondItem="Vcj-hk-3Oo" secondAttribute="bottom" constant="30" id="mKg-Uk-KxY"/>
                                <constraint firstItem="Vcj-hk-3Oo" firstAttribute="top" secondItem="Rwb-yT-hix" secondAttribute="top" constant="20" id="qpw-0s-pT4"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstItem="Rwb-yT-hix" firstAttribute="width" secondItem="GhA-k9-s9o" secondAttribute="width" id="0Kf-bq-GAn"/>
                        <constraint firstAttribute="bottom" secondItem="Rwb-yT-hix" secondAttribute="bottom" id="1Mq-vz-J66"/>
                        <constraint firstAttribute="trailing" secondItem="Rwb-yT-hix" secondAttribute="trailing" id="Q4D-RM-3Lb"/>
                        <constraint firstItem="Rwb-yT-hix" firstAttribute="top" secondItem="GhA-k9-s9o" secondAttribute="top" id="QYf-oR-fmN"/>
                        <constraint firstItem="Rwb-yT-hix" firstAttribute="bottom" secondItem="GhA-k9-s9o" secondAttribute="bottom" id="ehQ-mB-xdv"/>
                        <constraint firstItem="Rwb-yT-hix" firstAttribute="leading" secondItem="GhA-k9-s9o" secondAttribute="leading" id="fRK-z3-VOC"/>
                    </constraints>
                </scrollView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="GhA-k9-s9o" firstAttribute="bottom" secondItem="fnl-2z-Ty3" secondAttribute="bottom" id="92m-uW-ZAr"/>
                <constraint firstItem="GhA-k9-s9o" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="Hst-Li-gyM"/>
                <constraint firstItem="GhA-k9-s9o" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="RU9-l6-SCY"/>
                <constraint firstItem="GhA-k9-s9o" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="ru5-zN-QO6"/>
            </constraints>
            <point key="canvasLocation" x="55.725190839694655" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <image name="addPhotoButton" width="92" height="92"/>
        <image name="arowRIght" width="16" height="16"/>
        <image name="editPan" width="16" height="16"/>
    </resources>
</document>
