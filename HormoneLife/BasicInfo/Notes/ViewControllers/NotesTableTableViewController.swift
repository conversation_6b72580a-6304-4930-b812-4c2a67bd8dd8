//
//  NotesTableTableViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/23.
//

import UIKit
import MJRefresh

class NotesTableTableViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource, AddNoteViewControllerDelegate {
    
    let viewModel = NotePageViewModel()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .themeColor
        tableView.separatorStyle = .none
        
        tableView.register(NotesSectionHeader.self, forHeaderFooterViewReuseIdentifier: "NotesSectionHeader")
//        tableView.register(UINib(nibName: "NoteTableViewCell", bundle: nil), forCellReuseIdentifier: "NoteTableViewCell")
        tableView.register(UserNoteCell.classForCoder(), forCellReuseIdentifier: UserNoteCell.description())
        tableView.mj_header = MJRefreshNormalHeader(refreshingBlock: {
            self.downRefreshDataRequest()
        })
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()

        navigationItem.title = "Notes"
        setupUI()
        setNavigationBar()
        
        self.downRefreshDataRequest()
        
        NotificationCenter.default.addObserver(self, selector: #selector(updateSuccess), name: NSNotification.Name(rawValue: "user_note_update_success"), object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc func updateSuccess() {
        self.tableView.scrollsToTop = true
        self.downRefreshDataRequest()
    }
    
    func downRefreshDataRequest() {
        self.viewModel.getNotesPage(refresh: true, userId: Interface.shared().loggedInUser?.userInfo.id, imageUrls: nil) {[weak self] success in
            self?.handleRequestSuccessResult(refresh: self!.viewModel.hasMoreData, count: self!.viewModel.dataSource.count)
        } failure: { error in
            self.handleRequestFailureResult()
        }
    }
    
    func upMoreDataRequest() {
        self.viewModel.getNotesPage(refresh: false, userId: Interface.shared().loggedInUser?.userInfo.id, imageUrls: nil) {[weak self] success in
            self?.handleRequestSuccessResult(refresh: self!.viewModel.hasMoreData, count: self!.viewModel.dataSource.count)
        } failure: { error in
            self.handleRequestFailureResult()
        }
    }
    
    func setupUI() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setNavigationBar() {
        let imageViewSearch = UIImageView(frame: CGRect(x: 0, y: 0, width: 24, height: 24))
        imageViewSearch.image = UIImage(named: "navSearchIcon")
        imageViewSearch.isUserInteractionEnabled = true
        imageViewSearch.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapSearch)))
        
        let imageViewAdd = UIImageView(frame: CGRect(x: 0, y: 0, width: 24, height: 24))
        imageViewAdd.image = UIImage(named: "navAddIcon")
        imageViewAdd.isUserInteractionEnabled = true
        imageViewAdd.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapAdd)))
        
        let stackview = UIStackView(arrangedSubviews: [imageViewAdd])
        stackview.distribution = .fill
        stackview.alignment = .center
        stackview.axis = .horizontal
        stackview.spacing = 22
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: stackview)
    }
    
    @objc func didTapSearch() {
        // TODO: reFetch Data and then reload
        
        tableView.reloadData()
    }
    
    @objc func didTapAdd() {
        let addNoteVc = AddNoteViewController(nibName: "AddNoteViewController", bundle: nil)
        addNoteVc.delegate = self
        navigationController?.pushViewController(addNoteVc, animated: true)
    }
    
    func didAddNote() {
        updateSuccess()
    }

    // MARK: - Table view data source
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        // #warning Incomplete implementation, return the number of rows
        return self.viewModel.dataSource.count
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let vc = NoteDetailViewController()
        vc.noteModel = self.viewModel.dataSource[indexPath.row]
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: UserNoteCell.description()) as! UserNoteCell
        
        cell.refresh(model: self.viewModel.dataSource[indexPath.row])
        return cell
//        guard let cell = tableView.dequeueReusableCell(withIdentifier: "NoteTableViewCell", for: indexPath) as? NoteTableViewCell else {
//            return UITableViewCell()
//        }
//        cell.refresh(model: self.viewModel.dataSource[indexPath.row])
//        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard let header = tableView.dequeueReusableHeaderFooterView(withIdentifier: "NotesSectionHeader") as? NotesSectionHeader else {
            return nil
        }
        
        let user = Interface.shared().loggedInUser?.userInfo
        let days = "\(dayForDaysCount("\(user?.createTime ?? "")"))"
        header.titleLabel.text = "It's been \(days) day\((Int(days) ?? 0) > 1 ? "s" : "")"
        return header
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 40
    }
    
    func handleRequestSuccessResult(refresh: Bool, count: Int) {
        self.tableView.reloadData()
        if self.tableView.mj_header?.isRefreshing == true {
            self.tableView.mj_header?.endRefreshing()
        }
        if self.tableView.mj_footer?.isRefreshing == true {
            self.tableView.mj_footer?.endRefreshing()
        }
        if refresh {
            self.tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingBlock: {
                self.upMoreDataRequest()
            })
            
        } else {
            self.tableView.mj_footer?.endRefreshingWithNoMoreData()
        }
        
//        if count > 0 {
//            self.noDataView.isHidden = true
//        } else {
//            self.noDataView.isHidden = false
//        }
    }
    
    func handleRequestFailureResult() {
        if self.tableView.mj_header?.isRefreshing == true {
            self.tableView.mj_header?.endRefreshing()
        }
        if self.tableView.mj_footer?.isRefreshing == true {
            self.tableView.mj_footer?.endRefreshing()
        }
    }
}
