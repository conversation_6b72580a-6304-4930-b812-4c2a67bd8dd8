<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
        <array key="gilroy regular.otf">
            <string><PERSON><PERSON>-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="299" id="KGk-i7-Jjw" customClass="NoteTableViewCell" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="406" height="299"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="406" height="299"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="u3i-ih-3jN">
                        <rect key="frame" x="24" y="20" width="358" height="259"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="eDc-e0-FSN">
                                <rect key="frame" x="0.0" y="20" width="342" height="20"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="e27-eW-qJg">
                                        <rect key="frame" x="0.0" y="0.0" width="2" height="20"/>
                                        <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="2" id="S4i-E0-qSf"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ohZ-yk-1bU">
                                        <rect key="frame" x="6" y="0.0" width="6" height="20"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="6" id="CDr-1L-K2l"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Today" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HAD-rs-qjk">
                                        <rect key="frame" x="16" y="0.0" width="265" height="20"/>
                                        <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                        <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="210" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fpd-sW-2T8">
                                        <rect key="frame" x="285" y="0.0" width="30" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="30" id="Zox-oF-gLq"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="16"/>
                                        <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Day" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hhF-mj-8zf">
                                        <rect key="frame" x="319" y="0.0" width="23" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="23" id="egG-0x-QHW"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="12"/>
                                        <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="mBZ-5d-qcM"/>
                                </constraints>
                            </stackView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your nickname will be your identity in your own life, so make it unique" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1vf-IS-c7w">
                                <rect key="frame" x="16" y="52" width="326" height="83"/>
                                <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="14"/>
                                <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YHb-gk-pjK">
                                <rect key="frame" x="16" y="151" width="326" height="88"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="ldw-7w-Ez8">
                                        <rect key="frame" x="0.0" y="0.0" width="326" height="88"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="noteImageSample" translatesAutoresizingMaskIntoConstraints="NO" id="ekl-Cd-NHR">
                                                <rect key="frame" x="0.0" y="0.0" width="98" height="88"/>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="noteImageSample" translatesAutoresizingMaskIntoConstraints="NO" id="vrf-aU-F2b">
                                                <rect key="frame" x="114" y="0.0" width="98" height="88"/>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="noteImageSample" translatesAutoresizingMaskIntoConstraints="NO" id="wXy-XS-0Vu">
                                                <rect key="frame" x="228" y="0.0" width="98" height="88"/>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="ldw-7w-Ez8" secondAttribute="trailing" id="5mX-vh-zNn"/>
                                    <constraint firstItem="ldw-7w-Ez8" firstAttribute="top" secondItem="YHb-gk-pjK" secondAttribute="top" id="LIW-wo-pBm"/>
                                    <constraint firstAttribute="bottom" secondItem="ldw-7w-Ez8" secondAttribute="bottom" id="NBx-WH-Kb3"/>
                                    <constraint firstAttribute="height" constant="88" id="Oji-kj-MI1"/>
                                    <constraint firstItem="ldw-7w-Ez8" firstAttribute="leading" secondItem="YHb-gk-pjK" secondAttribute="leading" id="vh2-XA-Wtu"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="YHb-gk-pjK" firstAttribute="top" secondItem="1vf-IS-c7w" secondAttribute="bottom" constant="16" id="9h5-cS-xxs"/>
                            <constraint firstAttribute="bottom" secondItem="YHb-gk-pjK" secondAttribute="bottom" constant="20" id="Fvi-eM-qZO"/>
                            <constraint firstItem="1vf-IS-c7w" firstAttribute="leading" secondItem="u3i-ih-3jN" secondAttribute="leading" constant="16" id="TXW-bI-wry"/>
                            <constraint firstItem="eDc-e0-FSN" firstAttribute="top" secondItem="u3i-ih-3jN" secondAttribute="top" constant="20" id="U5t-4s-Tfp"/>
                            <constraint firstItem="1vf-IS-c7w" firstAttribute="top" secondItem="eDc-e0-FSN" secondAttribute="bottom" constant="12" id="UDW-PP-Wxs"/>
                            <constraint firstItem="YHb-gk-pjK" firstAttribute="leading" secondItem="u3i-ih-3jN" secondAttribute="leading" constant="16" id="gx0-LA-RBd"/>
                            <constraint firstItem="eDc-e0-FSN" firstAttribute="leading" secondItem="u3i-ih-3jN" secondAttribute="leading" id="k6L-0b-g6V"/>
                            <constraint firstAttribute="trailing" secondItem="YHb-gk-pjK" secondAttribute="trailing" constant="16" id="sN5-hu-czb"/>
                            <constraint firstAttribute="trailing" secondItem="1vf-IS-c7w" secondAttribute="trailing" constant="16" id="uz4-Ja-S60"/>
                            <constraint firstAttribute="trailing" secondItem="eDc-e0-FSN" secondAttribute="trailing" constant="16" id="vI7-RP-hs4"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="4"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="u3i-ih-3jN" secondAttribute="trailing" constant="24" id="8J3-z9-v3v"/>
                    <constraint firstAttribute="bottom" secondItem="u3i-ih-3jN" secondAttribute="bottom" constant="20" id="BNZ-yZ-7zn"/>
                    <constraint firstItem="u3i-ih-3jN" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="24" id="LDA-41-fRc"/>
                    <constraint firstItem="u3i-ih-3jN" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="20" id="iJz-vn-2eT"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="content" destination="1vf-IS-c7w" id="Hzi-Ka-evs"/>
                <outlet property="dueDay" destination="Fpd-sW-2T8" id="i9r-qT-RNO"/>
                <outlet property="imageContainerView" destination="YHb-gk-pjK" id="IfQ-io-BhS"/>
                <outlet property="timeTitle" destination="HAD-rs-qjk" id="IPg-ca-hxs"/>
                <outletCollection property="images" destination="ekl-Cd-NHR" collectionClass="NSMutableArray" id="xAz-xP-COr"/>
                <outletCollection property="images" destination="vrf-aU-F2b" collectionClass="NSMutableArray" id="Ocy-Tr-6nz"/>
                <outletCollection property="images" destination="wXy-XS-0Vu" collectionClass="NSMutableArray" id="s6g-8p-x2s"/>
            </connections>
            <point key="canvasLocation" x="198.47328244274809" y="45.422535211267608"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="noteImageSample" width="88" height="88"/>
    </resources>
</document>
