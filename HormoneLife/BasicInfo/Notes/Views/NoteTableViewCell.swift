//
//  NoteTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/3.
//

import UIKit

class NoteTableViewCell: UITableViewCell {

    @IBOutlet weak var timeTitle: UILabel!
    @IBOutlet weak var dueDay: UILabel!
    @IBOutlet weak var content: UILabel!
    
    @IBOutlet weak var imageContainerView: UIView!
    @IBOutlet var images: [UIImageView]!
    
    func refresh(model: NotePageListModel) {
        self.content.text = model.content
        
        if let imageUrls = model.imageUrls, imageUrls.count > 0 {
            imageContainerView.isHidden = false
            let imgs = imageUrls.components(separatedBy: ",")
            for (index, item) in self.images.enumerated(){
                if index < imgs.count {
                    item.isHidden = false
                    let url = imgs[index]
                    if url.hasPrefix("http") {
                        item.kf.setImage(with: URL(string: imgs[index]))
                    } else {
                        item.kf.setImage(with: URL(string: baseImageURL + imgs[index]))
                    }
                } else {
                    item.isHidden = true
                }
            }
        } else {
            imageContainerView.isHidden = true
        }
        
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
}
