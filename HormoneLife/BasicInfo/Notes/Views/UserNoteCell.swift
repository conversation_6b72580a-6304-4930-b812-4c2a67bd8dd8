//
//  UserNoteCell.swift
//  HormoneLife
//
//  Created by bm on 2024/9/6.
//

import UIKit

class UserNoteCell: BaseTableViewCell {
    
    var canPreviewImage = false
    let layout = RectangleLayout()
    var collectionView : UICollectionView! {
        didSet {
            collectionView.register(HomSingleImageCollectionViewCell.classForCoder(), forCellWithReuseIdentifier: HomSingleImageCollectionViewCell.description())
        }
    }
    var dataArr = [String]()
    
    let contentLab = UILabel()
    let timeLab = UILabel()
    let releaseDayLab = UILabel()
    let leftLineView = UIView()
    let timeTipLab = UILabel()
    
    func refresh(model: NotePageListModel) {
        self.collectionView.isUserInteractionEnabled = false
        self.canPreviewImage = false
       
        self.contentLab.text = model.content
        leftLineView.isHidden = false
        if let imageUrlStr = model.imageUrls, imageUrlStr.count > 0 {
            self.collectionView.isHidden = false
            self.dataArr = imageUrlStr.components(separatedBy: ",")
            self.collectionView.reloadData()
        } else {
            self.collectionView.isHidden = true
        }
        
        var height = 0.0
        let layout = self.collectionView.collectionViewLayout as! RectangleLayout
        height = Double(((self.dataArr.count-1)/layout.numbersOfCellInRow+1))*Double(layout.normalCellHeight)
        self.collectionView.snp.remakeConstraints { make in
            make.height.equalTo(height)
        }
        self.timeLab.text = "\(model.days ?? "0")"
        if isToday(model.markTime ?? "") {
            self.releaseDayLab.text = "Today"
        } else if isYesterday(model.markTime ?? "") {
            self.releaseDayLab.text = "Yesterday"
        } else {
            self.releaseDayLab.text = timeFormat(date: model.markTime ?? "", format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy")
        }
    }
    
    func refreshDetail(model: NoteDetailModel) {
        self.collectionView.isUserInteractionEnabled = true
        self.canPreviewImage = true
        self.contentLab.textColor = UIColorFromRGB(rgbValue: 0x999999)
        if model.type == 1 {
            self.contentLab.text = "-"
            if let imageUrlStr = model.imageUrls, imageUrlStr.count > 0 {
                self.collectionView.isHidden = false
                self.contentLab.isHidden = true
                self.timeLab.isHidden = false
                self.timeTipLab.isHidden = false
                self.dataArr = imageUrlStr.components(separatedBy: ",")
                self.collectionView.reloadData()
                var height = 0.0
                let layout = self.collectionView.collectionViewLayout as! RectangleLayout
                height = Double(((self.dataArr.count-1)/layout.numbersOfCellInRow+1))*Double(layout.normalCellHeight)
                self.collectionView.snp.remakeConstraints { make in
                    make.height.equalTo(height)
                }
            } else {
                self.collectionView.isHidden = true
            }
//            self.timeLab.text = "\(dayForDaysCount("\(model.content ?? "")"))"
            self.timeLab.text = "\(model.days ?? "0")"
            if isToday(model.content ?? "") {
                self.releaseDayLab.text = "Today"
            } else if isYesterday(model.content ?? "") {
                self.releaseDayLab.text = "Yesterday"
            } else {
                self.releaseDayLab.text = timeFormat(date: model.content ?? "", format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy")
            }
            leftLineView.isHidden = false
        } else {
            leftLineView.isHidden = true
            self.contentLab.isHidden = false
            self.collectionView.isHidden = true
            self.timeLab.isHidden = true
            self.timeTipLab.isHidden = true
            self.releaseDayLab.text = model.title
            self.contentLab.text = model.content
        }
    }
    
    override func createSubviews() {
        
        self.contentView.backgroundColor = .clear
        self.backgroundColor = .clear
        
        let containerView = UIView()
        containerView.layer.cornerRadius = 10
        containerView.layer.masksToBounds = true
        containerView.backgroundColor = .white
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.top.equalTo(20)
            make.bottom.equalTo(0)
        }
        
        
        leftLineView.backgroundColor = UIColorFromRGB(rgbValue: 0x043433)
        containerView.addSubview(leftLineView)
        leftLineView.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.top.equalTo(22)
            make.width.equalTo(2)
            make.height.equalTo(14)
        }
        
        releaseDayLab.textColor = UIColorFromRGB(rgbValue: 0x043433)
        releaseDayLab.font = UIFont(name: "Gilroy-Medium-Medium", size: 16)
        containerView.addSubview(releaseDayLab)
        releaseDayLab.text = "Today"
        releaseDayLab.snp.makeConstraints { make in
            make.left.equalTo(leftLineView.snp.right).offset(14)
            make.centerY.equalTo(leftLineView)
            make.height.equalTo(20)
        }
        
        
        timeTipLab.textAlignment = .right
        timeTipLab.text = "Day"
        timeTipLab.textColor = UIColorFromRGB(rgbValue: 0x043433, alpha: 0.4)
        timeTipLab.font = UIFont(name: "Gilroy-Medium-Medium", size: 12)
        containerView.addSubview(timeTipLab)
        timeTipLab.snp.makeConstraints { make in
            make.right.equalTo(-15)
            make.centerY.equalTo(leftLineView)
            make.height.equalTo(20)
        }
        
        
        timeLab.textAlignment = .right
        timeLab.text = "-"
        timeLab.textColor = UIColorFromRGB(rgbValue: 0x043433)
        timeLab.font = UIFont(name: "Gilroy-Medium-Medium", size: 16)
        containerView.addSubview(timeLab)
        timeLab.snp.makeConstraints { make in
            make.right.equalTo(timeTipLab.snp.left).offset(-6)
            make.centerY.equalTo(leftLineView)
            make.height.equalTo(20)
        }
        
        let contentStackView = UIStackView()
        contentStackView.axis = .vertical
        contentStackView.spacing = 14
        contentStackView.layer.masksToBounds = true
        contentStackView.backgroundColor = .clear
        containerView.addSubview(contentStackView);
        contentStackView.snp.makeConstraints { make in
            make.left.equalTo(leftLineView.snp.right).offset(14)
            make.top.equalTo(leftLineView.snp.bottom).offset(16)
            make.right.equalTo(-15)
            make.bottom.equalTo(-15)
        }
        
        contentLab.textColor = UIColorFromRGB(rgbValue: 0x043433)
        contentLab.font = UIFont(name: "Gilroy-Medium-Medium", size: 14)
        contentLab.lineBreakMode = .byWordWrapping
        contentLab.numberOfLines = 0
        contentLab.text = "-"
        contentStackView.addArrangedSubview(contentLab)
        contentLab.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(10)
        }
        
        let cellHeight = (kScreenWidth-20-20-30-8)/3.0
        layout.numbersOfCellInRow = 3
        layout.normalCellHeight = Float(cellHeight)
        layout.numbersOfCellInFirstRow = 3
        layout.maxHeight = Float(cellHeight)
        
        self.collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        self.collectionView.isScrollEnabled = false
        self.collectionView.backgroundColor = .white
        self.collectionView.delegate = self
        self.collectionView.dataSource = self
        self.collectionView.showsHorizontalScrollIndicator = false
        contentStackView.addArrangedSubview(self.collectionView)
        self.collectionView.snp.makeConstraints { make in
            make.height.equalTo(CGFloat.leastNormalMagnitude)
        }
    }
}

extension UserNoteCell : UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.dataArr.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HomSingleImageCollectionViewCell.description(), for: indexPath) as! HomSingleImageCollectionViewCell
        cell.refresh(imageUrl: self.dataArr[indexPath.item])
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if self.canPreviewImage {
            let browser = JXPhotoBrowser()
            
            browser.numberOfItems = {
                self.dataArr.count
            }
            browser.reloadCellAtIndex = { [weak self] context in
                let model = self!.dataArr[context.index]
                let browserCell = context.cell as? JXPhotoBrowserImageCell
                browserCell?.imageView.kf.setImage(with: URL(string: model), completionHandler: { result in
                    browserCell?.setNeedsLayout()
                })
            }
            
            browser.pageIndex = indexPath.item
            browser.show()
        }
    }
}
