//
//  HomeNormalImageCollectionViewCell.swift
//  ZeaDoor
//
//  Created by <PERSON><PERSON><PERSON> on 2023/11/30.
//

import UIKit

class HomSingleImageCollectionViewCell: UICollectionViewCell {
    
    
    let iconImgView = UIImageView()
    let nameLab = UILabel()
    let moreImageView = UIImageView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.contentView.backgroundColor = .white
        createSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func createSubviews() {
        
        //iconImgView.image = UIImage(named: "")
//        iconImgView.layer.cornerRadius = 3
        iconImgView.layer.masksToBounds = true
        iconImgView.backgroundColor = .clear
        iconImgView.contentMode = .scaleAspectFill
        contentView.addSubview(iconImgView)
        iconImgView.snp.makeConstraints { make in
            make.left.top.equalTo(3)
            make.right.bottom.equalTo(-3)
        }
    }
    
    func refresh(imageUrl: String) {
        if imageUrl.hasPrefix("http") {
            iconImgView.kf.setImage(with: URL(string: imageUrl))
        } else {
            iconImgView.kf.setImage(with: URL(string: baseImageURL + imageUrl))
        }
    }
    
}

