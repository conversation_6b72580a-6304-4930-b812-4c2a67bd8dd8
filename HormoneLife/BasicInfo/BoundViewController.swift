//
//  BoundViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/1.
//

import UIKit

class BoundViewController: BaseViewController {

    @IBOutlet weak var descriptionTitle: UILabel!
    @IBOutlet weak var inputTextField: UITextField!
    @IBOutlet weak var sendButton: UIButton!
    @IBOutlet weak var submitButton: UIButton!
    @IBOutlet weak var untieButton: UIButton!
    
    lazy var bindAccountViewModel = BindAccountViewModel()
    
    let boundType: BoundType
    var accountText: String = ""
    
    var timer: Timer?
    var totalTime = 61
    
    init(_ boundType: BoundType = .mobile) {
        self.boundType = boundType
        super.init(nibName: "BoundViewController", bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .themeColor
        self.title = boundType.vcTitle
        localization()
        inputTextField.delegate = self
        inputTextField.setAttributedPlaceholer(inputTextField.placeholder)
        setupUntieButton()
        checkSubmitButtonStatus()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = false
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        removeTimer()
    }
    
    private func checkSubmitButtonStatus() {
        submitButton.setTitle(boundType.submitButtonTitle, for: .normal)
        submitButton.isEnabled = !inputTextField.isEmpty
        
        if boundType == .mobile {
            submitButton.isEnabled = !inputTextField.isEmpty && !(inputTextField.text ?? "").contains("@")
        }
        
        let disableColor = UIColor.mainTextColor.withAlphaComponent(0.6)
        let enableColor = UIColor.mainTextColor.withAlphaComponent(1)
        submitButton.backgroundColor = submitButton.isEnabled ? enableColor : disableColor
    }
    
    func setupUntieButton() {
        untieButton.layer.borderColor = UIColor.mainTextColor.cgColor
        untieButton.layer.borderWidth = 1
        untieButton.isHidden = boundType.isHiddenUntieButton
        
        switch boundType {
        case .unBoundAppleId(let appleId):
            inputTextField.text = appleId
        case .unBoundFacebook(let facebook):
            inputTextField.text = facebook
        case .unBoundGoogle(let google):
            inputTextField.text = google
        default:
            return
        }
    }
    
    func localization() {
        descriptionTitle.text = boundType.getContentText(accountText).0
        inputTextField.placeholder = boundType.getContentText().1
        sendButton.isHidden = !(boundType == .mobileVerification || boundType == .emailVerification)
    }

    @IBAction func sendButtonDidTap(_ sender: Any) {
        let codeType: LoginType = boundType == .emailVerification ? .email : .phone
        let messageCodeType: MessageCodeType = boundType == .emailVerification ? .REPLACE_EMAIL : .REPLACE_PHONE
        let validator = SendValidate(account: accountText, codeType: codeType, messageCodeType: messageCodeType)
        
        func startTimer() {
            guard timer == nil else { return }
            
            // after send success
            var caculatorTime = totalTime
            timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true, block: { _ in
                caculatorTime -= 1
                self.sendButton.setTitle("|   \(caculatorTime)", for: .normal)
                if caculatorTime == -1 {
                    self.timer?.invalidate()
                    self.timer = nil
                    self.sendButton.setTitle("|   Send", for: .normal)
                }
            })
            timer?.fire()
        }
        
        UserInteractor.sendValidateCode(validator) { result in
            guard let s = result else { return }
            startTimer()
        }
    }
    
    private func removeTimer() {
        if timer != nil {
            timer?.invalidate()
            timer = nil
        }
    }
    
    @IBAction func submitButtonDidTap(_ sender: Any) {
        inputTextField.endEditing(true)
        guard let account = inputTextField.text else { return }
        switch boundType {
        case .email:
            guard account.contains("@") else {
                showToachMessage(message: "Please enter the correct email address")
                return
            }
            let boundVC = BoundViewController(.emailVerification)
            boundVC.title = "Email Address Verification"
            boundVC.accountText = account
            hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(boundVC, animated: true)
            hidesBottomBarWhenPushed = true
        case .emailVerification:
            guard let code = inputTextField.text else { return }
            UserInteractor.bindEmail(accountText, code: code) { success in
                self.success()
            }
        case .mobile:
            guard !account.contains("@") else { return }
            let boundVC = BoundViewController(.mobileVerification)
            boundVC.title = "Mobile Verification"
            boundVC.accountText = account
            hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(boundVC, animated: true)
            hidesBottomBarWhenPushed = true
        case .mobileVerification:
            guard let code = inputTextField.text else { return }
            UserInteractor.bindPhone(accountText, code: code) { success in
                self.success()
            }
        default:
            return
        }
    }
    
    @IBAction func didTapUntieButton(_ sender: Any) {
        let alert = UIAlertController(title: "Untie Your Account", message: "Are you sure want to \(boundType.vcTitle)?", preferredStyle:.alert)
        let confirmAction = UIAlertAction(title: "Confirm", style: .default) { _ in
            UserInteractor.unBindThirdPartAccount(self.boundType.grantType) { success in
                self.success()
            }
        }
        let cancelAction = UIAlertAction(title: "Cancel", style:.default, handler: nil)
        alert.addAction(cancelAction)
        alert.addAction(confirmAction)
        present(alert, animated: true, completion: nil)
    }
    
    
    func success() {
        
        hl_fetchUserInfo({
            self.navigationController?.popToRootViewController(animated: true)
            self.removeTimer()
        })
        
        
    }
}

extension BoundViewController {
    enum BoundType: Equatable {
        case mobile
        case mobileVerification
        case email
        case emailVerification
        case unBoundAppleId(appleId: String)
        case unBoundFacebook(facebook: String)
        case unBoundGoogle(google: String)
        
        func getContentText(_ inputText: String? = nil) -> (String, String) {
            switch self {
            case .mobile:
                return ("Please enter your phone number.\nAfter text verification is complete you may use this number to login.", "Please enter your mobile number")
            case .mobileVerification:
                return ("A verification code has been sent to\n\(inputText ?? "")", "Enter the verification code")
            case .email:
                return ("Please enter your email address.\nAfter verification you may use this email address to login.", "Please enter your email address")
            case .emailVerification:
                return ("A verification code has been sent to\n\(inputText ?? "")", "Enter the verification code")
            case .unBoundAppleId:
                return ("Apple ID:", "")
            case .unBoundFacebook:
                return ("Facebook:", "")
            case .unBoundGoogle:
                return ("Google:", "")
            }
        }
        
        var isHiddenUntieButton: Bool {
            switch self {
            case .mobile, .mobileVerification, .email, .emailVerification:
                return true
            default:
                return false
            }
        }
        
        var vcTitle: String {
            switch self {
            case .email, .emailVerification:
                return "Bound Email Address"
            case .mobile, .mobileVerification:
                return "Bound Mobile Phone"
            case .unBoundAppleId:
                return "Apple ID"
            case .unBoundFacebook:
                return "Facebook"
            default:
                return "Google"
            }
        }
        
        var submitButtonTitle: String {
            switch self {
            case .email, .emailVerification, .mobile, .mobileVerification:
                return "Submit"
            default:
                return "Change Binding"
            }
        }
        
        var grantType: String {
            switch self {
            case .unBoundAppleId:
                return "apple"
            case .unBoundFacebook:
                return "facebook"
            case .unBoundGoogle:
                return "google"
            case .mobile:
                return "mobile"
            case .email:
                return "email"
            default:
                return ""
            }
        }
    }
}

extension BoundViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
    }
    
    func textFieldDidChangeSelection(_ textField: UITextField) {
        checkSubmitButtonStatus()
    }
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard boundType == .mobile else { return true }
        let currentText = textField.text ?? ""
        let prospectiveText = (currentText as NSString).replacingCharacters(in: range, with: string)
        return true//prospectiveText.count <= (isProduce ? 10 : 11)
    }
}
