//
//  BirthdaySettingViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/31.
//

import UIKit

class BirthdaySettingViewController: PickPhotoViewController, PeriodPickerViewDelegate, PickPhotoDelegate, UITextFieldDelegate {

    var role: RoleType = .getPregnant
    
    let systemViewModel = CommonViewModel()
            
    @IBOutlet weak var ovulationStackView: UIStackView!
    @IBOutlet weak var avatar: UIImageView!
    @IBOutlet weak var birthdaySelectView: UIView!
    @IBOutlet weak var birthdayInputField: UITextField!
    
    @IBOutlet weak var periodStartDateView: UIView!
    @IBOutlet weak var periodStartDateField: UITextField!
    
    @IBOutlet weak var peroidLengthField: UITextField!
    @IBOutlet weak var menstrualCycleField: UITextField!
    var editingTextField: UITextField?
    var editingTextFieldOriginalValue: String?
    
    var otherTrackMethod: String?
    
    lazy var birthdayDatePicker: UIDatePicker = {
        let d = UIDatePicker(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 372))
        d.datePickerMode = .date
        d.maximumDate = Date()//before16YearsDate
        d.date = Date(timeIntervalSince1970: 946684800)
        d.preferredDatePickerStyle = .wheels
        d.addTarget(self, action: #selector(dateChanged(_:)), for: .valueChanged)
        return d
    }()
    
    lazy var periodDatePicker: UIDatePicker = {
        let d = UIDatePicker(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 372))
        d.datePickerMode = .date
        d.maximumDate = Date()
        d.preferredDatePickerStyle = .wheels
        d.addTarget(self, action: #selector(dateChanged(_:)), for: .valueChanged)
        return d
    }()
    
    var before16YearsDate: Date {
        let currentDate = Date()
        let calendar = Calendar.current
        let components = DateComponents(year: -16)
        if let newDate = calendar.date(byAdding: components, to: currentDate) {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy - MM - dd"
            print(dateFormatter.string(from: newDate))
            return newDate
        } else {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy - MM - dd"
            print(dateFormatter.string(from: Date()))
            return Date()
        }
    }
    
    lazy var periodLengthPickerView: PeriodPickerView = {
        let pickerView = PeriodPickerView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 372))
        pickerView.delegate = self
        pickerView.dataSource = periodLengthOptions
        pickerView.tag = 100
        return pickerView
    }()
    
    lazy var averageLengthPickerView: PeriodPickerView = {
        let pickerView = PeriodPickerView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 372))
        pickerView.delegate = self
        pickerView.dataSource = averageLengthOptions
        pickerView.tag = 101
        return pickerView
    }()
    
    var periodLengthOptions: [String] = {
        let minute = 1...15
        let array = minute.map { String(format: "%02d", $0) }
        return array
    }()
    
    var averageLengthOptions: [String] = {
        let minute = 15...50
        let array = minute.map { String(format: "%02d", $0) }
        return array
    }()
    
    init(_ role: RoleType) {
        self.role = role
        super.init(nibName: "BirthdaySettingViewController", bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        pickPhotoDelegate = self
        navigationItem.title = "Basic Info"
        setupUI()
        self.requestTags()
    }
    
    
    private func setupUI() {
        avatar.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(pickPhoto)))
        birthdaySelectView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapBirthday)))
        periodStartDateView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapPeriodStartView)))
        
        [birthdayInputField, periodStartDateField, peroidLengthField, menstrualCycleField].forEach {
            $0.setAttributedPlaceholer($0.placeholder)
        }
        
        birthdayInputField.inputView = birthdayDatePicker
        periodStartDateField.inputView = periodDatePicker
        peroidLengthField.inputView = periodLengthPickerView
        menstrualCycleField.inputView = averageLengthPickerView
        birthdayInputField.delegate = self
        periodStartDateField.delegate = self
        peroidLengthField.delegate = self
        menstrualCycleField.delegate = self
        
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapView)))
        
        guard let user = Interface.shared().loggedInUser?.userInfo else { return }
        avatar.sd_setImage(with: URL(string: user.headImg ?? ""), placeholderImage: UIImage(named: "peopleCircle"), context: nil)
        birthdayInputField.text = user.birthday?.covertDate().format(with: "MM/dd yyyy") ?? "01/01 2000"
        if let date = user.userBusinessConfigVO.startPeriodDate {
            periodStartDateField.text = date.covertDate().format(with: "MM/dd yyyy")
        }
        
        if (Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgMenstruationCycleAvg ?? 0) > 0 {
            menstrualCycleField.text = "\(Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgMenstruationCycleAvg ?? 0) days"
        }
        if (Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgPeriod ?? 0) > 0 {
            peroidLengthField.text = "\(Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgPeriod ?? 0) days"
        }
    }
    
    @objc func dateChanged(_ sender: UIDatePicker) {
        let selectedDate = sender.date
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd yyyy"
        let dateString = dateFormatter.string(from: selectedDate)
        editingTextField?.text = dateString
    }
    
    func textFieldShouldEndEditing(_ textField: UITextField) -> Bool {
        if textField.tag == 100, !textField.isEmpty, let birthday = textField.text {
            updateBirthday(birthday: birthday) { _ in
                
            }
        }
        
        return true
    }
    
    func updateBirthday(birthday: String, completion: @escaping (Bool?) -> Void) {
        let birthdayFormat = hl_timeFormat(date: birthday, format: "MM/dd yyyy", toFormat: "yyyy-MM-dd HH:mm:ss")
        UserInteractor.updateUserInfo(userName: "", firstName: "", sex: 0, birthday: birthdayFormat, headImg: "", label: "", photos: "") { result in
            hl_fetchUserInfo()
            completion(result)
        }
    }
    
    func requestTags() {
        self.systemViewModel.getSystemDictionary(type: "ovulation_methods") { result in
            self.addOvulation()
        } failure: { error in
            
        }
    }
    
    func addOvulation() {
        for item in self.systemViewModel.dataSource {
            let view = OvulationSelectView()
            view.refresh(model: item)
            
            if item.dictValue == "F" || item.dictValue == "f" {
                print("")
                view.inputTextFiled.isHidden = false
                view.editBlock = { [weak self] value in
                    self!.otherTrackMethod = value
                }
            }
            
            self.ovulationStackView.addArrangedSubview(view)
            view.snp.makeConstraints { make in
                make.height.equalTo(20)
            }
        }
    }
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    func didSelectValue(_ value: String) {
        let displayValue = "\(value) days"
        guard editingTextField?.text != displayValue else { return }
        editingTextField?.text = displayValue
    }
    
    func didTapSave(_ value: String) {
        didTapView()
    }
    
    func didTapCancel() {
        editingTextField?.text = editingTextFieldOriginalValue
        didTapView()
    }
    
    @IBAction func didTapMenstrualCycleIcon(_ sender: Any) {
        menstrualCycleField.becomeFirstResponder()
    }
    
    @IBAction func didTapPeriodLengthIcon(_ sender: Any) {
        peroidLengthField.becomeFirstResponder()
    }
    
    @objc func didTapPeriodStartView() {
//        popCanlendar { date in
//            self.periodStartDateField.text = date.format(with: "yyyy-MM-dd")
//        }
        periodStartDateField.becomeFirstResponder()
    }
    
    @objc func didTapBirthday() {
//        popCanlendar { date in
//
//            let birthday = date.format(with: "yyyy-MM-dd")
//            self.birthdayInputField.text = birthday
//
//            let birthdayFormat = hl_timeFormat(date: birthday, format: "yyyy-MM-dd", toFormat: "yyyy-MM-dd HH:mm:ss")
//            UserInteractor.updateUserInfo(userName: "", firstName: "", sex: 0, birthday: birthdayFormat, headImg: "", label: "", photos: "") { result in
//                hl_fetchUserInfo()
//            }
//        }
        
        if let birth = birthdayInputField.text, !birth.isEmpty {
            let birthN = "\(birth) 00:00:00"
            let birthD = birthN.covertDate(with: "MM/dd yyyy HH:mm:ss")
            birthdayDatePicker.date = birthD
        } else {
            birthdayDatePicker.date = Date(timeIntervalSince1970: 946684800)
        }
        birthdayInputField.becomeFirstResponder()
    }
    
    @IBAction func doneButtonAction(_ sender: Any) {
        
        var trackMethods = [String]()
        var isSelectF = false
        for item in self.systemViewModel.dataSource {
            if let dictValue = item.dictValue, item.isSelect {
                
                if dictValue == "F" || dictValue == "f" {
                    isSelectF = true
                }
                trackMethods.append(dictValue)
            }
        }
        
        guard let birthdayInputField = birthdayInputField.text else {
            showToachMessage(message: "Select birthday")
            return
        }
        
        updateBirthday(birthday: birthdayInputField) { result in
            guard let isSuccess = result, isSuccess else { return }
            updateInfo()
        }
        
        func updateInfo() {
            guard trackMethods.count > 0 else {
                showToachMessage(message: "Select ovulation")
                return
            }
            
            guard let perioLength = peroidLengthField.text, perioLength.count > 0 else {
                showToachMessage(message: "Select last period length")
                return
            }
            
            guard let perioStart = periodStartDateField.text else {
                showToachMessage(message: "Select last period start day")
                return
            }
            guard let averageLength = menstrualCycleField.text, averageLength.count > 0 else {
                showToachMessage(message: "Select average length")
                return
            }
            
            var config = UserConfig()
            config.trackMethod = trackMethods.joined(separator: ",")
            if let time = self.periodStartDateField.text {
                config.startPeriodDate = timeFormat(date: time, format: "MM/dd yyyy", toFormat: "yyyy-MM-dd HH:mm:ss")
            }
            if isSelectF {
                config.otherTrackMethod = self.otherTrackMethod
            }
            
            config.period = self.peroidLengthField.text?.components(separatedBy: " ").first
            config.menstruationCycleAvg = self.menstrualCycleField.text?.components(separatedBy: " ").first
            UserInteractor.userConfigUpdate(config) { success in
                if success {
                    currentWindow?.rootViewController = TabBarController()
                }
            }
        }
    }
    
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        editingTextFieldOriginalValue = textField.text
        editingTextField = textField
        
        if textField.tag == 100 {
            if let birth = birthdayInputField.text, !birth.isEmpty {
                let birthN = "\(birth) 00:00:00"
                let birthD = birthN.covertDate(with: "MM/dd yyyy HH:mm:ss")
                birthdayDatePicker.date = birthD
            } else {
                birthdayDatePicker.date = Date(timeIntervalSince1970: 946684800)
            }
        } else if textField.tag == 101 {
            if let birth = periodStartDateField.text, !birth.isEmpty {
                let birthN = "\(birth) 00:00:00"
                let birthD = birthN.covertDate(with: "MM/dd yyyy HH:mm:ss")
                periodDatePicker.date = birthD
            } else {
                periodDatePicker.date = Date()
            }
        }
        
        return true
    }
    
    func callBackImage(photo: UIImage) {
        avatar.image = photo
        self.uploadViewModel.uploadImages(images: photo) { (url, fileName)  in
            UserInteractor.updateUserInfo(userName: "", firstName: "", sex: 0, birthday: "", headImg: self.uploadViewModel.url, label: "", photos: "") { result in
                hl_fetchUserInfo()
            }
        } failure: { error in
            
        }
    }
    
}


protocol PeriodPickerViewDelegate: AnyObject {
    func didSelectValue(_ value: String)
    func didTapSave(_ value: String)
    func didTapCancel()
}

class PeriodPickerView: UIView, PickerViewDelegate, PickerViewDataSource {
    
    weak var delegate: PeriodPickerViewDelegate?
    var dataSource: [String] = []
    
    let dayTitle: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(14)
        t.textColor = .mainTextColor.withAlphaComponent(0.4)
        t.textAlignment = .left
        t.text = "Day"
        t.isHidden = true
        return t
    }()
    
    lazy var pickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .default
//        pickerView.
//        pickerView.selectionStyle = .overlay
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()
    
    let pickerSelectedView = UIView()
    
    lazy var cancelButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .themeColor
        b.layer.cornerRadius = 4
        b.layer.borderColor = UIColor.mainTextColor.cgColor
        b.layer.borderWidth = 1
        b.setTitle("Cancel", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.addTarget(self, action: #selector(didTapCancel), for: .touchUpInside)
        return b
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.setTitle("Save", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.white, for: .normal)
        b.addTarget(self, action: #selector(didTapSave), for: .touchUpInside)
        return b
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .white
        showShadow()
        layer.cornerRadius = 12
        [pickerSelectedView, pickerView, dayTitle, cancelButton, saveButton].forEach(addSubview)
        
        pickerSelectedView.backgroundColor = .lightGrayContentColor
        pickerSelectedView.layer.cornerRadius = 4
        
        
        pickerView.snp.makeConstraints { make in
            make.height.equalTo(248)
            make.width.equalTo(40)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(30)
        }
        
        pickerSelectedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(40)
            make.centerY.equalTo(pickerView.snp.centerY)
        }
        
        dayTitle.snp.makeConstraints { make in
            make.leading.equalTo(pickerView.snp.trailing).offset(70)
            make.centerY.equalTo(pickerSelectedView)
        }
        
        cancelButton.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.leading.equalToSuperview().inset(20)
            make.trailing.equalTo(snp.centerX).offset(-10)
            make.top.equalTo(pickerView.snp.bottom).offset(20)
        }
        
        saveButton.snp.makeConstraints { make in
            make.height.top.equalTo(cancelButton)
            make.trailing.equalToSuperview().inset(20)
            make.leading.equalTo(snp.centerX).offset(10)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func didTapCancel() {
        delegate?.didTapCancel()
    }
    
    @objc func didTapSave() {
        delegate?.didTapSave("")
    }
    
    func pickerViewNumberOfRows(_ pickerView: PickerView) -> Int {
        return dataSource.count
    }
    
    func pickerViewHeightForRows(_ pickerView: PickerView) -> CGFloat {
        40
    }
    
    func pickerView(_ pickerView: PickerView, titleForRow row: Int) -> String {
        dataSource[row]
    }
    
    func pickerView(_ pickerView: PickerView, didSelectRow row: Int) {
        delegate?.didSelectValue(dataSource[row])
    }
    
    func pickerView(_ pickerView: PickerView, styleForLabel label: UILabel, highlighted: Bool) {
        label.textAlignment = .center
        
        if highlighted {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor
        } else {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor.withAlphaComponent(0.3)
        }
    }
}

class OvulationSelectView: UIView {
    
    typealias HLOvulationSelectBlock = (_ model: SystemDictionaryModel) -> Void
    
    typealias HLOvulationEditBlock = (_ value: String?) -> Void
    
    var editBlock: HLOvulationEditBlock?
    
    let titleLab = UILabel()
    let selectBtn = UIButton()
    
    var inputTextFiled = UITextField()
    
    var sysModel: SystemDictionaryModel!
    
    var selectBlock: HLOvulationSelectBlock?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        createSubviews()
    }
    
    func refresh(model: SystemDictionaryModel) {
        self.sysModel = model
        selectBtn.isSelected = model.isSelect
        titleLab.text = model.dictLabel
    }
    
    func createSubviews() {
        
        selectBtn.setImage(UIImage(named: "circleUnCheckedIcon"), for: .normal)
        selectBtn.setImage(UIImage(named: "circleCheckedIcon"), for: .selected)
        self.addSubview(selectBtn)
        selectBtn.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
            make.left.equalTo(20)
        }
        
        titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E)
        titleLab.font = UIFont(name: "Gilroy-Regular-Regular", size: 14)
        self.addSubview(titleLab)
        titleLab.snp.makeConstraints { make in
            make.left.equalTo(selectBtn.snp.right).offset(10)
            make.centerY.equalToSuperview().offset(-2)
            make.right.equalTo(-25)
        }
        
        let actionBtn = UIButton()
        actionBtn.backgroundColor = .clear
        actionBtn.addTarget(self, action: #selector(action(_ :)), for: .touchUpInside)
        addSubview(actionBtn)
        actionBtn.snp.makeConstraints { make in
            make.left.top.right.bottom.equalToSuperview()
        }
        
        self.inputTextFiled = UITextField()
        self.inputTextFiled.isHidden = true
        inputTextFiled.addTarget(self, action: #selector(textFieldDidChange(_:)), for: .editingChanged)
        self.inputTextFiled.placeholder = "Enter"
        addSubview(self.inputTextFiled)
        self.inputTextFiled.snp.makeConstraints { make in
            make.left.equalTo(titleLab).offset(50)
            make.right.bottom.equalToSuperview()
            make.top.equalTo(-5)
        }
    }
    
    @objc func textFieldDidChange(_ textField: UITextField) {
        self.editBlock?(textField.text)
    }
    
    @objc func action(_ sender : UIButton) {
        self.selectBlock?(self.sysModel)
        self.sysModel.isSelect = !self.sysModel.isSelect
        self.refresh(model: self.sysModel)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
