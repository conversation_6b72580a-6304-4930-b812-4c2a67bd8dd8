//
//  UserTestViewModel.swift
//  HormoneLife
//
//  Created by loct on 2024/8/15.
//

import UIKit

class UserTestViewModel: NSObject {

    func getTestDetail(resultId: String, completion: @escaping (TestPaperDetail?) -> Void) {
        NetWorkHandle.requestAPI(.getTestPageDetail(id: resultId)) { (result: TestPaperDetail?) in
            completion(result)
        } callback: { data in
            if let data = data {
                let json = String(data: data, encoding: .utf8)
                let model = TestPaperDetail.deserialize(from: json)
                completion(model)
            }
        }
    }
    
    func updateTest(id: String?, url: String, pageType: String?, userId: String?, lastResult: String?, markTime: String?, completion: @escaping (Bool?) -> Void) {
        
        NetWorkHandle.requestAPI(.updateTestPage(id: id, imageUrl: url, pageType: pageType, userId: userId, lastResult: lastResult, markTime: markTime)) { (result: Bool?) in
            completion(result)
        }
    }
    
    func uploadTest(imageUrl: String, pageType: String?, success: @escaping (_ testMode: UserTestResultPageModel?, _ detailModel: TestPaperDetail?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        var params = ["imageUrl" : imageUrl]
        
        if let pageType = pageType {
            params["type"] = pageType
        }
        NetWorkHandle.requestPost("/app/userTestPage/getCalculateResult", parameters: params, showHub: false) { result in
            let model = UserTestResultPageModel.deserialize(from: result)
            var detailModel = TestPaperDetail.deserialize(from: result)
            detailModel?.imageUrl = imageUrl
            success(model, detailModel)
        } failure: { error in
            failure(error)
        }
    }
    
    func getWonfoReqBody(by pageType: String, success: @escaping (_ testMode: TestImageReqBody?) -> Void, failure: @escaping (_ error: String?) -> Void) {
        NetWorkHandle.requestPost("/app/userTestPage/getWonfoReqBody", parameters: ["type": pageType], showHub: false) { result in
            let model = TestImageReqBody.deserialize(from: result)
            success(model)
        } failure: { error in
            failure(error)
        }
    }
    
    func uploadPhotoToAmazon(reqBody: TestImageReqBody, success: @escaping (_ testMode: PhotoToAmazon?) -> Void, failure: @escaping (_ error: String?) -> Void) {
        
        let p = transformToJSON(reqBody)
        guard let params = transformToJSON(reqBody) else { return }
        
        NetWorkHandle.requestPostForFullUrl("https://953bpnejw9.execute-api.us-west-2.amazonaws.com/v1", parameters: params, showHub: false) { result in
            let model = PhotoToAmazon.deserialize(from: result)
            success(model)
        } failure: { error in
            failure(error)
        }
    }
    
    func transformToJSON(_ value: TestImageReqBody?) -> [String: Any]? {
        guard let item = value else {
            return nil
        }
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(item)
            let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String : Any]
            return dictionary
        } catch {
            return nil
        }
    }
    
    func getResultLabel(by pageType: String, value: Double, success: @escaping (_ resultModel: ResultLabelModel?) -> Void, failure: @escaping (_ error: String?) -> Void) {
        NetWorkHandle.requestPost("/app/userTestPage/getResultLabel", parameters: ["type": pageType, "value": value]) { result in
            let model = ResultLabelModel.deserialize(from: result)
            success(model)
        } failure: { error in
            failure(error)
        }
    }
    
    func addNewTestPaper(imageUrl: String?, lastResult: String?, ovulation: String?, pageType: String?, pregnancy: String?, resultLabel: String?, resultValue: Double?, markTime: String?, completion: @escaping (TestPaperDetail?) -> Void) {
        
        NetWorkHandle.requestAPI(.addTestPage(imageUrl: imageUrl, lastResult: lastResult, ovulation: ovulation, pageType: pageType, pregnancy: pregnancy, resultLabel: resultLabel, resultValue: resultValue, markTime: markTime)) { (result: TestPaperDetail?) in
            completion(result)
        } callback: { data in
            if let data = data {
                let json = String(data: data, encoding: .utf8)
                
                let model = TestPaperDetail.deserialize(from: json)
                completion(model)
            }
        } failure: { error in
            completion(nil)
        }
    }
    
    func getTestPageList(pageType: String?, fromDate: String? = nil, toDate: String? = nil, completion: @escaping ([TestPaperList]) -> Void) {
        NetWorkHandle.requestAPI(.getTestPageList(pageType: pageType, fromDate: fromDate, toDate: toDate)) { (result: TestPapersModel?) in
            guard let list = result?.list else { return completion([]) }
            completion(list)
        }
    }
    
    func deleteTestResult(resultId: String, completion: @escaping (Bool?) -> Void) {
        NetWorkHandle.requestAPI(.deleteTestResult(id: resultId)) { (result: Bool?) in
            completion(result)
        }
    }
    
    func getCycleSimpleList(pageType: String, completion: @escaping ([CycleSimple]) -> Void) {
        NetWorkHandle.requestAPI(.simpleList(pageType: pageType)) { (result: [CycleSimple]?) in
            guard let list = result else {
                completion([])
                return
            }
            completion(list)
        }
    }
    
    func calendarPeriodRecordDetail(date: String, completion: @escaping (PeriodRecordDetail?) -> Void) {
        
        let params = ["date" : date]
        
        NetWorkHandle.requestPost("/app/userCalendar/periodRecordDetail", parameters: params) { result in
            let model = PeriodRecordDetail.deserialize(from: result)
            completion(model)
        } failure: { error in
            
        }
    }
    
    func calendarPeriodUpdate(id: Int?, cervicalMucus: String?, loveState: Int?, markTime: String?, symptomsFlow: String?, temperature: CGFloat?, temperatureUnit: Int?, completion: @escaping (Bool?) -> Void) {
        NetWorkHandle.requestAPI(.calendarInsertTestDetail(id: id, cervicalMucus: cervicalMucus, loveState: loveState, markTime: markTime, symptomsFlow: symptomsFlow, temperature: temperature, temperatureUnit: temperatureUnit)) { (result: Bool?) in
            completion(result)
        }
    }
    
    // state : 1 on , 0 down
    func menstruationBeginsUpdate(id: Int?, state: Int, selectDate: String?, startOrEnd: Int?, startPeriodTime: String?, endPeriodTime: String?, completion: @escaping (Bool?) -> Void, failure: ((_ error: String?) -> Void)? = nil) {
        NetWorkHandle.requestAPI(.menstruationBeginsUpdate(id: id, state: state, selectDate: selectDate, startOrEnd: startOrEnd, startPeriodTime: startPeriodTime, endPeriodTime: endPeriodTime)) { (result: Bool?) in
            completion(result)
        } failure: { error in
            failure?(error)
        }

    }
    
    func cycleFoldLineStatisticAll(cycleId: String, completion: @escaping ([CycleFoldLineStatistic]) -> Void) {
        NetWorkHandle.requestPost("/app/statistic/cycleFoldLineStatisticAll/\(cycleId)", parameters: [:]) { result in
            
            guard let statistic = CycleStatistics.deserialize(from: result) else {
                completion([])
                return
            }
            
            completion(statistic.data)
        } failure: { error in
            print(error)
        }
    }
    
    func cycleFoldLineStatisticDetailBy(cycleId: String, pageType: String = "", completion: @escaping (CycleFoldLineStatistic?) -> Void) {
        let params = ["cycleId" : cycleId, "pageType": pageType]
        
        NetWorkHandle.requestPost("/app/statistic/cycleFoldLineStatistic", parameters: params) { result in
            
            guard let statistic = CycleFoldLineStatistic.deserialize(from: result) else {
                completion(nil)
                return
            }
            
            completion(statistic)
        } failure: { error in
            print(error)
        }
    }
    
    // temperature
    func temperatureFoldLineStatisticBy(cycleId: String, temperatureUnit: Int = 1, completion: @escaping (CycleFoldLineStatistic?) -> Void) {
        let params: [String : Any] = ["cycleId" : cycleId, "temperatureUnit": temperatureUnit]
        
        NetWorkHandle.requestPost("/app/statistic/temperatureFoldLineStatistic", parameters: params) { result in
            
            guard let statistic = CycleFoldLineStatistic.deserialize(from: result) else {
                completion(nil)
                return
            }
            
            completion(statistic)
        } failure: { error in
            print(error)
        }
    }
    
    // chance of conception
    func cycleOvulationFoldLineStatistic(cycleId: String, completion: @escaping (CycleFoldLineStatistic?) -> Void) {
        NetWorkHandle.requestPost("/app/statistic/cycleOvulationFoldLineStatistic/\(cycleId)", parameters: [:]) { result in
            
            guard let statistic = CycleFoldLineStatistic.deserialize(from: result) else {
                completion(nil)
                return
            }
            
            completion(statistic)
        } failure: { error in
            print(error)
        }
    }
}
