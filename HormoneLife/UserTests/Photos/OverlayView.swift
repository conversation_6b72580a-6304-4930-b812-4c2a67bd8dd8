import UIKit

protocol OverlayViewDelegate: class {
    func didTapCancel()
    func didTapTakePhoto()
    func didTapChooseFromGallery()
}

class OverlayView: UIView, NibLoadable {
    @IBOutlet weak var cameraButton: UIButton!
    weak var delegate: OverlayViewDelegate?
    @IBOutlet weak var topBarView: UIView!
    @IBOutlet weak var topBarViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var bottomView: UIView!
    @IBOutlet weak var galleryButton: UIButton!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var bottomTipsView: UIImageView!
    
//    @IBOutlet weak var maxLabel: UILabel!
//    @IBOutlet weak var TLabel: UILabel!
//    @IBOutlet weak var CLabel: UILabel!
    
    @IBOutlet weak var leftRedView: UIImageView!
    
    @IBOutlet weak var rightRedLineView: UIImageView!
    
    @IBOutlet weak var tipImageContainerView: UIView!
    
    @IBOutlet weak var maxBlackView: UIView!
        
    let titleLa: UILabel = {
        let l = UILabel()
        l.font = .mediumGilroyFont(18)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "Align the strip between lines, then press capture."
        // 自动缩放字体以适应单行展示，避免因文字过长而被截断
        l.adjustsFontSizeToFitWidth = true
        l.minimumScaleFactor = 0.5  // 最小可缩放到原字号的 50%
        l.numberOfLines = 1         // 强制单行
        return l
    }()
    
    let maxLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(14)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "Max"
        return l
    }()
    
    let tLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(14)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "T"
        return l
    }()
    
    let cLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(14)
        l.textColor = .white
        l.textAlignment = .center
        l.text = "C"
        return l
    }()
    
    let whiteBGView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#FFFFFF")
        return v
    }()
    
//    let whiteGrayBGView: UIView = {
//        let v = UIView()
//        v.backgroundColor = UIColor("#F3F3F3")
//        return v
//    }()
    
    let grayView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#000000")
        return v
    }()
    
    let pinkView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#DA95AA")
        return v
    }()
    
    let redView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#9B1E24")
        return v
    }()
    
    let whitePinkView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#DAB765")
        return v
    }()
    
    let maxImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "maxTitleImage"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    let lineBoarderImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "pinkBoarderIcon"))
        i.contentMode = .scaleAspectFit
        i.isHidden = true
        return i
    }()
    
    let line1ImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "baseRedLine"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    let line2ImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "baseRedLine"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    
    let whiteGrayBGView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#F3F3F3")
        return v
    }()
    
    
    let bgDarkV1 = UIView()
    let bgDarkV2 = UIView()
    let bgDarkV3 = UIView()
    let bgDarkV4 = UIView()
    @IBOutlet weak var navigationBarView: UIView!
    @IBOutlet weak var navigationTitleLabel: UILabel!
        
    var scanViewWidth : CGFloat = 0
    var cropOffsetWidth : CGFloat = 0
    var scanViewLeftMargin : CGFloat = 0
    private var bottomToolBarHeight: CGFloat = 96.0

    // MARK: - Countdown
    private let countdownLabel: UILabel = {
        let label = UILabel()
        label.font = .boldSystemFont(ofSize: 40)
        label.textColor = .white
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()

    private var countdownTimer: Timer?
    private var remainingSeconds: Int = 0
    private var countdownCompletion: (() -> Void)?

    lazy var scanView: ScanView = {
        let view = ScanView(frame: CGRect(origin: .zero, size: .zero))
        return view
    }()
    
    override func awakeFromNib() {
        super.awakeFromNib()
        
        setupUI()
        setupFontAndColor()
        let safeAreaInsetsTop = getKeyWindow()?.safeAreaInsets.top ?? 0
        let safeAreaInsetsBottom = getKeyWindow()?.safeAreaInsets.bottom ?? 0
        topBarViewHeightConstraint.constant = 190 + safeAreaInsetsTop
    }
    
    private func setupUI() {
        
        let scanViewLeftmargin: CGFloat = 40
        self.scanViewWidth = (kScreenWidth - scanViewLeftmargin * 2)
        self.scanViewLeftMargin = scanViewLeftmargin
        self.cropOffsetWidth = (kScreenWidth - scanViewLeftmargin * 2)

        navigationBarView.backgroundColor = .rgba(0, 0, 0, 0.5)
        scanView.backgroundColor = .clear
        addSubview(scanView)
        [bgDarkV1, bgDarkV2, bgDarkV3, bgDarkV4].forEach {
            addSubview($0)
            $0.backgroundColor = .rgba(0, 0, 0, 0.5)
        }
        
        [titleLa, maxLabel, tLabel, cLabel, whiteBGView, whiteGrayBGView, grayView, pinkView, redView, whitePinkView, maxImageView, lineBoarderImageView, line1ImageView, line2ImageView].forEach(addSubview)
        
        let scanLeftMargin = ((kScreenWidth - self.scanViewWidth) / 2.0)
        let left158Margin = self.scanViewWidth * 0.158
        let left538Margin = self.scanViewWidth * 0.538
        
        titleLa.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(bottomToolBarHeight + 50 + 10)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(20)
        }
        
        whiteBGView.layer.cornerRadius = 5
        whiteBGView.layer.masksToBounds = true
        whiteBGView.snp.makeConstraints { make in
            make.height.equalTo(30)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.top.equalTo(titleLa.snp.bottom).offset(64)
        }
        
        line1ImageView.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalTo(186)
            make.top.equalTo(whiteBGView.snp.bottom)
            make.left.equalTo(scanLeftMargin + left158Margin + 3)
        }
        
        line2ImageView.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalTo(186)
            make.top.equalTo(whiteBGView.snp.bottom)
            make.left.equalTo(scanLeftMargin + left538Margin + 3)
        }
        
        grayView.snp.makeConstraints { make in
            make.top.bottom.equalTo(whiteBGView)
            make.width.equalTo(4)
            make.centerX.equalTo(line1ImageView)
//            make.left.equalTo(scanLeftMargin + left158Margin - 1.5)
        }
        
        redView.snp.makeConstraints { make in
            make.width.equalTo(4)
            make.top.bottom.equalTo(whiteBGView)
            make.centerX.equalTo(line2ImageView)
//            make.left.equalTo(scanLeftMargin + left538Margin - 1.5)
        }
        
        pinkView.snp.makeConstraints { make in
            make.width.equalTo(4)
            make.top.bottom.equalTo(whiteBGView)
            make.right.equalTo(redView.snp.left).offset(-21)
//            make.centerX.equalToSuperview().offset(4)
        }
        
        whiteGrayBGView.snp.makeConstraints { make in
            make.height.left.bottom.equalTo(whiteBGView)
            make.right.equalTo(pinkView.snp.left).offset(-20)
        }
        
        whitePinkView.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(whiteBGView)
            make.left.equalTo(redView.snp.right).offset(21)
        }
        
        lineBoarderImageView.snp.makeConstraints { make in
            make.width.equalTo(92)
            make.height.equalTo(52)
            make.centerY.equalTo(whiteBGView.snp.centerY)
            make.centerX.equalTo(redView.snp.centerX).offset(-2)
        }
        
        maxImageView.snp.makeConstraints { make in
            make.width.height.equalTo(32)
            make.centerY.equalTo(whiteBGView)
            make.left.equalTo(grayView.snp.right).offset(12)
        }
        
        maxLabel.snp.makeConstraints { make in
            make.bottom.equalTo(whiteBGView.snp.top).offset(-20)
            make.centerX.equalTo(grayView)
        }
        
        tLabel.snp.makeConstraints { make in
            make.top.equalTo(maxLabel)
            make.centerX.equalTo(pinkView)
        }
        
        cLabel.snp.makeConstraints { make in
            make.top.equalTo(maxLabel)
            make.centerX.equalTo(redView)
        }
        
        scanView.snp.makeConstraints { make in
            make.top.equalTo(topBarView.snp.bottom).offset(39)
//            make.centerX.equalToSuperview()
            make.left.equalTo(scanViewLeftmargin)
            make.right.equalTo(-scanViewLeftmargin)
            make.height.equalTo(63) // 原来42pt，高度增加50%
//            make.width.equalTo(allWidth)
        }

        // Countdown label – 24pt above scanView
        addSubview(countdownLabel)
        countdownLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(scanView.snp.top).offset(-24)
        }

        scanView.scanAnimationImage = UIImage(named: "scanLine") ?? UIImage()
        
        bgDarkV1.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
//            make.top.equalTo(topBarView.snp.bottom)
            make.top.equalTo(navigationBarView.snp.bottom)
            make.bottom.equalTo(scanView.snp.top)
        }
        
        bgDarkV2.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.bottom.equalTo(scanView)
//            make.width.equalTo(leftLineMargin - leftMargin)
            make.right.equalTo(scanView.snp.left)
//            make.right.equalTo(self.leftRedView.snp.left).offset(-leftMargin)
        }
        
        bgDarkV3.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.bottom.equalTo(scanView)
//            make.width.equalTo(leftLineMargin - leftMargin)
            make.left.equalTo(scanView.snp.right)
        }
        
        bgDarkV4.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(scanView.snp.bottom)
            make.bottom.equalTo(bottomView.snp.top)
        }
        
        let lineHorTopOne = UIView()
        lineHorTopOne.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineHorTopOne)
        lineHorTopOne.snp.makeConstraints { make in
            make.top.equalTo(scanView).offset(-2)
            make.height.equalTo(1)
            make.left.equalTo(scanView).offset(-2)
            make.width.equalTo(12)
        }
        
        let lineHorTopTwo = UIView()
        lineHorTopTwo.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineHorTopTwo)
        lineHorTopTwo.snp.makeConstraints { make in
            make.top.equalTo(scanView).offset(-2)
            make.height.equalTo(1)
            make.right.equalTo(scanView).offset(2)
            make.width.equalTo(12)
        }
        
        let lineHorTopThree = UIView()
        lineHorTopThree.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineHorTopThree)
        lineHorTopThree.snp.makeConstraints { make in
            make.top.equalTo(scanView.snp.bottom).offset(1)
            make.height.equalTo(1)
            make.left.equalTo(scanView).offset(-2)
            make.width.equalTo(12)
        }
        
        let lineHorTopFour = UIView()
        lineHorTopFour.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineHorTopFour)
        lineHorTopFour.snp.makeConstraints { make in
            make.top.equalTo(scanView.snp.bottom).offset(1)
            make.height.equalTo(1)
            make.right.equalTo(scanView).offset(2)
            make.width.equalTo(12)
        }
        
        let lineVerTopOne = UIView()
        lineVerTopOne.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineVerTopOne)
        lineVerTopOne.snp.makeConstraints { make in
            make.top.equalTo(scanView).offset(-2)
            make.height.equalTo(12)
            make.left.equalTo(scanView).offset(-2)
            make.width.equalTo(1)
        }
        
        let lineVerTopTwo = UIView()
        lineVerTopTwo.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineVerTopTwo)
        lineVerTopTwo.snp.makeConstraints { make in
            make.top.equalTo(scanView).offset(-2)
            make.height.equalTo(12)
            make.right.equalTo(scanView).offset(2)
            make.width.equalTo(1)
        }
        let lineVerTopThree = UIView()
        lineVerTopThree.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineVerTopThree)
        lineVerTopThree.snp.makeConstraints { make in
            make.bottom.equalTo(scanView.snp.bottom).offset(1)
            make.height.equalTo(10)
            make.right.equalTo(scanView).offset(2)
            make.width.equalTo(1)
        }
        
        let lineVerTopFour = UIView()
        lineVerTopFour.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
        addSubview(lineVerTopFour)
        lineVerTopFour.snp.makeConstraints { make in
            make.bottom.equalTo(scanView.snp.bottom).offset(1)
            make.height.equalTo(10)
            make.left.equalTo(scanView).offset(-2)
            make.width.equalTo(1)
        }
        
    }
    
    func getCenterMargin() {
        
    }
    
    private func setupFontAndColor() {
        //backgroundColor = UIColor.black.withAlphaComponent(0.3)
        titleLabel.font = .mediumGilroyFont(18)
        titleLabel.textColor = .white
        // 自动缩放字体以适应单行展示，避免因文字过长而换行
        titleLabel.adjustsFontSizeToFitWidth = true
        titleLabel.minimumScaleFactor = 0.5   // 最小缩放至原字体的 50%
        titleLabel.numberOfLines = 1          // 保持单行显示
        galleryButton.titleLabel?.font = .regularGilroyFont(16)
        galleryButton.setTitleColor(.mainTextColor, for: .normal)
        galleryButton.setTitle("Choose from gallery", for: .normal)
        galleryButton.layer.cornerRadius = 4
        galleryButton.layer.borderColor = UIColor.mainTextColor.cgColor
        galleryButton.layer.borderWidth = 1
        
        [maxLabel].forEach {
            $0.font = .regularGilroyFont(14)
            $0.textColor = .white
        }
    }
    @IBAction func didTapBackButton(_ sender: Any) {
        delegate?.didTapCancel()
    }

    @IBAction func didTapTakePhoto() {
        delegate?.didTapTakePhoto()
    }
    
    @IBAction func chooseFromGalleryAction(_ sender: Any) {
        delegate?.didTapChooseFromGallery()
    }
    
    // MARK: - Countdown Control
    func startCountdown(seconds: Int, completion: (() -> Void)? = nil) {
        stopCountdown() // ensure previous timer cleared
        guard seconds > 0 else { completion?(); return }

        print("【自动拍照】倒计时开始：共 \(seconds) 秒")

        remainingSeconds = seconds
        countdownCompletion = completion
        countdownLabel.text = "\(remainingSeconds)"
        countdownLabel.isHidden = false

        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            guard let self = self else { return }
            self.remainingSeconds -= 1
            print("【自动拍照】倒计时：\(self.remainingSeconds) 秒")
            if self.remainingSeconds <= 0 {
                timer.invalidate()
                self.countdownTimer = nil
                self.countdownLabel.isHidden = true
                print("【自动拍照】倒计时结束")
                self.countdownCompletion?()
                self.countdownCompletion = nil
            } else {
                self.countdownLabel.text = "\(self.remainingSeconds)"
            }
        }
        // 确保计时器在 RunLoop.common 中运行，避免 UI 操作遮挡
        RunLoop.current.add(countdownTimer!, forMode: .common)
    }

    func stopCountdown() {
        countdownTimer?.invalidate()
        countdownTimer = nil
        countdownLabel.isHidden = true
        countdownCompletion = nil
        print("【自动拍照】倒计时被手动停止")
    }
    
}
