//
//  ConfirmResultPopupViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/3.
//

import UIKit
import SDWebImage

protocol ConfirmResultPopupViewControllerDelegate: AnyObject {
    func saveSuccess(testResultId: String)
    
    func didBack()
}

class ConfirmResultPopupViewController: UIViewController {

    let popupBgView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    let titleLabel: UILabel = {
        let l = UILabel()
        l.text = "Result"
        l.textColor = .mainTextColor
        l.font = .mediumGilroyFont(17)
        l.textAlignment = .center
        return l
    }()
    
    let resultLabel: UILabel = {
        let l = UILabel()
        l.text = "Result: "
        l.textColor = .red
        l.font = .mediumGilroyFont(16)
        l.textAlignment = .center
        return l
    }()
    
    let imageView: UIImageView = {
        let i = UIImageView()
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    lazy var backButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Back", for: .normal)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.backgroundColor = .white
        b.layer.cornerRadius = 4
        b.layer.borderColor = UIColor.mainTextColor.cgColor
        b.layer.borderWidth = 1
        b.addTarget(self, action: #selector(didTapBack), for: .touchUpInside)
        return b
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Save", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.addTarget(self, action: #selector(didTapSave), for: .touchUpInside)
        return b
    }()
    
    weak var delegate: ConfirmResultPopupViewControllerDelegate?
    let testResult: PhotoToAmazon
    let viewModel = UserTestViewModel()
    let imageUrlString: String
    var selectDate: Date?
    var resultLabelModel: ResultLabelModel?
    var paperType: String?
    
    init(paperType: String?, testResult: PhotoToAmazon, imageUrl: String, value: Double, delegate: ConfirmResultPopupViewControllerDelegate?) {
        self.paperType = paperType
        self.testResult = testResult
        self.imageUrlString = imageUrl
        super.init(nibName: nil, bundle: nil)
        imageView.sd_setImage(with: URL(string: imageUrl), placeholderImage: nil)
        
        getResultLabel(by: value)
        
        self.delegate = delegate
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .black.withAlphaComponent(0.3)
        setupUI()
    }
    
    func getResultLabel(by value: Double = 0) {
        guard let type = self.paperType else { return }
        viewModel.getResultLabel(by: type, value: value) { resultModel in
            self.resultLabelModel = resultModel
            if type == "HCG" || type == "PdG" {
                self.resultLabel.text = "\(type) Result: \(resultModel?.resultLabel ?? "")"
            } else {
                self.resultLabel.text = "\(type) Result: \(value)(\(resultModel?.resultLabel ?? ""))"
            }
        } failure: { error in
            showToachMessage(message: error ?? "")
        }
    }
    
    @objc func didTapBack() {
        dismiss(animated: true)
        self.delegate?.didBack()
    }
    
    @objc func didTapSave() {
        // call API
        self.saveButton.isUserInteractionEnabled = false
        showActivityHUD()
        var time = transformTimeStamp(timeStamp: Double(Int(Date().timeIntervalSince1970)), format: "yyyy-MM-dd HH:mm:ss")
        
        if let seleDate = selectDate {
            let yyyyMMdd = seleDate.yyyyMMdd()
            let hhmmss = Date().yyyyMMdd(dateFormat: "HH:mm:ss")
            time = "\(yyyyMMdd) \(hhmmss)"
        }
        
//        var ovulation = testResult.result?.ovulation as
//        ovulation = ovulation.replacingOccurrences(of: "\n", with: "")
//        ovulation = ovulation.replacingOccurrences(of: " ", with: "")
//        ovulation = ovulation.replacingOccurrences(of: "(", with: "[")
//        ovulation = ovulation.replacingOccurrences(of: ")", with: "]")
//        paramsM.ovulation = ovulation
        
//        var ovulations =
        
        var ovulation = testResult.result?.ovulation ?? ""
        ovulation = ovulation.replacingOccurrences(of: "\n", with: "")
        ovulation = ovulation.replacingOccurrences(of: " ", with: "")
        ovulation = ovulation.replacingOccurrences(of: "(", with: "[")
        ovulation = ovulation.replacingOccurrences(of: ")", with: "]")
        
        var pregnance = testResult.result?.ovulation ?? ""
        pregnance = pregnance.replacingOccurrences(of: "\n", with: "")
        pregnance = pregnance.replacingOccurrences(of: " ", with: "")
        pregnance = pregnance.replacingOccurrences(of: "(", with: "[")
        pregnance = pregnance.replacingOccurrences(of: ")", with: "]")
        
        viewModel.addNewTestPaper(imageUrl: imageUrlString, lastResult: "\(self.resultLabelModel?.resultValue ?? 0.0)", ovulation: ovulation, pageType: self.paperType, pregnancy: pregnance, resultLabel: self.resultLabelModel?.resultLabel, resultValue: self.resultLabelModel?.resultValue, markTime: time) { success in

        //viewModel.addNewTestPaper(imageUrl: imageUrlString, lastResult: "\(self.testResult.resultValue ?? 0.0)", ovulation: testResult.result?.ovulation, pageType: testResult.type, pregnancy: testResult.result?.pregnancy, resultLabel: testResult.resultLabel, resultValue: testResult.resultValue, markTime: time) { success in
            self.didTapBack()
            
            let id = self.testResult.id ?? success?.id
            
            self.delegate?.saveSuccess(testResultId: id ?? "")
            self.saveButton.isUserInteractionEnabled = true
            hideActivityHUD()
        }
    }
    
    private func setupUI() {
        view.addSubview(popupBgView)
        popupBgView.snp.makeConstraints { make in
            make.height.equalTo(292)
            make.left.right.equalToSuperview().inset(40)
            make.center.equalToSuperview()
        }
        
        [titleLabel, resultLabel, imageView, backButton, saveButton].forEach(popupBgView.addSubview)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(40)
            make.centerX.equalToSuperview()
        }
        
        resultLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
        }
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(32)
            make.top.equalTo(resultLabel.snp.bottom).offset(20)
        }
        
        backButton.snp.makeConstraints { make in
            make.width.equalTo(108)
            make.height.equalTo(48)
            make.top.equalTo(imageView.snp.bottom).offset(40)
            make.right.equalTo(view.snp.centerX).offset(-10)
        }
        
        saveButton.snp.makeConstraints { make in
            make.width.equalTo(108)
            make.height.equalTo(48)
            make.top.equalTo(imageView.snp.bottom).offset(40)
            make.left.equalTo(view.snp.centerX).offset(10)
        }
    }
}
