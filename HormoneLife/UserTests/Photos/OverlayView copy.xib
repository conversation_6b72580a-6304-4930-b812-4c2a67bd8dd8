<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="dvW-jC-M7u" customClass="OverlayView" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Z0V-Dg-hKS">
                    <rect key="frame" x="159.5" y="563" width="56" height="56"/>
                    <color key="backgroundColor" white="1" alpha="0.40000000000000002" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="56" id="NTD-WO-0hn"/>
                        <constraint firstAttribute="height" constant="56" id="fvL-4q-kQ1"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                            <color key="value" red="0.20000001789999999" green="0.20000001789999999" blue="0.20000001789999999" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="displayP3"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                            <real key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="28"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="didTapTakePhoto" destination="dvW-jC-M7u" eventType="touchUpInside" id="L54-fI-SPn"/>
                    </connections>
                </button>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bRv-Jk-7O6" userLabel="TopBarView">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="92"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0Rq-ZI-bPE">
                            <rect key="frame" x="16" y="56" width="88" height="24"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="88" id="LFo-Iy-VRA"/>
                                <constraint firstAttribute="height" constant="24" id="ta5-hV-nul"/>
                            </constraints>
                            <state key="normal" title="Cancel">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="didTapCancel" destination="dvW-jC-M7u" eventType="touchUpInside" id="Tb8-3a-xga"/>
                            </connections>
                        </button>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="kDI-vt-OL6"/>
                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.20000000000000001" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="0Rq-ZI-bPE" secondAttribute="bottom" constant="12" id="f8b-dD-Ah8"/>
                        <constraint firstItem="0Rq-ZI-bPE" firstAttribute="leading" secondItem="bRv-Jk-7O6" secondAttribute="leading" constant="16" id="kyi-qL-j8D"/>
                        <constraint firstAttribute="height" constant="92" id="rdf-G3-RZU"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="or8-Rz-UU2"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Z0V-Dg-hKS" firstAttribute="centerX" secondItem="dvW-jC-M7u" secondAttribute="centerX" id="7c1-8L-PLw"/>
                <constraint firstItem="or8-Rz-UU2" firstAttribute="bottom" secondItem="Z0V-Dg-hKS" secondAttribute="bottom" constant="48" id="TpQ-PJ-AFp"/>
                <constraint firstItem="bRv-Jk-7O6" firstAttribute="top" secondItem="dvW-jC-M7u" secondAttribute="top" id="dKc-z7-9f4"/>
                <constraint firstItem="bRv-Jk-7O6" firstAttribute="leading" secondItem="or8-Rz-UU2" secondAttribute="leading" id="fxC-it-jiP"/>
                <constraint firstItem="bRv-Jk-7O6" firstAttribute="trailing" secondItem="or8-Rz-UU2" secondAttribute="trailing" id="lk3-k1-uyI"/>
            </constraints>
            <connections>
                <outlet property="cancelButton" destination="0Rq-ZI-bPE" id="ig8-Kw-JR9"/>
                <outlet property="topBarView" destination="bRv-Jk-7O6" id="ZaV-uG-Gm7"/>
                <outlet property="topBarViewHeightConstraint" destination="rdf-G3-RZU" id="iXZ-eJ-Pco"/>
            </connections>
            <point key="canvasLocation" x="-230" y="49"/>
        </view>
    </objects>
</document>
