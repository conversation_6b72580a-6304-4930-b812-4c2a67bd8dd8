import Foundation
import UIKit

class TBImageCropperHelper: NSObject {
    class func minimumScale(from size: CGSize, toFitTargetSize targetSize: CGSize) -> CGFloat {
        let widthScale = targetSize.width / size.width
        let heightScale = targetSize.height / size.height
        return (widthScale > heightScale) ? widthScale : heightScale
    }
    
    class func cropImage(image: UIImage?, from scrollView: UIScrollView?, with size: CGSize) -> UIImage? {
        let x: CGFloat = ((scrollView?.contentOffset.x ?? 0.0) / (scrollView?.zoomScale ?? 0.0)) + ((scrollView?.contentInset.left ?? 0.0) / (scrollView?.zoomScale ?? 0.0))
        let y: CGFloat = ((scrollView?.contentOffset.y ?? 0.0) / (scrollView?.zoomScale ?? 0.0)) +  ((scrollView?.contentInset.top ?? 0.0) / (scrollView?.zoomScale ?? 0.0))
        let cropRect = CGRect(x: x, y: y, width: size.width / (scrollView?.zoomScale ?? 0.0), height: size.height / scrollView!.zoomScale)
                
        return cropImage(image, with: cropRect)
    }
    
    class func cropImage(_ image: UIImage?, with rect: CGRect) -> UIImage? {
        var checkedImage = image
        if image?.imageOrientation != .up {
            UIGraphicsBeginImageContextWithOptions(image?.size ?? CGSize.zero, false, image?.scale ?? 0.0)
            image?.draw(in: CGRect(x: 0, y: 0, width: image?.size.width ?? 0.0, height: image?.size.height ?? 0.0))
            checkedImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
        }
        
        let imageRef = checkedImage?.cgImage?.cropping(to: rect)
        var resultImage: UIImage?
        if let imageRef,
           let scale = checkedImage?.scale,
           let orientation = checkedImage?.imageOrientation {
            resultImage = UIImage(cgImage: imageRef,
                                  scale: scale,
                                  orientation: orientation)
        }
        
        return resultImage
    }
}
