//
//  ConfirmErrorPopupViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/3.
//

import UIKit

protocol ConfirmErrorPopupViewControllerDelegate: AnyObject {
    func didTapConfirm()
}

class ConfirmErrorPopupViewController: UIViewController {

    let popupBgView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    let titleLabel: UILabel = {
        let l = UILabel()
        l.text = "Helpful Tips"
        l.textColor = .mainTextColor
        l.font = .mediumGilroyFont(18)
        l.textAlignment = .center
        return l
    }()
    
    let resultLabel: UILabel = {
        let l = UILabel()
        l.textColor = .mainTextColor
        l.font = .mediumGilroyFont(16)
        l.textAlignment = .center
        l.numberOfLines = 0
        return l
    }()
    
    lazy var backButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Confirm", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = .boldGilroyFont(16)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.addTarget(self, action: #selector(didTapBack), for: .touchUpInside)
        return b
    }()
    
    weak var delegate: ConfirmErrorPopupViewControllerDelegate?
    
    init(errorMessage: String, delegate: ConfirmErrorPopupViewControllerDelegate?) {
        super.init(nibName: nil, bundle: nil)
        
        self.delegate = delegate
        self.resultLabel.text = errorMessage
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .black.withAlphaComponent(0.3)
        setupUI()
    }
    
    @objc func didTapBack() {
        dismiss(animated: true)
        self.delegate?.didTapConfirm()
    }
    
    private func setupUI() {
        view.addSubview(popupBgView)
        popupBgView.snp.makeConstraints { make in
            make.height.equalTo(292)
            make.left.right.equalToSuperview().inset(40)
            make.center.equalToSuperview()
        }
        
        [titleLabel, resultLabel, backButton].forEach(popupBgView.addSubview)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(40)
            make.centerX.equalToSuperview()
        }
        
        resultLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(30)
        }
        
        backButton.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
        }
    }
}
