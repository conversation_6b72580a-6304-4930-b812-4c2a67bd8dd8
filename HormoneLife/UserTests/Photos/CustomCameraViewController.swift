import UIKit
import Photos

// MARK: - Protocol
protocol CustomCameraViewControllerDelegate: AnyObject {
    func didFinishPickingImage(_ cameraController: CustomCameraViewController, image: UIImage)
    func didCancel(cameraController: CustomCameraViewController)
}

extension CustomCameraViewControllerDelegate {
    func didCancel(cameraController: CustomCameraViewController) {}
}

class CustomCameraViewController: BaseViewController, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    
    // MARK: - Constants
    private let kImageCompressionQuality: CGFloat = 1//0.5
    private let kImageMaxWidth: CGFloat = 800
    private let kCameraTransitionDuration: TimeInterval = 0.2
    
    // MARK: - Properties
    var image: UIImage?
    var pageType: String?
    var paperType: TestType?
    var selectDate: Date?
    var reqBody: TestImageReqBody?
    private var isFromRootPage: Bool = true  // 添加标记，区分是从首页进入还是从裁剪页返回
    
    // MARK: - View Models
    lazy var fileViewModel = UploadFileViewModel()
    let viewModel = UserTestViewModel()
    
    // MARK: - UI Components
    let takingPicture = UIImagePickerController()
    
    // MARK: - Camera Controllers
    var imageCropperController: ImageCropperViewController?
    var fullScreenCameraController: FullScreenCameraViewController?
    
    // MARK: - Private Properties
    private var currentViewController: UIViewController?
    weak var delegate: CustomCameraViewControllerDelegate?

    // 【对比测试】reqBody 获取重试计数，防止无限递归
    private var reqBodyFetchAttempts: Int = 0
    
    // MARK: - Image Processing
    func saveImageToPhotosAlbum(_ image: UIImage) {
        print("\n=== Saving Image to Album ===")
        print("Image size to save: \(image.size)")
        
        let status = PHPhotoLibrary.authorizationStatus()
        if status == .notDetermined {
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                if status == .authorized {
                    self?.performSaveImage(image)
                } else {
                    print("Photo library access denied")
                }
            }
        } else if status == .authorized {
            performSaveImage(image)
        } else {
            print("Photo library access not available")
        }
    }
    
    // MARK: - Result Handling
    private func handleHCGResult(url: String) {
        var detailModel = TestPaperDetail()
        var testModel = UserTestResultPageModel()
        testModel.type = "HCG"
        detailModel.isCreate = true
        detailModel.imageUrl = url
        // 没有ID, ...
        let vc = ResultViewController(testDetail: detailModel, circleViewType: .HCG, delegate: nil)
        vc.isCreate = true
        vc.testResult = testModel
        navigationController?.pushViewController(vc, animated: true)
    }
    
    func confirmResultPopupShow(testResult: PhotoToAmazon, imageUrl: String, value: Double?) {
        let popupView = ConfirmResultPopupViewController(paperType: self.pageType, testResult: testResult, imageUrl: imageUrl, value: value ?? 0.0, delegate: self)
        popupView.selectDate = self.selectDate
        present(popupView, animated: true)
    }
    
    // MARK: - Computed Properties
    var showImageCropperController: ImageCropperViewController {
        get {
            if let vc = self.imageCropperController {
                return vc
            } else {
                self.imageCropperController = ImageCropperViewController()
                self.imageCropperController?.scanViewWidth = self.showFullScreenCameraController.getScanViewWidth()
                self.imageCropperController?.cropOffsetWidth = self.showFullScreenCameraController.getCropOffsetWidth()
                self.imageCropperController?.scanViewLeftMargin = self.showFullScreenCameraController.getScanViewLeftMargin()
                self.imageCropperController!.delegate = self
                return self.imageCropperController!
            }
        }
    }
    
    var showFullScreenCameraController: FullScreenCameraViewController {
        get {
            if let vc = self.fullScreenCameraController {
                return vc
            } else {
                self.fullScreenCameraController = FullScreenCameraViewController()
                self.fullScreenCameraController!.delegate = self
                self.fullScreenCameraController?.actionDelegate = self
                return self.fullScreenCameraController!
            }
        }
    }
    
    // MARK: - Lifecycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        coldStartText()
        getWonfoReqBody()
        isFromRootPage = true  // 重置标记
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setupCamera()
        navigationController?.isNavigationBarHidden = true
        
        // 【对比测试】已禁用自动拍照功能，以下逻辑被注释以保留手动拍照体验
//        if isFromRootPage {
//            showFullScreenCameraController.isAutoCatchImage = true
//            CaptureStateManager.shared.resetAutoCapture()
//        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        navigationController?.isNavigationBarHidden = false
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        navigationItem.title = "Record Your Tests"
        view.backgroundColor = .themeColor
    }
    
    private func setupCamera() {
        self.addChild(self.showFullScreenCameraController)
        self.view.addSubview(self.showFullScreenCameraController.view)
        self.showFullScreenCameraController.didMove(toParent: self)
    }
    
    func startCropperImage(image: UIImage) {
        // To Image cropper vc
        showImageCropperController.image = image
        
        // 标记不是从首页进入
        isFromRootPage = false
        
        // 先将 FullScreenCameraController 的视图移除
        showFullScreenCameraController.willMove(toParent: nil)
        showFullScreenCameraController.view.removeFromSuperview()
        showFullScreenCameraController.removeFromParent()
        
        // 添加 ImageCropperController
        addChild(showImageCropperController)
        view.addSubview(showImageCropperController.view)
        showImageCropperController.view.frame = view.bounds
        showImageCropperController.didMove(toParent: self)
        
        // 更新当前视图控制器引用
        self.fullScreenCameraController = nil
        self.currentViewController = self.imageCropperController
        self.navigationController?.isNavigationBarHidden = true
    }
    
    // MARK: - Image Upload
    func autoUploadImage(image: UIImage) {
        print("\n=== Image Upload Process ===")
        print("1. Starting image process")
        
        // Check if auto mode
        let isAutoMode = self.fullScreenCameraController?.isAutoCatchImage == true
        print("2. Mode: \(isAutoMode ? "Auto capture" : "Manual capture")")
        
        // 手动模式进入裁剪界面
        if !isAutoMode {
            startCropperImage(image: image)
            return
        }
        
        // 使用与手动拍照相同的图片处理逻辑
        uploadImage(image: image)
    }
    
    // MARK: - Error Handling
    private func handleValidationError(_ errorCode: Int?) -> String? {
        
        var message = "Align the strip between lines, then press capture."
        switch errorCode {
        case 1:
            message = "The max and control lines are not aligned. Please align the max and control lines with the two yellow reference lines."
        case 2:
            message = "The environmental light is too weak. Please capture the image in a brighter location."
        case 3:
            message = "The light is too bright. Please take the picture in an area with moderate lighting."
        case 4:
            message = "The image is blurry. Please focus to get a clear image."
        case 5:
            message = "The strip is not centered. Please align the strip in the center."
        default:
            break
        }
        
        print("Validation error \(errorCode ?? -1): \(message)")
        //if self.fullScreenCameraController == nil { showToachMessage(message: message) }
        return message
    }
    
    // MARK: - Image Processing
    private func performSaveImage(_ image: UIImage) {
        DispatchQueue.main.async {
            PHPhotoLibrary.shared().performChanges({
                PHAssetChangeRequest.creationRequestForAsset(from: image)
            }) { completed, error in
                if completed {
                    print("Successfully saved image to album")
                } else if let error = error {
                    print("Failed to save image: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - Amazon Upload
    private func uploadToAmazon(reqB: TestImageReqBody, image: UIImage) {
        viewModel.uploadPhotoToAmazon(reqBody: reqB, success: { [weak self] amazonResult in
            hideActivityHUD()
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                
                // Validate analysis result
                guard let testResult = amazonResult,
                      let error = testResult.error,
                      error == 0 else {
                    let errorMessage = self.handleValidationError(amazonResult?.error)
                    if let fullVC = self.fullScreenCameraController {
                        //self.saveImageToPhotosAlbum(image)
                        fullVC.retryCapture(with: errorMessage ?? "")
                    } else {
                        self.confirmErrorPopupShow(errorMessage: errorMessage ?? "")
                        // 错误处理完成后标记状态为错误
                        CaptureStateManager.shared.handleError()
                    }
                    return
                }
                
                // 成功分析：停止自动拍照，后续仅允许手动
                self.fullScreenCameraController?.stopAutoCapture()
                // 完成拍照流程
                CaptureStateManager.shared.completeCapture()

                // Upload image to server
                self.uploadToServer(image: image, testResult: testResult)
            }
        }) { [weak self] error in
            DispatchQueue.main.async {
                hideActivityHUD()
                print("Amazon analysis failed: \(error?.description ?? "Unknown error")")
                
                // 处理错误状态
                CaptureStateManager.shared.handleError()
                
                if let errorCode = Int(error ?? "0"),
                   let self = self,
                   let errorMessage = self.handleValidationError(errorCode) {
                    if let fullVC = self.fullScreenCameraController {
                        fullVC.retryCapture(with: errorMessage)
                    } else {
                        self.confirmErrorPopupShow(errorMessage: errorMessage)
                    }
                }
            }
        }
    }
    
    private func confirmErrorPopupShow(errorMessage: String) {
        let popupView = ConfirmErrorPopupViewController(errorMessage: errorMessage, delegate: nil)
        popupView.modalTransitionStyle = .crossDissolve
        present(popupView, animated: true)
    }
    
    private func uploadToServer(image: UIImage, testResult: PhotoToAmazon) {
        
        fileViewModel.uploadImages(images: image) { [weak self] url, filename in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                hideActivityHUD()
                
                // Handle HCG type
                if self.paperType == .HCG {
                    self.handleHCGResult(url: url)
                    return
                }
                
                // Show result confirmation popup
                self.confirmResultPopupShow(testResult: testResult, imageUrl: url, value: testResult.result?.value)
            }
        } failure: { error in
            DispatchQueue.main.async {
                hideActivityHUD()
                print("Image upload failed: \(error?.description ?? "Unknown error")")
            }
        }
    }
    
    // MARK: - ImageCropperViewControllerDelegate
    func imageCropperControllerDidRetake() {
        performAlphaTransition(from: showImageCropperController, to: showFullScreenCameraController, withDuration: kCameraTransitionDuration) {
            self.navigationController?.isNavigationBarHidden = true
            self.showImageCropperController.removeFromParent()
            self.imageCropperController = nil
            
            self.reStartAutoCapture()
        }
    }
    
    func reStartAutoCapture() {
        // 【对比测试】自动拍照功能已禁用，此方法留空以兼容现有调用路径，仅保留手动拍照
    }
    
    // MARK: - Network Requests
    func coldStartText() {
        SystemInteractor.coldStart(params: nil) { result in
        } failure: { error in
        }
    }
    
    func getWonfoReqBody() {
        guard let type = pageType else { return }
        
        viewModel.getWonfoReqBody(by: type) { [weak self] result in
            self?.reqBody = result
            hideActivityHUD()
        } failure: { _ in
            hideActivityHUD()
        }
    }
    
    // MARK: - Manual Image Upload
    // 修改 uploadImage 方法，避免递归调用导致的循环问题
    func uploadImage(image: UIImage) {
        print("\n=== Manual Mode Image Processing ===")
        print("1. Original image size: \(image.size)")
        
        // Compress image
        let imageData = image.jpegData(compressionQuality: kImageCompressionQuality)!
        print("2. Compressed image data size: \(imageData.count) bytes")
        
        let newImage = resizeImage(image, newWidth: kImageMaxWidth) ?? (UIImage(data: imageData) ?? image)
        print("3. Resized image size: \(newImage.size)")
        
        // Continue upload process in normal mode
        DispatchQueue.main.async {
           showActivityHUD("Analyzing image...")
        }
        
        // Prepare upload request
        guard var reqB = self.reqBody,
              let newImageData = newImage.jpegData(compressionQuality: kImageCompressionQuality) else {
            print("【上传流程】reqBody 为空，尝试重新获取 WonfoReqBody…")

            // 防止无限递归：最多重试 3 次
            if reqBodyFetchAttempts >= 3 {
                hideActivityHUD()
                showErrorAlert(message: "Failed to get validation parameters. Please check your network and try again.")
                return
            }

            reqBodyFetchAttempts += 1
            CaptureStateManager.shared.handleError()
            
            getWonfoReqBody()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) { [weak self] in
                guard let self = self else { return }
                self.uploadImage(image: newImage)
            }
            return
        }

        // 成功获取 reqBody，重置重试计数
        reqBodyFetchAttempts = 0

        reqB.imgBase64 = newImageData.base64EncodedString()
        uploadToAmazon(reqB: reqB, image: newImage)
    }
    
    // 简化错误提示方法，去掉重试选项
    private func showErrorAlert(message: String) {
        let alert = UIAlertController(
            title: "Upload Failed",
            message: message,
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        
        present(alert, animated: true)
    }
    
    // MARK: - FullScreenCameraViewControllerDelegate
    func chooseFromGallery() {
        // 检查相册权限
        let status = PHPhotoLibrary.authorizationStatus()
        switch status {
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                DispatchQueue.main.async {
                    if status == .authorized {
                        self?.presentImagePicker()
                    } else {
                        self?.showPhotoLibraryPermissionAlert()
                    }
                }
            }
        case .authorized:
            presentImagePicker()
        case .denied, .restricted:
            showPhotoLibraryPermissionAlert()
        @unknown default:
            showPhotoLibraryPermissionAlert()
        }
    }
    
    private func presentImagePicker() {
        takingPicture.navigationController?.isNavigationBarHidden = true
        takingPicture.sourceType = .photoLibrary
        takingPicture.delegate = self
        takingPicture.allowsEditing = false
        takingPicture.modalPresentationStyle = .overFullScreen
        
        // 确保在主线程中展示
        DispatchQueue.main.async { [weak self] in
            self?.present(self?.takingPicture ?? UIImagePickerController(), animated: true)
        }
    }
    
    private func showPhotoLibraryPermissionAlert() {
        let alert = UIAlertController(
            title: "Photo Library Access Required",
            message: "Please allow photo library access in Settings to select photos",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Settings", style: .default) { _ in
            if let url = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(url)
            }
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        // 确保在主线程中展示
        DispatchQueue.main.async { [weak self] in
            self?.present(alert, animated: true)
        }
    }
    
    // MARK: - UIImagePickerControllerDelegate & UINavigationControllerDelegate
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        //delegate?.didCancel(cameraController: self)
        picker.dismiss(animated: true) {
            self.reStartAutoCapture()
        }
    }
    
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
        // 先关闭选择器
        picker.dismiss(animated: true) { [weak self] in
            // 在关闭后处理图片
            if let image = info[.originalImage] as? UIImage {
                self?.autoUploadImage(image: image)
            }
        }
    }
}

// MARK: - ImageCropperViewControllerDelegate
extension CustomCameraViewController: ImageCropperViewControllerDelegate {
    func didFinishEditingWithImage(editedImage: UIImage?) {
        guard let image = editedImage else { return }
        showActivityHUD("Analyzing image...")
        // Save user adjusted image in demo mode
        
        // Use FullScreenCameraViewController's crop logic
        if let croppedImage = self.fullScreenCameraController?.cropAndProcessImage(image, autoProcess: false) {
            print("2. Saving user adjusted cropped image: \(croppedImage.size)")
            saveImageToPhotosAlbum(croppedImage)
            
            // Process and compress
            let imageData = croppedImage.jpegData(compressionQuality: kImageCompressionQuality)!
            let processedImage = resizeImage(croppedImage, newWidth: kImageMaxWidth) ?? (UIImage(data: imageData) ?? croppedImage)
            print("3. Saving final processed image: \(processedImage.size)")
            saveImageToPhotosAlbum(processedImage)
            
            // Return in demo mode
            DispatchQueue.main.async {
                hideActivityHUD()
                self.navigationController?.popViewController(animated: true)
            }
            return
        }
        
        uploadImage(image: image)
    }
}

// MARK: - FullScreenCameraViewControllerDelegate
extension CustomCameraViewController: FullScreenCameraViewControllerDelegate {
    func didCaptureImage(_ image: UIImage) {
        print("\n=== Manual Photo Capture ===")
        print("1. Received captured image: \(image.size)")
        
        // Start upload process
        print("2. Starting upload process...")
        self.autoUploadImage(image: image)
    }
    
    func didReceiveValidationError() {
        // This method is required by the protocol but not used in current implementation
        // as we handle validation errors directly in CustomCameraViewController
    }
}

// MARK: - ConfirmResultPopupViewControllerDelegate
extension CustomCameraViewController: ConfirmResultPopupViewControllerDelegate {
    func saveSuccess(testResultId: String) {
        if paperType == .HCG { // if is HCG, won't have popupView, so won't jump into here.
            guard !testResultId.isEmpty else { return }
            //showActivityHUD("Analyzing image...")
            viewModel.getTestDetail(resultId: testResultId) { detail in
                hideActivityHUD()
                guard let testDetail = detail else { return }
                let vc = ResultViewController(testDetail: testDetail, circleViewType: .HCG, delegate: nil)
                vc.hidesBottomBarWhenPushed = true
                self.navigationController?.pushViewController(vc, animated: true)
            }
        } else {
            navigationController?.popToRootViewController(animated: false)
            if let vc = getKeyWindow()?.rootViewController as? TabBarController {
                vc.selectedIndex = 1
                let noti = Notification(name: Notification.Name("refreshHistoryRecordData"), userInfo: ["paperType": pageType ?? ""])
                NotificationCenter.default.post(noti)
            }
        }
    }
    
    func didBack() {
        self.fullScreenCameraController?.resetCaptureState()
    }
}
