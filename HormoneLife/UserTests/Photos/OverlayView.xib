<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string><PERSON><PERSON>-Medium</string>
        </array>
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="dvW-jC-M7u" customClass="OverlayView" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9Yu-tU-XEg">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="96"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Mly-L7-Oow">
                            <rect key="frame" x="18" y="60" width="18" height="18"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="18" id="CUQ-4o-Bqg" userLabel="height = 30"/>
                                <constraint firstAttribute="width" constant="18" id="h3m-Fl-dY0" userLabel="width = 30"/>
                            </constraints>
                            <state key="normal" title="Button"/>
                            <buttonConfiguration key="configuration" style="plain" image="backIcon" title=" ">
                                <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </buttonConfiguration>
                            <connections>
                                <action selector="didTapBackButton:" destination="dvW-jC-M7u" eventType="touchUpInside" id="zC2-3R-WRS"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Record Your Tests" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OkW-vn-EHw">
                            <rect key="frame" x="99.5" y="56" width="176" height="26"/>
                            <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="22"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemYellowColor"/>
                    <constraints>
                        <constraint firstItem="OkW-vn-EHw" firstAttribute="centerY" secondItem="Mly-L7-Oow" secondAttribute="centerY" id="QbX-HS-Psh"/>
                        <constraint firstAttribute="height" constant="96" id="TfL-ST-SqO"/>
                        <constraint firstAttribute="bottom" secondItem="Mly-L7-Oow" secondAttribute="bottom" constant="18" id="VYz-lt-ZCp"/>
                        <constraint firstItem="OkW-vn-EHw" firstAttribute="centerX" secondItem="9Yu-tU-XEg" secondAttribute="centerX" id="ekp-32-Ltx"/>
                        <constraint firstItem="Mly-L7-Oow" firstAttribute="leading" secondItem="9Yu-tU-XEg" secondAttribute="leading" constant="18" id="rMN-5b-L5e"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bRv-Jk-7O6" userLabel="TopBarView">
                    <rect key="frame" x="0.0" y="96" width="375" height="190"/>
                    <subviews>
                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please align with the C line and the Max line." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OzK-hO-uK5">
                            <rect key="frame" x="20" y="20" width="335" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="NiH-26-GSH"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kRp-2f-zHj">
                            <rect key="frame" x="20" y="108" width="335" height="26"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yxG-fl-Doi">
                                    <rect key="frame" x="69.5" y="0.0" width="4" height="26"/>
                                    <color key="backgroundColor" red="0.1925634742" green="0.057495601479999998" blue="0.35657423729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="4" id="z9Z-qs-OH7"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ci9-Zt-ebd">
                                    <rect key="frame" x="169.5" y="0.0" width="4" height="26"/>
                                    <color key="backgroundColor" red="0.85906678439999995" green="0.65510600809999997" blue="0.65098381039999997" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="4" id="4IG-SU-V4o"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BeS-WZ-iKu">
                                    <rect key="frame" x="194.5" y="0.0" width="4" height="26"/>
                                    <color key="backgroundColor" red="0.85005241629999995" green="0.4437052608" blue="0.43138396740000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="4" id="8AO-g3-Kcq"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tBa-Wd-8OO">
                                    <rect key="frame" x="219.5" y="0.0" width="115.5" height="26"/>
                                    <color key="backgroundColor" red="0.83267831800000003" green="0.72171771529999995" blue="0.44382631779999998" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                </view>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="maxTitleImage" translatesAutoresizingMaskIntoConstraints="NO" id="2iO-yl-Yjx">
                                    <rect key="frame" x="85.5" y="-3" width="32" height="32"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="32" id="4r8-9D-dcL"/>
                                        <constraint firstAttribute="width" constant="32" id="Uzb-RT-iZe"/>
                                    </constraints>
                                </imageView>
                                <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pinkBoarderIcon" translatesAutoresizingMaskIntoConstraints="NO" id="cga-p1-I7o">
                                    <rect key="frame" x="148.5" y="-13" width="92" height="52"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="52" id="Opv-TZ-FIr"/>
                                        <constraint firstAttribute="width" constant="92" id="s0M-wd-daL"/>
                                    </constraints>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Max" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vtj-7s-swt">
                                    <rect key="frame" x="50.5" y="-34" width="42" height="21"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="21" id="j0D-39-3IF"/>
                                        <constraint firstAttribute="width" constant="42" id="tQa-ek-hbg"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="T" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="74d-DX-6He">
                                    <rect key="frame" x="150.5" y="-34" width="42" height="21"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="42" id="ein-Eo-HJx"/>
                                        <constraint firstAttribute="height" constant="21" id="ulC-AY-Ali"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="C" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cYi-6H-hV9">
                                    <rect key="frame" x="175.5" y="-34" width="42" height="21"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="42" id="5LJ-ah-myC"/>
                                        <constraint firstAttribute="height" constant="21" id="qMB-bp-4Ay"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="tBa-Wd-8OO" secondAttribute="bottom" id="0L4-rm-2Kr"/>
                                <constraint firstItem="tBa-Wd-8OO" firstAttribute="leading" secondItem="BeS-WZ-iKu" secondAttribute="trailing" constant="21" id="140-Gz-2mn"/>
                                <constraint firstItem="ci9-Zt-ebd" firstAttribute="centerX" secondItem="kRp-2f-zHj" secondAttribute="centerX" constant="4" id="1eb-Bp-DOp"/>
                                <constraint firstItem="tBa-Wd-8OO" firstAttribute="top" secondItem="kRp-2f-zHj" secondAttribute="top" id="1x5-9Q-AhW"/>
                                <constraint firstItem="Vtj-7s-swt" firstAttribute="centerX" secondItem="yxG-fl-Doi" secondAttribute="centerX" id="2Bw-ej-8vD"/>
                                <constraint firstItem="74d-DX-6He" firstAttribute="centerX" secondItem="ci9-Zt-ebd" secondAttribute="centerX" id="6QF-Xq-6fm"/>
                                <constraint firstItem="BeS-WZ-iKu" firstAttribute="top" secondItem="kRp-2f-zHj" secondAttribute="top" id="71f-Io-Oyz"/>
                                <constraint firstAttribute="bottom" secondItem="yxG-fl-Doi" secondAttribute="bottom" id="MC7-td-dVB"/>
                                <constraint firstItem="cYi-6H-hV9" firstAttribute="centerX" secondItem="BeS-WZ-iKu" secondAttribute="centerX" id="ONL-Eb-9iP"/>
                                <constraint firstAttribute="height" constant="26" id="QZl-El-Npm"/>
                                <constraint firstItem="2iO-yl-Yjx" firstAttribute="centerY" secondItem="kRp-2f-zHj" secondAttribute="centerY" id="TSE-pb-pNK"/>
                                <constraint firstItem="74d-DX-6He" firstAttribute="top" secondItem="ci9-Zt-ebd" secondAttribute="top" constant="-34" id="X1i-nN-dEJ"/>
                                <constraint firstItem="2iO-yl-Yjx" firstAttribute="leading" secondItem="yxG-fl-Doi" secondAttribute="trailing" constant="12" id="YDp-AH-wuS"/>
                                <constraint firstAttribute="bottom" secondItem="ci9-Zt-ebd" secondAttribute="bottom" id="bev-WX-fp9"/>
                                <constraint firstItem="Vtj-7s-swt" firstAttribute="top" secondItem="yxG-fl-Doi" secondAttribute="top" constant="-34" id="cAZ-Fm-fIq"/>
                                <constraint firstItem="yxG-fl-Doi" firstAttribute="top" secondItem="kRp-2f-zHj" secondAttribute="top" id="eXI-7U-wLU"/>
                                <constraint firstAttribute="bottom" secondItem="BeS-WZ-iKu" secondAttribute="bottom" id="fOb-Tk-gDn"/>
                                <constraint firstItem="ci9-Zt-ebd" firstAttribute="leading" secondItem="yxG-fl-Doi" secondAttribute="trailing" constant="96" id="fRb-A3-GP0"/>
                                <constraint firstAttribute="trailing" secondItem="tBa-Wd-8OO" secondAttribute="trailing" id="gz7-Qm-Zne"/>
                                <constraint firstItem="BeS-WZ-iKu" firstAttribute="leading" secondItem="ci9-Zt-ebd" secondAttribute="trailing" constant="21" id="ihX-eu-76i"/>
                                <constraint firstItem="cga-p1-I7o" firstAttribute="centerY" secondItem="kRp-2f-zHj" secondAttribute="centerY" id="kAW-hm-51R"/>
                                <constraint firstItem="ci9-Zt-ebd" firstAttribute="top" secondItem="kRp-2f-zHj" secondAttribute="top" id="kkX-ZL-aRW"/>
                                <constraint firstItem="cYi-6H-hV9" firstAttribute="top" secondItem="BeS-WZ-iKu" secondAttribute="top" constant="-34" id="pne-wd-HPJ"/>
                                <constraint firstItem="cga-p1-I7o" firstAttribute="centerX" secondItem="BeS-WZ-iKu" secondAttribute="centerX" constant="-2" id="sTc-Cs-9ek"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="kDI-vt-OL6"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="kDI-vt-OL6" firstAttribute="trailing" secondItem="kRp-2f-zHj" secondAttribute="trailing" constant="20" id="Hz5-vd-8uN"/>
                        <constraint firstItem="OzK-hO-uK5" firstAttribute="leading" secondItem="kDI-vt-OL6" secondAttribute="leading" constant="20" id="JD9-g8-mA0"/>
                        <constraint firstItem="kDI-vt-OL6" firstAttribute="trailing" secondItem="OzK-hO-uK5" secondAttribute="trailing" constant="20" id="S4C-en-4bJ"/>
                        <constraint firstItem="OzK-hO-uK5" firstAttribute="top" secondItem="kDI-vt-OL6" secondAttribute="top" constant="20" id="VsA-Qt-GO3"/>
                        <constraint firstAttribute="height" constant="190" id="rdf-G3-RZU"/>
                        <constraint firstAttribute="bottom" secondItem="kRp-2f-zHj" secondAttribute="bottom" constant="56" id="wV2-G6-EFY"/>
                        <constraint firstItem="kRp-2f-zHj" firstAttribute="leading" secondItem="kDI-vt-OL6" secondAttribute="leading" constant="20" id="yOo-x6-aEC"/>
                    </constraints>
                </view>
                <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="baseRedLine" translatesAutoresizingMaskIntoConstraints="NO" id="BHW-5H-j2K">
                    <rect key="frame" x="92.5" y="244" width="1" height="192"/>
                    <color key="backgroundColor" systemColor="systemYellowColor"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="1" id="i3b-PR-UKb"/>
                    </constraints>
                </imageView>
                <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="baseRedLine" translatesAutoresizingMaskIntoConstraints="NO" id="PUu-KW-1kE" userLabel="baseTRedLine">
                    <rect key="frame" x="217.5" y="244" width="1" height="192"/>
                    <color key="backgroundColor" systemColor="systemYellowColor"/>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" id="fFc-NB-WGh"/>
                        <constraint firstAttribute="width" constant="1" id="i8Y-wJ-eWm"/>
                    </constraints>
                </imageView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YSQ-PD-gY0" userLabel="BottomBarView">
                    <rect key="frame" x="0.0" y="436" width="375" height="231"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Place the test strip on a clean, white background. " textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rep-GT-4Kn">
                            <rect key="frame" x="20" y="30" width="335" height="42"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="18"/>
                            <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.59999999999999998" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="After pressing capture, you'll still be able to adjust the image (zoom, rotate, or move) for precise alignment." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9IE-2t-8D2">
                            <rect key="frame" x="20" y="82" width="335" height="63"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="18"/>
                            <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nhZ-k7-bPG">
                            <rect key="frame" x="87.5" y="274" width="200" height="36"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="200" id="Gdr-5X-gv4"/>
                                <constraint firstAttribute="height" constant="36" id="huR-4L-lbe"/>
                            </constraints>
                            <state key="normal" title="Button"/>
                            <buttonConfiguration key="configuration" style="plain">
                                <backgroundConfiguration key="background" cornerRadius="4"/>
                            </buttonConfiguration>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.borderWidth">
                                    <integer key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="layer.borderColor">
                                    <color key="value" red="0.015686274510000001" green="0.20392156859999999" blue="0.20000000000000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="chooseFromGalleryAction:" destination="dvW-jC-M7u" eventType="touchUpInside" id="ZLc-wc-tee"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ioX-g8-J7f" userLabel="camera">
                            <rect key="frame" x="155.5" y="165" width="64" height="64"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="64" id="Gaj-Kt-wIv"/>
                                <constraint firstAttribute="width" constant="64" id="em9-Hz-kMk"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="scanCamera"/>
                            <connections>
                                <action selector="didTapTakePhoto" destination="dvW-jC-M7u" eventType="touchUpInside" id="rcF-76-xiT"/>
                            </connections>
                        </button>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="hMD-al-yvB"/>
                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstItem="Rep-GT-4Kn" firstAttribute="top" secondItem="YSQ-PD-gY0" secondAttribute="top" constant="30" id="1EA-rc-yvt"/>
                        <constraint firstItem="9IE-2t-8D2" firstAttribute="leading" secondItem="hMD-al-yvB" secondAttribute="leading" constant="20" id="5ov-5y-AYf"/>
                        <constraint firstItem="nhZ-k7-bPG" firstAttribute="top" secondItem="ioX-g8-J7f" secondAttribute="bottom" constant="45" id="Fct-V1-H7Z"/>
                        <constraint firstItem="ioX-g8-J7f" firstAttribute="top" secondItem="9IE-2t-8D2" secondAttribute="bottom" constant="20" id="UmA-n9-HV9"/>
                        <constraint firstItem="hMD-al-yvB" firstAttribute="trailing" secondItem="Rep-GT-4Kn" secondAttribute="trailing" constant="20" id="Vxb-IB-iAI"/>
                        <constraint firstItem="9IE-2t-8D2" firstAttribute="top" secondItem="Rep-GT-4Kn" secondAttribute="bottom" constant="10" id="eRP-Dx-dWB"/>
                        <constraint firstItem="nhZ-k7-bPG" firstAttribute="centerX" secondItem="YSQ-PD-gY0" secondAttribute="centerX" id="gs9-M6-BOP"/>
                        <constraint firstItem="ioX-g8-J7f" firstAttribute="centerX" secondItem="YSQ-PD-gY0" secondAttribute="centerX" id="iE0-Y8-Uuv"/>
                        <constraint firstItem="hMD-al-yvB" firstAttribute="trailing" secondItem="9IE-2t-8D2" secondAttribute="trailing" constant="20" id="wf7-0L-FuN"/>
                        <constraint firstItem="Rep-GT-4Kn" firstAttribute="leading" secondItem="hMD-al-yvB" secondAttribute="leading" constant="20" id="yMR-yS-e0m"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="or8-Rz-UU2"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="PUu-KW-1kE" firstAttribute="top" secondItem="BeS-WZ-iKu" secondAttribute="top" constant="40" id="3nb-uT-meM"/>
                <constraint firstItem="9Yu-tU-XEg" firstAttribute="leading" secondItem="or8-Rz-UU2" secondAttribute="leading" id="4ch-RY-HeV"/>
                <constraint firstItem="YSQ-PD-gY0" firstAttribute="top" secondItem="PUu-KW-1kE" secondAttribute="bottom" id="4ts-bY-6tw"/>
                <constraint firstItem="BHW-5H-j2K" firstAttribute="top" secondItem="yxG-fl-Doi" secondAttribute="top" constant="40" id="72S-dx-Gmj"/>
                <constraint firstItem="YSQ-PD-gY0" firstAttribute="leading" secondItem="or8-Rz-UU2" secondAttribute="leading" id="UA1-qs-59c"/>
                <constraint firstItem="YSQ-PD-gY0" firstAttribute="top" secondItem="bRv-Jk-7O6" secondAttribute="bottom" constant="150" id="X09-Ho-NIE"/>
                <constraint firstItem="PUu-KW-1kE" firstAttribute="trailing" secondItem="BeS-WZ-iKu" secondAttribute="trailing" id="YQz-T6-XgS"/>
                <constraint firstAttribute="bottom" secondItem="YSQ-PD-gY0" secondAttribute="bottom" id="bla-Yc-YYM"/>
                <constraint firstItem="bRv-Jk-7O6" firstAttribute="leading" secondItem="or8-Rz-UU2" secondAttribute="leading" id="fxC-it-jiP"/>
                <constraint firstItem="or8-Rz-UU2" firstAttribute="trailing" secondItem="9Yu-tU-XEg" secondAttribute="trailing" id="jV0-7Z-VWA"/>
                <constraint firstItem="BHW-5H-j2K" firstAttribute="trailing" secondItem="yxG-fl-Doi" secondAttribute="trailing" id="kiU-bW-R0W"/>
                <constraint firstItem="9Yu-tU-XEg" firstAttribute="top" secondItem="dvW-jC-M7u" secondAttribute="top" id="l6e-lF-Jtz"/>
                <constraint firstItem="bRv-Jk-7O6" firstAttribute="top" secondItem="9Yu-tU-XEg" secondAttribute="bottom" id="lTE-Wz-TBT"/>
                <constraint firstItem="bRv-Jk-7O6" firstAttribute="trailing" secondItem="or8-Rz-UU2" secondAttribute="trailing" id="lk3-k1-uyI"/>
                <constraint firstItem="YSQ-PD-gY0" firstAttribute="top" secondItem="BHW-5H-j2K" secondAttribute="bottom" id="zSR-Ye-0mG"/>
                <constraint firstItem="YSQ-PD-gY0" firstAttribute="trailing" secondItem="or8-Rz-UU2" secondAttribute="trailing" id="zwM-4d-gni"/>
            </constraints>
            <connections>
                <outlet property="bottomView" destination="YSQ-PD-gY0" id="XgR-t4-04n"/>
                <outlet property="cameraButton" destination="ioX-g8-J7f" id="wvx-lm-6vl"/>
                <outlet property="galleryButton" destination="nhZ-k7-bPG" id="dOu-7s-3t2"/>
                <outlet property="leftRedView" destination="BHW-5H-j2K" id="m3g-GS-ESd"/>
                <outlet property="maxBlackView" destination="yxG-fl-Doi" id="rM2-6G-9zn"/>
                <outlet property="navigationBarView" destination="9Yu-tU-XEg" id="B8R-Wh-Oo6"/>
                <outlet property="navigationTitleLabel" destination="OkW-vn-EHw" id="d1c-cm-Vdg"/>
                <outlet property="rightRedLineView" destination="PUu-KW-1kE" id="6ia-gR-KIs"/>
                <outlet property="tipImageContainerView" destination="kRp-2f-zHj" id="6fC-YM-6b1"/>
                <outlet property="titleLabel" destination="OzK-hO-uK5" id="7KJ-0T-uCI"/>
                <outlet property="topBarView" destination="bRv-Jk-7O6" id="ZaV-uG-Gm7"/>
                <outlet property="topBarViewHeightConstraint" destination="rdf-G3-RZU" id="iXZ-eJ-Pco"/>
            </connections>
            <point key="canvasLocation" x="-268" y="60.719640179910051"/>
        </view>
    </objects>
    <resources>
        <image name="backIcon" width="9" height="17"/>
        <image name="baseRedLine" width="1" height="179"/>
        <image name="maxTitleImage" width="31" height="32"/>
        <image name="pinkBoarderIcon" width="92" height="52"/>
        <image name="scanCamera" width="64" height="64"/>
        <systemColor name="systemYellowColor">
            <color red="1" green="0.80000000000000004" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
