import UIKit
import SnapKit

protocol ImageCropperViewControllerDelegate: AnyObject {
    func imageCropperControllerDidRetake()
    func didFinishEditingWithImage(editedImage: UIImage?)
}

final class ImageCropperViewController: BaseViewController {
    // MARK: - UI组件
    private lazy var contentScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .rgba(100, 100, 100)
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.clipsToBounds = false // 允许内容超出边界
        scrollView.delegate = self
        scrollView.minimumZoomScale = 0.5
        scrollView.maximumZoomScale = 3.0
        scrollView.bouncesZoom = true
        return scrollView
    }()
    
    private let backButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "backIcon")?.withRenderingMode(.alwaysTemplate), for: .normal)
        button.tintColor = .white
        return button
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .mediumGilroyFont(22)
        label.textColor = .white
        label.text = "Adjust Your Photo"
        label.textAlignment = .center
        return label
    }()
    
    private let nextButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "rightNav"), for: .normal)
        return button
    }()
    
    private let bottomBackButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "leftNav"), for: .normal)
        return button
    }()
    
    private let toolBar: UIView = {
        let bar = UIView()
        bar.backgroundColor = .rgba(0, 0, 0, 0.4)
        return bar
    }()
    
    lazy var overlayView: TestStripsView = {
        let view = TestStripsView(frame: .zero, width: self.scanViewWidth)
        view.isUserInteractionEnabled = false
        return view
    }()
    
    private let bottomOverlayView: UIView = {
        let view = UIView()
        view.backgroundColor = .themeColor
        return view
    }()
    
    private let overlayView1: UIView = {
        let view = UIView()
        view.borderWidth = 1
        view.layer.borderColor = UIColor.clear.cgColor
        view.backgroundColor = .clear
        view.isUserInteractionEnabled = false
        return view
    }()
    
    // MARK: - 属性
    var image: UIImage?
    private var imageView: UIImageView?
    weak var delegate: ImageCropperViewControllerDelegate?
    private var bottomToolBarHeight: CGFloat = 96.0

    private var cropRect = CGRect.zero
    var scanViewWidth: CGFloat = 0
    var scanViewLeftMargin: CGFloat = 0
    var cropOffsetWidth: CGFloat = 0

    private static var isFromCamera = false
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .rgba(100, 100, 100)
        
        // 将裁剪框高度从 25 提升至 37.5（+50%）
        cropRect = CGRect(
            x: (UIScreen.main.bounds.width - self.scanViewWidth) / 2.0,
                               y: 252 + 49 + bottomToolBarHeight,
                               width: self.scanViewWidth,
            height: 37.5
        )
        
        setupUI()
        setupActions()
        bottomToolBarHeight += view.safeSafeAreaInsets.top
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 检查来源页面
        checkSourceViewController()
        // 设置图片
        setupImageWithInitialPosition()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 只在首次布局时设置位置
        if !hasSetInitialOffset && imageView != nil {
            // 使用延迟确保所有视图已完成布局
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.hasSetInitialOffset = true
                self.fixFirstTimePositionOffset()
                self.setupContentInsets()
            }
        }
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        cleanupResources()
    }
    
    // MARK: - 私有方法
    private func setupUI() {
        // 添加视图
        [contentScrollView, toolBar, bottomOverlayView, overlayView, overlayView1].forEach(view.addSubview)
        
        // 设置约束
        contentScrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        toolBar.snp.makeConstraints { make in
            make.height.equalTo(bottomToolBarHeight + 50)
            make.trailing.leading.top.equalToSuperview()
        }
        
        bottomOverlayView.snp.makeConstraints { make in
            make.height.equalTo(120)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        overlayView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(toolBar.snp.bottom)
            make.bottom.equalTo(bottomOverlayView.snp.top)
        }
        
        [backButton, titleLabel].forEach(toolBar.addSubview)
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(18)
            make.bottom.equalToSuperview().inset(68)
            make.width.height.equalTo(18)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalTo(backButton)
        }
        
        [bottomBackButton, nextButton].forEach(bottomOverlayView.addSubview)
        bottomBackButton.snp.makeConstraints { make in
            make.width.height.equalTo(48)
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().offset(-40)
        }
        
        nextButton.snp.makeConstraints { make in
            make.width.height.equalTo(48)
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().offset(40)
        }
        
        overlayView1.snp.makeConstraints { make in
            make.height.equalTo(63) // 原来42pt，高度增加50%
            make.width.equalTo(self.scanViewWidth)
            make.centerX.equalToSuperview()
            make.top.equalTo(overlayView.bgDarkV1.snp.bottom)
        }
        
        // 添加裁剪框四角装饰
        setupCropFrameDecorations()
    }
    
    private func setupCropFrameDecorations() {
        // 添加裁剪框四角的装饰线
        let lineViews = [
            (UIView(), overlayView1, -2, -2, 12, 1, true),  // 左上角横线
            (UIView(), overlayView1, -2, -2, 1, 12, true),  // 左上角竖线
            (UIView(), overlayView1, -2, 0, 12, 1, false),  // 左下角横线
            (UIView(), overlayView1, -2, -10, 1, 12, false), // 左下角竖线
            (UIView(), overlayView1, 0, -2, 12, 1, true),   // 右上角横线
            (UIView(), overlayView1, 1, -2, 1, 12, true),   // 右上角竖线
            (UIView(), overlayView1, 0, 0, 12, 1, false),   // 右下角横线
            (UIView(), overlayView1, 1, -10, 1, 12, false)  // 右下角竖线
        ]
        
        for (i, (lineView, refView, offsetX, offsetY, width, height, isTop)) in lineViews.enumerated() {
            lineView.backgroundColor = UIColorFromRGB(rgbValue: 0xE5C2DB)
            view.addSubview(lineView)
            
            lineView.snp.makeConstraints { make in
                if i % 2 == 0 { // 横线
                    make.height.equalTo(height)
                    make.width.equalTo(width)
                    if isTop {
                        make.top.equalTo(refView).offset(offsetY)
                    } else {
                        make.bottom.equalTo(refView).offset(offsetY) // 修改这里，使用bottom而不是top
                    }
                    
                    if i < 4 { // 左侧
                        make.left.equalTo(refView).offset(offsetX)
                    } else { // 右侧
                        make.right.equalTo(refView).offset(-offsetX)
                    }
                } else { // 竖线
                    make.width.equalTo(width)
                    make.height.equalTo(height)
                    
                    if i < 4 { // 左侧
                        make.left.equalTo(refView).offset(offsetX)
                    } else { // 右侧
                        make.right.equalTo(refView).offset(-offsetX)
                    }
                    
                    if isTop {
                        make.top.equalTo(refView).offset(offsetY)
                    } else {
                        make.bottom.equalTo(refView).offset(offsetY) // 修改这里，使用bottom而不是snp.bottom
                    }
                }
            }
        }
    }
    
    private func setupActions() {
        backButton.addTarget(self, action: #selector(didTapReselect), for: .touchUpInside)
        nextButton.addTarget(self, action: #selector(didTapNext), for: .touchUpInside)
        bottomBackButton.addTarget(self, action: #selector(didTapReselect), for: .touchUpInside)
    }
    
    private func checkSourceViewController() {
        if let navigationController = navigationController {
            let viewControllers = navigationController.viewControllers
            if viewControllers.count >= 2 {
                let previousVC = viewControllers[viewControllers.count - 2]
                ImageCropperViewController.isFromCamera = previousVC is FullScreenCameraViewController
            }
        }
    }
    
    private func setupImageWithInitialPosition() {
        // 移除旧的imageView
        imageView?.removeFromSuperview()
        
        // 创建新的imageView
        imageView = UIImageView(image: image)
        imageView?.contentMode = .scaleAspectFit
        
        guard let imageView = imageView, let image = image else { 
            print("Debug - 图片加载失败: image为空")
            return 
        }
        
        // 添加到滚动视图
        contentScrollView.addSubview(imageView)
        
        // 获取裁剪框的尺寸
        let cropFrame = overlayView1.frame
        
        // 使用裁剪框宽度为基准，但放大2倍
        // 这样可以确保图片足够大，但又不会占满整个屏幕
        let scaleFactor: CGFloat = 1.15  // 增加比例至1.8倍
        let targetWidth = (cropFrame.width > 0 ? cropFrame.width : UIScreen.main.bounds.width * 0.7) * scaleFactor
        
        // 计算高度保持比例
        let aspectRatio = image.size.height / image.size.width
        let targetHeight = targetWidth * aspectRatio
        
        print("Debug - 原始图片尺寸: \(image.size)")
        print("Debug - 目标宽度: \(targetWidth), 高度: \(targetHeight)")
        print("Debug - 使用的缩放系数: \(scaleFactor)")
        
        // 设置imageView的初始frame
        imageView.frame = CGRect(
            x: 0,
            y: 0,
            width: targetWidth,
            height: targetHeight
        )
        
        // 重要：设置contentScrollView的初始状态，增加水平空间以便左右移动
        let horizontalPadding: CGFloat = 200
        contentScrollView.zoomScale = 1.0
        contentScrollView.contentSize = CGSize(
            width: targetWidth + horizontalPadding,
            height: max(targetHeight, UIScreen.main.bounds.height)
        )
        
        // 直接设置图片位置到裁剪框中心
        if cropFrame.height > 0 {
            imageView.center = CGPoint(
                x: contentScrollView.contentSize.width / 2,
                y: cropFrame.midY
            )
            
            // 设置初始偏移量，使图片在视图中居中显示
            contentScrollView.contentOffset = CGPoint(
                x: (contentScrollView.contentSize.width - contentScrollView.bounds.width) / 2,
                y: max(0, cropFrame.midY - contentScrollView.bounds.height / 2)
            )
        } else {
            // 如果裁剪框位置尚未确定，暂时居中放置
            imageView.center = CGPoint(
                x: contentScrollView.contentSize.width / 2,
                y: contentScrollView.contentSize.height / 2
            )
        }
        
        // 添加旋转手势
        for gesture in contentScrollView.gestureRecognizers ?? [] {
            if gesture is UIRotationGestureRecognizer {
                contentScrollView.removeGestureRecognizer(gesture)
            }
        }
        let rotationGesture = UIRotationGestureRecognizer(target: self, action: #selector(rotateImage(_:)))
        contentScrollView.addGestureRecognizer(rotationGesture)
        
        // 如果裁剪框尚未布局，等待布局完成后再次调整位置
        if cropFrame.height <= 0 {
            // 仅当裁剪框未布局时才设置标记
            hasSetInitialOffset = false
        } else {
            // 裁剪框已布局，标记为已设置
            hasSetInitialOffset = true
        }
    }
    
    // 新方法：设置允许自由移动的区域
    private func setupFreeMovementArea() {
        // 确保图片可以自由左右移动
        contentScrollView.alwaysBounceHorizontal = true
        contentScrollView.alwaysBounceVertical = true
        
        // 如果图片宽度等于裁剪框宽度，增加contentSize以允许左右移动
        if let imageView = imageView, abs(imageView.frame.width - overlayView1.frame.width) < 10 {
            // 在原始宽度基础上增加额外空间
            let extraSpace: CGFloat = 200  // 左右各增加100点
            contentScrollView.contentSize.width = imageView.frame.width + extraSpace
            
            // 居中放置图片
            imageView.center.x = contentScrollView.contentSize.width / 2
        }
    }
    
    // 新方法：修复第一次进入时的位置偏移问题
    private func fixFirstTimePositionOffset() {
        guard let imageView = imageView else { return }
        
        // 获取裁剪框位置
        let cropFrame = overlayView1.frame
        if cropFrame.height <= 0 {
            return  // 如果裁剪框尚未布局，则不进行调整
        }
        
        // 直接计算并设置图片中心，确保与裁剪框中心对齐
        let targetCenterY = cropFrame.midY
        
        // 比较当前位置与目标位置，如果差异小则不调整以避免闪动
        if abs(imageView.center.y - targetCenterY) < 5 {
            return
        }
        
        // 使用无动画方式调整位置，避免闪动
        imageView.center = CGPoint(
            x: contentScrollView.contentSize.width / 2,
            y: targetCenterY
        )
        
        // 修正contentOffset
        contentScrollView.contentOffset = CGPoint(
            x: (contentScrollView.contentSize.width - contentScrollView.bounds.width) / 2,
            y: max(0, targetCenterY - contentScrollView.bounds.height / 2)
        )
    }
    
    // 修改缩放后中心调整方法，支持自由移动
    private func centerImageAfterZooming() {
        guard let imageView = imageView, contentScrollView.bounds.width > 0 else { return }
        
        // 计算新的contentSize，确保有足够空间移动
        let boundsSize = contentScrollView.bounds.size
        let extraHorizontalSpace: CGFloat = 200  // 水平方向额外空间
        let contentSize = CGSize(
            width: max(imageView.frame.width, boundsSize.width) + extraHorizontalSpace,
            height: max(imageView.frame.height, boundsSize.height)
        )
        contentScrollView.contentSize = contentSize
        
        // 只在图片宽度小于ScrollView宽度时进行水平居中
        if imageView.frame.width < boundsSize.width {
            imageView.center.x = contentScrollView.contentSize.width / 2
        }
        
        // 垂直方向保持与裁剪框对齐，但不强制重置水平位置
        let cropFrameY = overlayView1.frame.midY
        imageView.center.y = cropFrameY
        
        print("Debug - 缩放后 - 图片中心点: \(imageView.center)")
        print("Debug - 缩放后 - 裁剪框中心点: \(overlayView1.frame.midY)")
    }
    
    private func ensureImagePositionCorrect() {
        // 确保scrollView的contentInset设置正确，以便在边缘有足够的空间
        setupContentInsets()
        
        // 再次确保图片位置正确
        fixFirstTimePositionOffset()
    }
    
    private func setupContentInsets() {
        let cropFrame = overlayView1.frame
        
        // 计算上下左右的内边距，确保裁剪框始终可见
        let topInset = max(0, cropFrame.minY - 20)
        let leftInset = max(0, cropFrame.minX - 20)
        let bottomInset = max(0, view.bounds.height - cropFrame.maxY - 20)
        let rightInset = max(0, view.bounds.width - cropFrame.maxX - 20)
        
        // 设置内边距
        contentScrollView.contentInset = UIEdgeInsets(
            top: topInset,
            left: leftInset,
            bottom: bottomInset,
            right: rightInset
        )
    }
    
    private func cleanupResources() {
        imageView?.removeFromSuperview()
        imageView?.image = nil
        imageView = nil
    }
    
    @objc func rotateImage(_ gesture: UIRotationGestureRecognizer) {
        guard let imageView = imageView else { return }
        
        // 对图片本身进行旋转，而不是整个scrollView
        imageView.transform = imageView.transform.rotated(by: gesture.rotation)
        gesture.rotation = 0
    }
    
    @objc func didTapReselect() {
        ImageCropperViewController.isFromCamera = false
        delegate?.imageCropperControllerDidRetake()
    }
    
    @objc func didTapNext() {
        // 确保我们有图片和图片视图
        guard let imageView = self.imageView, let originalImage = imageView.image else {
            print("没有可用的图片")
            return
        }
        
        // 获取裁剪框在屏幕上的位置
        guard let cropViewSuperview = overlayView.cropView.superview,
              let window = UIApplication.shared.windows.first else {
            print("无法获取裁剪视图位置")
            return
        }
        
        let cropViewFrameInWindow = cropViewSuperview.convert(overlayView.cropView.frame, to: window)
        let imageViewFrameInWindow = imageView.superview?.convert(imageView.frame, to: window)
        
        guard let imageViewFrame = imageViewFrameInWindow else {
            print("无法获取图片视图位置")
            return
        }
        
        // 计算裁剪区域相对于图片视图的位置
        let relativeX = (cropViewFrameInWindow.minX - imageViewFrame.minX) / imageViewFrame.width
        let relativeY = (cropViewFrameInWindow.minY - imageViewFrame.minY) / imageViewFrame.height
        let relativeWidth = cropViewFrameInWindow.width / imageViewFrame.width
        let relativeHeight = cropViewFrameInWindow.height / imageViewFrame.height
        
        // 将相对位置应用到原始图片上
        let cropX = relativeX * originalImage.size.width
        let cropY = relativeY * originalImage.size.height
        let cropWidth = relativeWidth * originalImage.size.width
        let cropHeight = relativeHeight * originalImage.size.height
        
        // 考虑图片的缩放和旋转
        let transformedCropRect = CGRect(
            x: max(0, cropX),
            y: max(0, cropY),
            width: min(cropWidth, originalImage.size.width - cropX),
            height: min(cropHeight, originalImage.size.height - cropY)
        )
        
        // 使用TBImageCropperHelper裁剪图片
        let croppedImage = TBImageCropperHelper.cropImage(originalImage, with: transformedCropRect)
        
        // 输出裁剪后的图片尺寸
        if let finalImage = croppedImage {
            print("裁剪后图片尺寸: \(finalImage.size)")
        }
        
        // 返回裁剪后的图片
        delegate?.didFinishEditingWithImage(editedImage: croppedImage)
    }
    
    // 添加变量追踪初始化状态
    private var hasSetInitialOffset = false
    
    // 处理缩放中心点偏移问题 - 添加缩放前记录点和缩放后恢复中心点的逻辑
    // 添加变量记录缩放时的点
    private var zoomInitialCenter: CGPoint = .zero
    
    // 缩放开始时记录中心点
    func scrollViewWillBeginZooming(_ scrollView: UIScrollView, with view: UIView?) {
        guard let imageView = imageView else { return }
        
        // 记录图片当前中心点
        zoomInitialCenter = imageView.center
    }
    
    // 缩放结束后重置记录点
    func scrollViewDidEndZooming(_ scrollView: UIScrollView, with view: UIView?, atScale scale: CGFloat) {
        // 缩放结束时，清空记录的中心点
        zoomInitialCenter = .zero
        
        // 确保垂直方向对齐到裁剪框
        if let imageView = imageView {
            let cropFrameY = overlayView1.frame.midY
            
            // 仅调整垂直位置，保持水平位置不变
            if abs(imageView.center.y - cropFrameY) > 5 {
                UIView.animate(withDuration: 0.2) {
                    imageView.center.y = cropFrameY
                }
            }
        }
    }
}

// MARK: - UIScrollViewDelegate
extension ImageCropperViewController: UIScrollViewDelegate {
    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        return imageView
    }
    
    func scrollViewDidZoom(_ scrollView: UIScrollView) {
        guard let imageView = imageView else { return }
        
        // 计算新的contentSize，确保有足够空间移动
        let boundsSize = contentScrollView.bounds.size
        let extraHorizontalSpace: CGFloat = 200  // 水平方向额外空间
        let contentSize = CGSize(
            width: max(imageView.frame.width, boundsSize.width) + extraHorizontalSpace,
            height: max(imageView.frame.height, boundsSize.height)
        )
        contentScrollView.contentSize = contentSize
        
        // 获取裁剪框位置
        let cropFrame = overlayView1.frame
        
        // 计算水平位置，保持原来的相对位置
        let centerX: CGFloat
        if zoomInitialCenter.x != 0 {
            // 使用缩放前的水平位置比例计算新的水平位置
            let widthRatio = contentSize.width / scrollView.contentSize.width
            centerX = zoomInitialCenter.x * widthRatio
        } else {
            // 如果没有记录初始位置，则使用内容中心
            centerX = contentSize.width / 2
        }
        
        // 设置新的中心点，水平保持原位，垂直对齐裁剪框
        imageView.center = CGPoint(
            x: centerX,
            y: cropFrame.midY
        )
        
        // 调整contentOffset确保缩放时视图不会跳动
        if zoomInitialCenter.x != 0 {
            // 计算新的contentOffset，使图片在视图中保持相对位置
            let xOffset = max(0, centerX - scrollView.bounds.width / 2)
            let yOffset = max(0, cropFrame.midY - scrollView.bounds.height / 2)
            scrollView.contentOffset = CGPoint(x: xOffset, y: yOffset)
        }
    }
    
    // 添加滚动结束事件处理
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        // 滚动停止后确保垂直方向与裁剪框对齐
        if let imageView = imageView {
            // 只调整垂直位置，保持水平位置不变
            let cropFrameY = overlayView1.frame.midY
            
            // 仅当垂直偏移较大时才调整
            if abs(imageView.center.y - cropFrameY) > 5 {
                UIView.animate(withDuration: 0.3) {
                    imageView.center.y = cropFrameY
                }
            }
        }
    }
}
