import UIKit

protocol OverlayViewDelegate: class {
    func didTapCancel()
    func didTapTakePhoto()
}

class OverlayView: UIView, NibLoadable {
    @IBOutlet weak var cancelButton: UIButton!
    weak var delegate: OverlayViewDelegate?
    @IBOutlet weak var topBarView: UIView!
    @IBOutlet weak var topBarViewHeightConstraint: NSLayoutConstraint!

    override func awakeFromNib() {
        super.awakeFromNib()

        setupFontAndColor()
        let safeAreaInsetsTop = UIApplication.shared.keyWindow?.safeAreaInsets.top ?? 0
        let safeAreaInsetsBottom = UIApplication.shared.keyWindow?.safeAreaInsets.bottom ?? 0
        topBarViewHeightConstraint.constant = 44 + safeAreaInsetsTop
    }

    private func setupFontAndColor() {
        backgroundColor = .clear
        topBarView.backgroundColor = UIColor.black.withAlphaComponent(0.2)
//        cancelButton.titleLabel?.font = TBFontType.mulishLink2.font
//        cancelButton.setAttributedTitle("Cancel".attributedText(.mulishLink2, foregroundColor: .GlobalTextSecondary), for: .normal)
    }

    @IBAction func didTapCancel() {
        delegate?.didTapCancel()
    }

    @IBAction func didTapTakePhoto() {
        delegate?.didTapTakePhoto()
    }
}
