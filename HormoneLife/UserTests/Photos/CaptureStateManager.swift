import Foundation

// MARK: - 拍照状态
enum CaptureState {
    case idle        // 空闲状态
    case preparing   // 准备拍照（倒计时中）
    case capturing   // 正在拍照
    case processing  // 正在处理图片
    case uploading   // 正在上传图片
    case error       // 错误状态
    case completed   // 拍照流程完成
    
    var canStartNewCapture: Bool {
        return self == .idle || self == .error || self == .completed
    }
    
    var isProcessing: Bool {
        return self == .capturing || self == .processing || self == .uploading
    }
}

// MARK: - 拍照管理器
class CaptureStateManager {
    // 单例模式
    static let shared = CaptureStateManager()
    
    // 当前拍照状态
    private(set) var currentState: CaptureState = .idle
    
    // 是否允许自动拍照
    // 【对比测试】默认关闭自动拍照功能，保留手动拍照流程
    private(set) var isAutoCaptureEnabled: Bool = false
    
    // 重试次数
    private(set) var retryCount: Int = 0
    private var maxRetryCount: Int = 3
    
    // 标记是否已执行过自动拍照 (替代静态变量)
    private(set) var hasExecutedAutoCapture: Bool = false
    
    // 防止重复初始化
    private init() {}
    
    // MARK: - 状态管理方法
    
    // 重置所有状态
    func resetAllStates() {
        currentState = .idle
        // 【对比测试】保持自动拍照关闭状态
        isAutoCaptureEnabled = false
        retryCount = 0
        print("【拍照管理器】重置所有状态")
    }
    
    // 准备开始自动拍照
    func prepareForAutoCapture() -> Bool {
        guard currentState.canStartNewCapture else {
            print("【拍照管理器】当前状态(\(currentState))不允许开始新的拍照")
            return false
        }
        
        currentState = .preparing
        print("【拍照管理器】准备开始自动拍照")
        return true
    }
    
    // 开始拍照
    func startCapture() -> Bool {
        guard currentState == .preparing || currentState == .idle else {
            print("【拍照管理器】当前状态(\(currentState))不允许开始拍照")
            return false
        }
        
        currentState = .capturing
        print("【拍照管理器】开始拍照，当前重试次数: \(retryCount)")
        return true
    }
    
    // 开始处理图片
    func startProcessing() -> Bool {
        guard currentState == .capturing else {
            print("【拍照管理器】当前状态(\(currentState))不允许处理图片")
            return false
        }
        
        currentState = .processing
        print("【拍照管理器】开始处理图片")
        return true
    }
    
    // 开始上传图片
    func startUploading() -> Bool {
        guard currentState == .processing else {
            print("【拍照管理器】当前状态(\(currentState))不允许上传图片")
            return false
        }
        
        currentState = .uploading
        print("【拍照管理器】开始上传图片")
        return true
    }
    
    // 处理错误
    func handleError() {
        let previousState = currentState
        currentState = .error
        print("【拍照管理器】处理错误，前一状态: \(previousState)")
    }
    
    // 完成拍照流程
    func completeCapture() {
        currentState = .completed
        hasExecutedAutoCapture = true
        print("【拍照管理器】完成拍照流程")
    }
    
    // 停止自动拍照
    func stopAutoCapture() {
        isAutoCaptureEnabled = false
        if currentState.isProcessing {
            currentState = .completed
        }
        print("【拍照管理器】停止自动拍照，当前状态: \(currentState)")
    }
    
    // 启用自动拍照
    func enableAutoCapture() {
        isAutoCaptureEnabled = true
        print("【拍照管理器】启用自动拍照")
    }
    
    // 尝试增加重试次数
    func incrementRetryCount() -> Bool {
        retryCount += 1
        print("【拍照管理器】增加重试次数，当前: \(retryCount)/\(maxRetryCount)")
        
        if retryCount >= maxRetryCount {
            stopAutoCapture()
            return false
        }
        return true
    }
    
    // 重置重试次数
    func resetRetryCount() {
        retryCount = 0
        print("【拍照管理器】重置重试次数")
    }
    
    // 重置自动拍照标记
    func resetAutoCapture() {
        hasExecutedAutoCapture = false
        print("【拍照管理器】重置自动拍照标记")
    }
    
    // 检查是否可以开始新的拍照
    func canStartNewCapture() -> Bool {
        return currentState.canStartNewCapture && isAutoCaptureEnabled
    }
} 