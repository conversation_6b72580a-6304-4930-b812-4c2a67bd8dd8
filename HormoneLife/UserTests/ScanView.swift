import UIKit

class ScanView: UIView {
    
//    static var scanViewWidth = UIScreen.main.bounds.width - 40
    
    lazy var scanAnimationImage = UIImage()
    public var borderColor = UIColor.white
    public lazy var borderLineWidth: CGFloat = 0.4
    public lazy var cornerColor = UIColor.rgba(229, 194, 219)
    public lazy var cornerWidth: CGFloat = 2.0
    public lazy var backgroundAlpha: CGFloat = 0
    public lazy var scanBorderWidthRadio: CGFloat = 1
    lazy var scanBorderWidth = scanBorderWidthRadio * UIScreen.main.bounds.width
    lazy var scanBorderHeight = scanBorderWidth * 0.5
    private var scanViewSize: CGSize = CGSize(width: UIScreen.main.bounds.width - 40, height: 30)
    lazy var contentView = UIView(frame:  .zero)

    private lazy var imageView: UIImageView = {
        let imageView = UIImageView(image:  self.scanAnimationImage)
        return imageView
    }()

    override public init(frame:  CGRect) {
        super.init(frame:  frame)
        backgroundColor = .white
        contentView.backgroundColor = .clear
//        contentView.backgroundColor = .red
        contentView.clipsToBounds = true
        addSubview(contentView)
        contentView.snp.makeConstraints { make in
//            make.edges.equalToSuperview()
            make.left.right.top.bottom.equalToSuperview()
        }
        contentView.borderWidth = 1
        contentView.layer.borderColor = UIColor.clear.cgColor //UIColor.rgba(229, 165, 165).cgColor
    }

    required init?(coder aDecoder:  NSCoder) {
        fatalError("init(coder: ) has not been implemented")
    }

    override public func draw(_ rect:  CGRect) {
        super.draw(rect)
        drawScan(rect)
    }

    func startAnimation() {
        let rect = CGRect(x:  0, y:  0, width:  scanBorderWidth, height: scanBorderHeight)
        ScanAnimation.shared.startWith(rect, contentView, imageView:  imageView)
    }
    
    func stopAnimation() {
        ScanAnimation.shared.stopStepAnimating()
    }
    
    func refreshScanViewFrame(width: CGFloat) {
        
//        self.scanViewSize = CGSize(width: width, height: 30)
//        print("")
        self.layoutIfNeeded()
    }
}

extension ScanView {
    //画四个角还要调整
    func drawScan(_ rect:  CGRect) {
        UIColor.black.withAlphaComponent(backgroundAlpha).setFill()
        UIRectFill(rect)
    }
}

// MARK:  -  ScanAnimation
class ScanAnimation: NSObject{
    static let shared: ScanAnimation = {
        let instance = ScanAnimation()
        return instance
    }()
    lazy var animationImageView = UIImageView()
    var tempFrame: CGRect?
    var isAnimationing = false

    func startWith(_ rect: CGRect, _ parentView: UIView, imageView: UIImageView) {
        // 提前检查图片资源是否有效，避免 0/0 产生 NaN 导致 CALayer crash
        guard let img = imageView.image,
              img.size.width > 0,
              img.size.height > 0 else {
            print("[ScanAnimation] 无有效扫描线图片，跳过动画")
            return
        }

        tempFrame = rect
        animationImageView = imageView
        animationImageView.alpha = 0.5
        parentView.addSubview(animationImageView)
        animationImageView.frame = tempFrame ?? .zero
        isAnimationing = true
        animation()
    }

    @objc func animation() {
        guard isAnimationing else {
            return
        }
        guard let baseFrame = tempFrame, let img = animationImageView.image else { return }

        // 保护性计算，避免除以 0
        let imgWidth = img.size.width > 0 ? img.size.width : 1
        let imgHeight = img.size.height > 0 ? img.size.height : 1

        var frame = baseFrame
        let hImg = imgHeight * frame.size.width / imgWidth
        frame.origin.y -= hImg
        frame.size.height = hImg
        self.animationImageView.frame = frame

        UIView.animate(withDuration: 3, delay: 0, options: .curveLinear, animations: {
            guard let baseFrame = self.tempFrame else { return }
            let imgW = imgWidth
            let imgH = imgHeight
            var newFrame = baseFrame
            let hImgAnim = self.animationImageView.frame.size.height * baseFrame.size.width / imgW
            newFrame.origin.y += (baseFrame.size.height - hImgAnim)
            newFrame.size.height = hImgAnim
            self.animationImageView.frame = newFrame
        }, completion: { _ in
            self.perform(#selector(ScanAnimation.animation), with: nil, afterDelay: 0.3)
        })
    }

    func stopStepAnimating() {
        self.animationImageView.alpha = 0
        isAnimationing = false
    }

    deinit {
        stopStepAnimating()
    }
}
