import UIKit
import Photos

class RecordTestsViewController: BaseViewController {

    public var uploadingSucessCallback:((_ code: String) -> ())?
    public var didReceiveErrorCallback:((_ error: Error) -> ())?
    lazy var imageCropperController: ImageCropperViewController = {
        let imageCropperController = ImageCropperViewController()
        imageCropperController.delegate = self
        return imageCropperController
    }()

    @IBOutlet weak var scanbgView: UIView!
    @IBOutlet weak var chooseFromGalleryButton: UIButton!
    private lazy var scanView: ScanView = {
        let view = ScanView(frame: CGRect(origin: .zero, size: .zero))
        return view
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        navigationItem.title = "Record Your Tests"
        setupUI()
    }

    func setupUI() {
        chooseFromGalleryButton.layer.borderColor = UIColor.rgba(4, 52, 51).cgColor
        chooseFromGalleryButton.layer.masksToBounds = true
        chooseFromGalleryButton.layer.borderWidth = 0.5
        chooseFromGalleryButton.layer.cornerRadius = 4

        scanView.backgroundColor = .red
        scanbgView.addSubview(scanView)
        scanView.bringSubviewToFront(view)
        scanView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(60)
            make.width.equalTo(244)
        }
        scanView.scanAnimationImage = UIImage(named: "scanLine") ?? UIImage()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
//        scanView.startAnimation()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        scanView.stopAnimation()
    }
    
    @IBAction func didTapCamera(_ sender: UIButton) {
        checkPermissions { isAuthorized in
            guard isAuthorized else { return }
            DispatchQueue.main.async {
                self.pushCameraVC()
            }
        }
    }

    @IBAction func didTapChooseFromGallery(_ sender: UIButton) {
        presentImagePickerController()
    }

    private func presentImagePickerController() {
        let picker = UIImagePickerController()
        picker.sourceType = .photoLibrary
        picker.delegate = self
        picker.allowsEditing = true
        picker.modalPresentationStyle = .fullScreen
        self.present(picker, animated: true, completion: nil)
    }
    
    func pushCameraVC() {
        let customCameraController = CustomCameraViewController()
        customCameraController.delegate = self
        navigationController?.pushViewController(customCameraController, animated: false)
        customCameraController.view.layoutIfNeeded()
    }

    func checkPermissions(isAuthorized: @escaping ((Bool) -> Void)) {
        switch AVCaptureDevice.authorizationStatus(for: AVMediaType.video) {
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: AVMediaType.video, completionHandler: { (authorization) in
                isAuthorized(authorization)
            })
        case .denied:
            isAuthorized(false)
        case .authorized: isAuthorized(true)
        default: break
        }
    }

}

extension RecordTestsViewController: UIImagePickerControllerDelegate,UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        guard let image = info[.originalImage] as? UIImage else { return }
        //拿到图片
        dismissVC()
//        imageCropperController.view.backgroundColor = .red
        imageCropperController.image = image
        addChild(imageCropperController)
        view.addSubview(imageCropperController.view)
        imageCropperController.didMove(toParent: self)
    }
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        dismissVC()
    }

    private func dismissVC() {
        dismiss(animated: true, completion: nil)
    }
}

extension RecordTestsViewController: CustomCameraViewControllerDelegate {
    func didFinishPickingImage(_ cameraController: CustomCameraViewController, image: UIImage) {
        //
    }
    
    func didCancel(cameraController: CustomCameraViewController) {
        //
    }
}

extension RecordTestsViewController: ImageCropperViewControllerDelegate {
    func imageCropperControllerDidRetake() {
        //
    }

    func didFinishEditingWithImage(editedImage: UIImage?) {
        //
    }

}
