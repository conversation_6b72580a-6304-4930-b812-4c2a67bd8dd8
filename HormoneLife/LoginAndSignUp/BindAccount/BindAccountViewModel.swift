//
//  BindAccountViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/10/4.
//

import UIKit

class BindAccountViewModel: BaseViewModel {

    func bindAccount(account: String?, grantType: String?, idToken: String?, uid : String? ,success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        var params = [String : String]()
        if let account = account {
            params["account"] = account
        }
        if let grantType = grantType {
            params["grantType"] = grantType
        }
        if let idToken = idToken {
            params["idToken"] = idToken
        }
        if let uid = uid {
            params["uid"] = uid
        }
        
        UserInteractor.bindThirdAccount(params: params) { result in
            success(result)
        } failure: { error in
            failure(error)
        }
    }
}
