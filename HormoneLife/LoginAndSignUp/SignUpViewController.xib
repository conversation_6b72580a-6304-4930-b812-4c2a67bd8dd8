<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="SignUpViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="accountFieldHeight" destination="cxH-sh-bOJ" id="BE0-JE-GDz"/>
                <outlet property="accountLabelL" destination="Jb3-iT-owW" id="nny-cP-U7S"/>
                <outlet property="accountLabelR" destination="i3t-KH-tBr" id="g3L-oY-QmK"/>
                <outlet property="accountTypeButton" destination="EGr-dp-kUo" id="yLT-HK-2pu"/>
                <outlet property="accountTypeSelectView" destination="pnO-Dw-bWT" id="vc6-ru-lWX"/>
                <outlet property="accountTypeSelectViewBottomContraint" destination="3MC-Dk-Ufl" id="XgI-K5-g34"/>
                <outlet property="accountTypeSelectViewCloseButton" destination="Daf-U1-sjt" id="Bco-Em-BA0"/>
                <outlet property="accountTypeSelectViewEmail" destination="c1a-ie-cz0" id="DWb-Kx-Uho"/>
                <outlet property="accountTypeSelectViewMobile" destination="kOa-RT-nuk" id="Osa-Qc-e1P"/>
                <outlet property="agreeBtn" destination="Rkn-gn-E4e" id="hic-1L-pvj"/>
                <outlet property="agreeCheckBox" destination="gBd-Wa-duk" id="4GZ-YV-4cd"/>
                <outlet property="confirmPwdField" destination="lOx-oI-qUc" id="drV-2s-jUz"/>
                <outlet property="disAgreeBtn" destination="Mry-Xx-GvI" id="cPP-g0-B6K"/>
                <outlet property="emailField" destination="bJi-nv-7E6" id="xcY-A4-m5g"/>
                <outlet property="emailTipLabel" destination="Uke-IN-FHy" id="fZX-xq-3Dh"/>
                <outlet property="firstNameField" destination="qBf-CQ-WdJ" id="GeC-CJ-1IU"/>
                <outlet property="invitationCodeField" destination="Jkc-kh-EXp" id="fzS-Wd-Zzh"/>
                <outlet property="lastNameField" destination="PXr-uS-rQn" id="e2o-20-kG3"/>
                <outlet property="passwordField" destination="e7B-fC-TKB" id="BEs-F3-rAL"/>
                <outlet property="signUpButton" destination="xAQ-5Z-oZX" id="FKi-9p-CJo"/>
                <outlet property="tAndcDescriptionTextView" destination="0x7-r3-PR9" id="cxL-gQ-JJz"/>
                <outlet property="tAndcTextView" destination="ezc-L3-7k5" id="fhD-Cz-Im8"/>
                <outlet property="tAndcTipsView" destination="MBK-Zd-8zl" id="UyK-nw-0jR"/>
                <outlet property="tipsBgView" destination="FYB-Zj-AGK" id="B2B-kH-fkg"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" contentInsetAdjustmentBehavior="never" translatesAutoresizingMaskIntoConstraints="NO" id="vqc-Tk-nvJ">
                    <rect key="frame" x="0.0" y="118" width="393" height="734"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fDL-RZ-Q6H">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="760.33333333333337"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Yu2-wf-4dV">
                                    <rect key="frame" x="24" y="24" width="345" height="712.33333333333337"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yME-8t-2pu">
                                            <rect key="frame" x="0.0" y="0.0" width="345" height="72"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Xn9-QV-ljr">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="72"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a2c-bi-wQ5">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="9ph-ey-68z"/>
                                                            </constraints>
                                                            <attributedString key="attributedText">
                                                                <fragment content="First Name">
                                                                    <attributes>
                                                                        <color key="NSColor" red="0.20816263560000001" green="0.075631909070000003" blue="0.3644168377" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" metaFont="system" size="17"/>
                                                                    </attributes>
                                                                </fragment>
                                                                <fragment content="*">
                                                                    <attributes>
                                                                        <color key="NSColor" red="1" green="0.24525119417662955" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" metaFont="system" size="17"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1ws-8R-edX">
                                                            <rect key="frame" x="0.0" y="24" width="345" height="48"/>
                                                            <subviews>
                                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="qBf-CQ-WdJ">
                                                                    <rect key="frame" x="24" y="0.0" width="321" height="48"/>
                                                                    <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="qBf-CQ-WdJ" secondAttribute="trailing" id="EHq-wf-qrD"/>
                                                                <constraint firstItem="qBf-CQ-WdJ" firstAttribute="leading" secondItem="1ws-8R-edX" secondAttribute="leading" constant="24" id="cTU-eQ-NKz"/>
                                                                <constraint firstAttribute="height" constant="48" id="lrS-N9-5Nb"/>
                                                                <constraint firstItem="qBf-CQ-WdJ" firstAttribute="top" secondItem="1ws-8R-edX" secondAttribute="top" id="tjs-m2-GLI"/>
                                                                <constraint firstAttribute="bottom" secondItem="qBf-CQ-WdJ" secondAttribute="bottom" id="utj-SW-pdw"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                    <integer key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="Xn9-QV-ljr" firstAttribute="leading" secondItem="yME-8t-2pu" secondAttribute="leading" id="Anz-RM-we2"/>
                                                <constraint firstItem="Xn9-QV-ljr" firstAttribute="top" secondItem="yME-8t-2pu" secondAttribute="top" id="HMP-bw-Ws8"/>
                                                <constraint firstAttribute="bottom" secondItem="Xn9-QV-ljr" secondAttribute="bottom" id="nel-ke-Wqm"/>
                                                <constraint firstAttribute="trailing" secondItem="Xn9-QV-ljr" secondAttribute="trailing" id="sc7-aC-1g2"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ONe-2a-7Hg">
                                            <rect key="frame" x="0.0" y="88" width="345" height="72"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="NSi-E2-16U">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="72"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GXA-JQ-HWm">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="fKV-Uu-cQA"/>
                                                            </constraints>
                                                            <attributedString key="attributedText">
                                                                <fragment content="Last Name">
                                                                    <attributes>
                                                                        <color key="NSColor" red="0.*****************" green="0.071362100540000001" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" metaFont="system" size="17"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                    </attributes>
                                                                </fragment>
                                                                <fragment content="*">
                                                                    <attributes>
                                                                        <color key="NSColor" red="1" green="0.24525119417662955" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" metaFont="system" size="17"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lkI-Ix-RoR">
                                                            <rect key="frame" x="0.0" y="24" width="345" height="48"/>
                                                            <subviews>
                                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="PXr-uS-rQn">
                                                                    <rect key="frame" x="24" y="0.0" width="321" height="48"/>
                                                                    <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="PXr-uS-rQn" secondAttribute="trailing" id="G9n-nZ-bOe"/>
                                                                <constraint firstAttribute="bottom" secondItem="PXr-uS-rQn" secondAttribute="bottom" id="YAv-BK-P9r"/>
                                                                <constraint firstItem="PXr-uS-rQn" firstAttribute="top" secondItem="lkI-Ix-RoR" secondAttribute="top" id="aOw-jg-KLu"/>
                                                                <constraint firstAttribute="height" constant="48" id="adG-WR-ZOc"/>
                                                                <constraint firstItem="PXr-uS-rQn" firstAttribute="leading" secondItem="lkI-Ix-RoR" secondAttribute="leading" constant="24" id="vig-lB-IBt"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                    <integer key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="NSi-E2-16U" firstAttribute="top" secondItem="ONe-2a-7Hg" secondAttribute="top" id="m6H-xd-q1Z"/>
                                                <constraint firstAttribute="trailing" secondItem="NSi-E2-16U" secondAttribute="trailing" id="nHQ-LO-kUB"/>
                                                <constraint firstAttribute="bottom" secondItem="NSi-E2-16U" secondAttribute="bottom" id="qiq-bk-BFL"/>
                                                <constraint firstItem="NSi-E2-16U" firstAttribute="leading" secondItem="ONe-2a-7Hg" secondAttribute="leading" id="ydO-IB-I9I"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="43s-uS-nb3">
                                            <rect key="frame" x="0.0" y="176" width="345" height="120"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="qM4-r5-5xR">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="120"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YdN-KO-G5N">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="20"/>
                                                            <subviews>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mobile Number" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jb3-iT-owW">
                                                                    <rect key="frame" x="0.0" y="0.0" width="117.66666666666667" height="20"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="20" id="PaE-Oe-wMB"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="17"/>
                                                                    <color key="textColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uMa-oK-Dw4">
                                                                    <rect key="frame" x="117.66666666666666" y="0.0" width="7.6666666666666714" height="20"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="20" id="qF3-ye-aZr"/>
                                                                    </constraints>
                                                                    <attributedString key="attributedText">
                                                                        <fragment content="*">
                                                                            <attributes>
                                                                                <color key="NSColor" red="1" green="0.0093617555427757404" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                <font key="NSFont" metaFont="system" size="17"/>
                                                                                <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                            </attributes>
                                                                        </fragment>
                                                                    </attributedString>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="r35-Hp-YgT">
                                                                    <rect key="frame" x="125.33333333333336" y="0.0" width="219.**************" height="20"/>
                                                                    <subviews>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EGr-dp-kUo">
                                                                            <rect key="frame" x="199.66666666666666" y="0.0" width="20" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="20" id="tlZ-Eh-IrE"/>
                                                                            </constraints>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon">
                                                                                <color key="titleColor" red="0.*****************" green="0.071362100540000001" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="didTapAccountTypeButtonAction:" destination="-1" eventType="touchUpInside" id="abs-iG-T32"/>
                                                                            </connections>
                                                                        </button>
                                                                        <label opaque="NO" alpha="0.5" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Use Email Instead" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="i3t-KH-tBr">
                                                                            <rect key="frame" x="62.***************" y="0.0" width="136.**************" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="R8W-mX-yC9"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="17"/>
                                                                            <color key="textColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="EGr-dp-kUo" firstAttribute="leading" secondItem="i3t-KH-tBr" secondAttribute="trailing" id="03b-c7-l3S"/>
                                                                        <constraint firstAttribute="bottom" secondItem="EGr-dp-kUo" secondAttribute="bottom" id="4rx-8E-PUA"/>
                                                                        <constraint firstAttribute="bottom" secondItem="i3t-KH-tBr" secondAttribute="bottom" id="MLO-I2-YC2"/>
                                                                        <constraint firstItem="i3t-KH-tBr" firstAttribute="top" secondItem="r35-Hp-YgT" secondAttribute="top" id="YdV-Pj-R0b"/>
                                                                        <constraint firstAttribute="trailing" secondItem="EGr-dp-kUo" secondAttribute="trailing" id="aAz-nR-KMc"/>
                                                                        <constraint firstItem="EGr-dp-kUo" firstAttribute="top" secondItem="r35-Hp-YgT" secondAttribute="top" id="eNx-15-P3o"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="e2I-rd-V0D">
                                                            <rect key="frame" x="0.0" y="24" width="345" height="48"/>
                                                            <subviews>
                                                                <textField opaque="NO" tag="3000" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="bJi-nv-7E6">
                                                                    <rect key="frame" x="23.999999999999996" y="0.0" width="32.666666666666657" height="48"/>
                                                                    <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="48" id="5Us-5c-DJJ"/>
                                                                <constraint firstAttribute="bottom" secondItem="bJi-nv-7E6" secondAttribute="bottom" id="C4P-KK-1aE"/>
                                                                <constraint firstItem="bJi-nv-7E6" firstAttribute="leading" secondItem="e2I-rd-V0D" secondAttribute="leading" constant="24" id="gBu-p3-GiR"/>
                                                                <constraint firstItem="bJi-nv-7E6" firstAttribute="top" secondItem="e2I-rd-V0D" secondAttribute="top" id="mZU-4n-0Za"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                    <integer key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="If you do not receive the confirmation email, check your spam folder." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Uke-IN-FHy">
                                                            <rect key="frame" x="0.0" y="76" width="345" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="16" id="YDf-NU-nIl"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="We'll send you a verification code once you tap the button above." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a3H-3S-AU4">
                                                            <rect key="frame" x="0.0" y="104" width="345" height="16"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="16" id="zG4-zW-fiZ"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="qM4-r5-5xR" secondAttribute="trailing" id="3Zw-wB-kb7"/>
                                                <constraint firstItem="qM4-r5-5xR" firstAttribute="leading" secondItem="43s-uS-nb3" secondAttribute="leading" id="E9X-1S-Z6r"/>
                                                <constraint firstAttribute="height" constant="120" id="cxH-sh-bOJ"/>
                                                <constraint firstItem="qM4-r5-5xR" firstAttribute="top" secondItem="43s-uS-nb3" secondAttribute="top" id="dwU-e2-dL5"/>
                                                <constraint firstAttribute="bottom" secondItem="qM4-r5-5xR" secondAttribute="bottom" id="fwk-bB-ZOs"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Kax-aj-2bc">
                                            <rect key="frame" x="0.0" y="312" width="345" height="72"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="bXo-hR-Bkl">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="72"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Da9-8I-Yag">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="wpS-ZM-SvI"/>
                                                            </constraints>
                                                            <attributedString key="attributedText">
                                                                <fragment content="Password">
                                                                    <attributes>
                                                                        <color key="NSColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" metaFont="system" size="17"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                    </attributes>
                                                                </fragment>
                                                                <fragment content="*">
                                                                    <attributes>
                                                                        <color key="NSColor" red="1" green="0.0093600000000000003" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" size="17" name="HelveticaNeue"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pF7-nU-fAl">
                                                            <rect key="frame" x="0.0" y="24" width="345" height="48"/>
                                                            <subviews>
                                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="At least 6 characters" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="e7B-fC-TKB">
                                                                    <rect key="frame" x="24" y="0.0" width="321" height="48"/>
                                                                    <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                                                                </textField>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="e7B-fC-TKB" secondAttribute="bottom" id="1wx-h5-6MS"/>
                                                                <constraint firstAttribute="height" constant="48" id="bdF-cJ-57x"/>
                                                                <constraint firstItem="e7B-fC-TKB" firstAttribute="top" secondItem="pF7-nU-fAl" secondAttribute="top" id="iR8-IR-F7e"/>
                                                                <constraint firstItem="e7B-fC-TKB" firstAttribute="leading" secondItem="pF7-nU-fAl" secondAttribute="leading" constant="24" id="sKS-7Y-3q8"/>
                                                                <constraint firstAttribute="trailing" secondItem="e7B-fC-TKB" secondAttribute="trailing" id="xNS-J9-2OW"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                    <integer key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="bXo-hR-Bkl" secondAttribute="trailing" id="JAZ-o0-wJG"/>
                                                <constraint firstItem="bXo-hR-Bkl" firstAttribute="top" secondItem="Kax-aj-2bc" secondAttribute="top" id="WYO-oA-tUz"/>
                                                <constraint firstItem="bXo-hR-Bkl" firstAttribute="leading" secondItem="Kax-aj-2bc" secondAttribute="leading" id="gux-m0-3sb"/>
                                                <constraint firstAttribute="bottom" secondItem="bXo-hR-Bkl" secondAttribute="bottom" id="wF0-kO-J96"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3TF-HL-xgW">
                                            <rect key="frame" x="0.0" y="400" width="345" height="72"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="pHo-lk-pzb">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="72"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PMq-An-4Hc">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="kw5-ae-Ao7"/>
                                                            </constraints>
                                                            <attributedString key="attributedText">
                                                                <fragment content="Confirm Password">
                                                                    <attributes>
                                                                        <color key="NSColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" metaFont="system" size="17"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                    </attributes>
                                                                </fragment>
                                                                <fragment content="*">
                                                                    <attributes>
                                                                        <color key="NSColor" red="1" green="0.0093600000000000003" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                        <font key="NSFont" size="17" name="HelveticaNeue"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JOX-Ec-IYe">
                                                            <rect key="frame" x="0.0" y="24" width="345" height="48"/>
                                                            <subviews>
                                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="lOx-oI-qUc">
                                                                    <rect key="frame" x="24" y="0.0" width="321" height="48"/>
                                                                    <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                                                                </textField>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="lOx-oI-qUc" secondAttribute="bottom" id="JSd-9Q-lOp"/>
                                                                <constraint firstItem="lOx-oI-qUc" firstAttribute="leading" secondItem="JOX-Ec-IYe" secondAttribute="leading" constant="24" id="ZcD-QL-xBs"/>
                                                                <constraint firstAttribute="trailing" secondItem="lOx-oI-qUc" secondAttribute="trailing" id="ZfC-Q3-uwi"/>
                                                                <constraint firstItem="lOx-oI-qUc" firstAttribute="top" secondItem="JOX-Ec-IYe" secondAttribute="top" id="f5X-Zw-DZq"/>
                                                                <constraint firstAttribute="height" constant="48" id="jHb-aM-d3a"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                    <integer key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="pHo-lk-pzb" firstAttribute="leading" secondItem="3TF-HL-xgW" secondAttribute="leading" id="0cl-4M-jed"/>
                                                <constraint firstAttribute="bottom" secondItem="pHo-lk-pzb" secondAttribute="bottom" id="hjj-6w-xjo"/>
                                                <constraint firstItem="pHo-lk-pzb" firstAttribute="top" secondItem="3TF-HL-xgW" secondAttribute="top" id="rEG-F9-Jua"/>
                                                <constraint firstAttribute="trailing" secondItem="pHo-lk-pzb" secondAttribute="trailing" id="sbz-6S-Fxo"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bMc-Qb-lFH">
                                            <rect key="frame" x="0.0" y="488" width="345" height="72"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="aWu-nG-1Zy">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="72"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Invitation Code" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nKL-Qw-Bnf">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="jdS-Aq-Jbm"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="17"/>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xsz-Xf-Wqf">
                                                            <rect key="frame" x="0.0" y="24" width="345" height="48"/>
                                                            <subviews>
                                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Jkc-kh-EXp">
                                                                    <rect key="frame" x="24" y="0.0" width="321" height="48"/>
                                                                    <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="Jkc-kh-EXp" secondAttribute="bottom" id="5Uw-Oj-Ufm"/>
                                                                <constraint firstItem="Jkc-kh-EXp" firstAttribute="leading" secondItem="Xsz-Xf-Wqf" secondAttribute="leading" constant="24" id="6Aw-Yb-QWQ"/>
                                                                <constraint firstAttribute="trailing" secondItem="Jkc-kh-EXp" secondAttribute="trailing" id="Aqw-Wr-Ypd"/>
                                                                <constraint firstItem="Jkc-kh-EXp" firstAttribute="top" secondItem="Xsz-Xf-Wqf" secondAttribute="top" id="kkc-Vj-Vdw"/>
                                                                <constraint firstAttribute="height" constant="48" id="mVx-Hb-Ggc"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                    <integer key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="aWu-nG-1Zy" firstAttribute="leading" secondItem="bMc-Qb-lFH" secondAttribute="leading" id="3mf-Qj-Ygf"/>
                                                <constraint firstAttribute="bottom" secondItem="aWu-nG-1Zy" secondAttribute="bottom" id="Nj7-Nt-Xhv"/>
                                                <constraint firstItem="aWu-nG-1Zy" firstAttribute="top" secondItem="bMc-Qb-lFH" secondAttribute="top" id="ZXS-HL-Wqq"/>
                                                <constraint firstAttribute="trailing" secondItem="aWu-nG-1Zy" secondAttribute="trailing" id="pIa-Ld-Tnj"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2ig-uA-Iwo">
                                            <rect key="frame" x="0.0" y="576" width="345" height="136.33333333333337"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="28" translatesAutoresizingMaskIntoConstraints="NO" id="5Ur-zr-JP9">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="136.33333333333334"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="w6U-lZ-TYj">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="60.333333333333336"/>
                                                            <subviews>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gBd-Wa-duk">
                                                                    <rect key="frame" x="0.0" y="2" width="14" height="14"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="14" id="DPv-Qy-5s8"/>
                                                                        <constraint firstAttribute="width" constant="14" id="F5s-8C-2wX"/>
                                                                    </constraints>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <state key="normal" image="uncheckMarkDark"/>
                                                                    <state key="selected" image="checkMark"/>
                                                                    <connections>
                                                                        <action selector="agreeCheckBoxAction:" destination="-1" eventType="touchUpInside" id="zru-cO-rYJ"/>
                                                                    </connections>
                                                                </button>
                                                                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="I agree with Privacy Policy &amp; Terms of Conditions" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fbh-T7-jTd">
                                                                    <rect key="frame" x="18" y="0.0" width="327" height="60.333333333333336"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="4Bi-Wj-Sgs"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                                                                    <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" text="I agree with Privacy Policy &amp; Terms of Conditions" translatesAutoresizingMaskIntoConstraints="NO" id="ezc-L3-7k5">
                                                                    <rect key="frame" x="18" y="-7" width="327" height="67.333333333333329"/>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="27" id="nu3-dQ-xfd"/>
                                                                    </constraints>
                                                                    <color key="textColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                                    <dataDetectorType key="dataDetectorTypes" link="YES"/>
                                                                    <connections>
                                                                        <outlet property="delegate" destination="-1" id="vQ4-9U-50R"/>
                                                                    </connections>
                                                                </textView>
                                                                <label opaque="NO" userInteractionEnabled="NO" alpha="0.59999999999999998" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="All fields marked with * are required." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xrm-CQ-Uel">
                                                                    <rect key="frame" x="0.0" y="26.000000000000004" width="345" height="34.333333333333343"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                                                                    <color key="textColor" red="0.19250999999999999" green="0.057950000000000002" blue="0.35265999999999997" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="Fbh-T7-jTd" secondAttribute="trailing" id="06D-jk-exB"/>
                                                                <constraint firstItem="ezc-L3-7k5" firstAttribute="leading" secondItem="Fbh-T7-jTd" secondAttribute="leading" id="2Cd-I5-4Ya"/>
                                                                <constraint firstItem="Fbh-T7-jTd" firstAttribute="leading" secondItem="gBd-Wa-duk" secondAttribute="trailing" constant="4" id="7p1-6T-tqL"/>
                                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="Aa2-pl-4X9"/>
                                                                <constraint firstAttribute="bottom" secondItem="xrm-CQ-Uel" secondAttribute="bottom" id="Sur-Mo-mAk"/>
                                                                <constraint firstItem="xrm-CQ-Uel" firstAttribute="top" secondItem="gBd-Wa-duk" secondAttribute="bottom" constant="10" id="TfH-Kz-8bl"/>
                                                                <constraint firstItem="Fbh-T7-jTd" firstAttribute="top" secondItem="w6U-lZ-TYj" secondAttribute="top" id="civ-Ec-lrX"/>
                                                                <constraint firstItem="gBd-Wa-duk" firstAttribute="top" secondItem="w6U-lZ-TYj" secondAttribute="top" constant="2" id="dPE-QO-MEO"/>
                                                                <constraint firstAttribute="bottom" secondItem="Fbh-T7-jTd" secondAttribute="bottom" id="fnb-Wf-idB"/>
                                                                <constraint firstItem="ezc-L3-7k5" firstAttribute="bottom" secondItem="Fbh-T7-jTd" secondAttribute="bottom" id="ifo-0L-Nx8"/>
                                                                <constraint firstItem="xrm-CQ-Uel" firstAttribute="leading" secondItem="w6U-lZ-TYj" secondAttribute="leading" id="j3o-0K-3TM"/>
                                                                <constraint firstItem="ezc-L3-7k5" firstAttribute="trailing" secondItem="Fbh-T7-jTd" secondAttribute="trailing" id="mei-Fm-KzM"/>
                                                                <constraint firstAttribute="trailing" secondItem="xrm-CQ-Uel" secondAttribute="trailing" id="vyE-WA-oma"/>
                                                                <constraint firstItem="gBd-Wa-duk" firstAttribute="leading" secondItem="w6U-lZ-TYj" secondAttribute="leading" id="wsf-JF-2MN"/>
                                                                <constraint firstItem="ezc-L3-7k5" firstAttribute="top" secondItem="Fbh-T7-jTd" secondAttribute="top" constant="-7" id="yCr-XV-dTa"/>
                                                            </constraints>
                                                        </view>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xAQ-5Z-oZX">
                                                            <rect key="frame" x="0.0" y="88.333333333333371" width="345" height="48"/>
                                                            <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="48" id="mZ7-nZ-DOX"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="18"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Sign Up"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                    <integer key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                            <connections>
                                                                <action selector="didTapSignUpAction:" destination="-1" eventType="touchUpInside" id="GHm-7O-Heb"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstItem="xAQ-5Z-oZX" firstAttribute="leading" secondItem="5Ur-zr-JP9" secondAttribute="leading" id="2fA-KE-IdV"/>
                                                        <constraint firstAttribute="trailing" secondItem="w6U-lZ-TYj" secondAttribute="trailing" id="Fc7-8n-8Hj"/>
                                                        <constraint firstAttribute="trailing" secondItem="xAQ-5Z-oZX" secondAttribute="trailing" id="NZt-Xo-Kr7"/>
                                                        <constraint firstItem="w6U-lZ-TYj" firstAttribute="top" secondItem="5Ur-zr-JP9" secondAttribute="top" id="Rxc-OG-vnJ"/>
                                                        <constraint firstItem="w6U-lZ-TYj" firstAttribute="leading" secondItem="5Ur-zr-JP9" secondAttribute="leading" id="jtr-8d-LOE"/>
                                                        <constraint firstAttribute="bottom" secondItem="xAQ-5Z-oZX" secondAttribute="bottom" id="n6t-J7-LRV"/>
                                                        <constraint firstItem="xAQ-5Z-oZX" firstAttribute="top" secondItem="w6U-lZ-TYj" secondAttribute="bottom" constant="28" id="wER-7e-cbK"/>
                                                    </constraints>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="5Ur-zr-JP9" secondAttribute="trailing" id="52M-Zh-gvD"/>
                                                <constraint firstItem="5Ur-zr-JP9" firstAttribute="top" secondItem="2ig-uA-Iwo" secondAttribute="top" id="gPL-if-gwZ"/>
                                                <constraint firstAttribute="bottom" secondItem="5Ur-zr-JP9" secondAttribute="bottom" id="mb9-lI-x3l"/>
                                                <constraint firstItem="5Ur-zr-JP9" firstAttribute="leading" secondItem="2ig-uA-Iwo" secondAttribute="leading" id="mdQ-uF-nOB"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="Yu2-wf-4dV" secondAttribute="trailing" constant="24" id="Vvj-My-GtD"/>
                                <constraint firstAttribute="bottom" secondItem="Yu2-wf-4dV" secondAttribute="bottom" constant="24" id="bBr-sN-8Gc"/>
                                <constraint firstItem="Yu2-wf-4dV" firstAttribute="top" secondItem="fDL-RZ-Q6H" secondAttribute="top" constant="24" id="eda-rg-CH0"/>
                                <constraint firstItem="Yu2-wf-4dV" firstAttribute="leading" secondItem="fDL-RZ-Q6H" secondAttribute="leading" constant="24" id="oA1-pZ-CRr"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="fDL-RZ-Q6H" secondAttribute="bottom" id="0WX-m6-4AH"/>
                        <constraint firstItem="fDL-RZ-Q6H" firstAttribute="top" secondItem="vqc-Tk-nvJ" secondAttribute="top" id="9gn-id-1Ai"/>
                        <constraint firstItem="fDL-RZ-Q6H" firstAttribute="width" secondItem="vqc-Tk-nvJ" secondAttribute="width" id="CJA-2j-CpX"/>
                        <constraint firstAttribute="trailing" secondItem="fDL-RZ-Q6H" secondAttribute="trailing" id="aHZ-jo-K0t"/>
                        <constraint firstItem="fDL-RZ-Q6H" firstAttribute="leading" secondItem="vqc-Tk-nvJ" secondAttribute="leading" id="wMn-m3-gsv"/>
                    </constraints>
                </scrollView>
                <view hidden="YES" alpha="0.69999999999999996" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FYB-Zj-AGK">
                    <rect key="frame" x="0.0" y="118" width="393" height="666"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OKt-Xr-gi9">
                            <rect key="frame" x="183.66666666666666" y="515" width="26" height="26"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="26" id="GDg-Ia-v8t"/>
                                <constraint firstAttribute="width" constant="26" id="OvJ-5g-Ux8"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="closeIcon"/>
                            <connections>
                                <action selector="closeTipsViewButtonAction:" destination="-1" eventType="touchUpInside" id="RNT-CG-jdN"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MBK-Zd-8zl">
                    <rect key="frame" x="40" y="201" width="313" height="400"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="bottom" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="messageViewBg" translatesAutoresizingMaskIntoConstraints="NO" id="Axz-hg-bAE">
                            <rect key="frame" x="0.0" y="0.0" width="313" height="400"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </imageView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Rl4-uU-PtS">
                            <rect key="frame" x="30" y="40" width="253" height="324"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Lq2-2z-8hU">
                                    <rect key="frame" x="0.0" y="0.0" width="253" height="57.333333333333336"/>
                                    <attributedString key="attributedText">
                                        <fragment content="Privacy Policy and Terms of Service Consent Request">
                                            <attributes>
                                                <color key="NSColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <font key="NSFont" metaFont="system" size="16"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                    <nil key="highlightedColor"/>
                                </label>
                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0x7-r3-PR9">
                                    <rect key="frame" x="0.0" y="73.3333333333333" width="253" height="186.**************"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <string key="text">Thank you for using our services! We value your privacy and want to ensure that you understand how your information is used. Before proceeding, please take a moment to review and agree to our Privacy Policy and Terms of Conditions.</string>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                    <dataDetectorType key="dataDetectorTypes" link="YES"/>
                                    <connections>
                                        <outlet property="delegate" destination="-1" id="hbo-T8-y0m"/>
                                    </connections>
                                </textView>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="j5m-QE-p2n">
                                    <rect key="frame" x="0.0" y="276" width="253" height="48"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="fyk-uJ-Zz9">
                                            <rect key="frame" x="0.0" y="0.0" width="253" height="48"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Mry-Xx-GvI">
                                                    <rect key="frame" x="0.0" y="0.0" width="130.33333333333334" height="48"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Disagree">
                                                        <color key="titleColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    </state>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="disagreeButtonAction:" destination="-1" eventType="touchUpInside" id="QwJ-kZ-FjO"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Rkn-gn-E4e">
                                                    <rect key="frame" x="148.33333333333334" y="0.0" width="104.66666666666666" height="48"/>
                                                    <color key="backgroundColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Agree">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="agreeButonAction:" destination="-1" eventType="touchUpInside" id="s4b-qi-gmx"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="fyk-uJ-Zz9" firstAttribute="leading" secondItem="j5m-QE-p2n" secondAttribute="leading" id="2Ns-bC-qQz"/>
                                        <constraint firstItem="fyk-uJ-Zz9" firstAttribute="top" secondItem="j5m-QE-p2n" secondAttribute="top" id="8Mt-4V-tV9"/>
                                        <constraint firstAttribute="bottom" secondItem="fyk-uJ-Zz9" secondAttribute="bottom" id="ATp-4I-1Gi"/>
                                        <constraint firstAttribute="height" constant="48" id="DqP-qq-GIZ"/>
                                        <constraint firstAttribute="trailing" secondItem="fyk-uJ-Zz9" secondAttribute="trailing" id="dR4-60-Jj7"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="j5m-QE-p2n" secondAttribute="bottom" id="iid-0C-mm9"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="Rl4-uU-PtS" firstAttribute="top" secondItem="MBK-Zd-8zl" secondAttribute="top" constant="40" id="3P3-JC-Fg8"/>
                        <constraint firstItem="Rl4-uU-PtS" firstAttribute="leading" secondItem="MBK-Zd-8zl" secondAttribute="leading" constant="30" id="4YE-Sx-Tzy"/>
                        <constraint firstItem="Axz-hg-bAE" firstAttribute="leading" secondItem="MBK-Zd-8zl" secondAttribute="leading" id="9Gp-hp-XNk"/>
                        <constraint firstAttribute="trailing" secondItem="Axz-hg-bAE" secondAttribute="trailing" id="9aj-Xd-e6N"/>
                        <constraint firstAttribute="bottom" secondItem="Rl4-uU-PtS" secondAttribute="bottom" constant="36" id="Iju-Hg-znN"/>
                        <constraint firstAttribute="trailing" secondItem="Rl4-uU-PtS" secondAttribute="trailing" constant="30" id="LsN-jx-65x"/>
                        <constraint firstAttribute="height" constant="400" id="U06-GX-cmd"/>
                        <constraint firstAttribute="bottom" secondItem="Axz-hg-bAE" secondAttribute="bottom" id="n3r-PM-Ice"/>
                        <constraint firstItem="Axz-hg-bAE" firstAttribute="top" secondItem="MBK-Zd-8zl" secondAttribute="top" id="wBM-Lu-E06"/>
                    </constraints>
                </view>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pnO-Dw-bWT">
                    <rect key="frame" x="0.0" y="524" width="393" height="328"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Daf-U1-sjt">
                            <rect key="frame" x="353" y="14" width="16" height="16"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="16" id="slv-jZ-s9B"/>
                                <constraint firstAttribute="height" constant="16" id="xVp-mv-v9S"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="18"/>
                            <color key="tintColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="purpleClose"/>
                            <connections>
                                <action selector="accountTypeSelectViewCloseButtonAction:" destination="-1" eventType="touchUpInside" id="g3b-31-1uZ"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sign Up" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="28v-xT-cpN">
                            <rect key="frame" x="24" y="30" width="345" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="ruf-rQ-zJj"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <color key="textColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="c1a-ie-cz0">
                            <rect key="frame" x="24" y="70" width="345" height="36"/>
                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="36" id="uX7-wC-aZy"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Email Address"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="accountTypeSelectViewEmailAction:" destination="-1" eventType="touchUpInside" id="m6h-lB-Qbb"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kOa-RT-nuk">
                            <rect key="frame" x="24" y="122" width="345" height="36"/>
                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="36" id="T9U-s9-ib7"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Mobile Number"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="accountTypeSelectViewMobileAction:" destination="-1" eventType="touchUpInside" id="8eS-lG-k0e"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qEO-5q-TUO">
                            <rect key="frame" x="24" y="246" width="345" height="48"/>
                            <color key="backgroundColor" red="0.**********" green="0.***********9999998" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="48" id="OPL-yf-Z9S"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Save"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="accountTypeSelectViewSaveAction:" destination="-1" eventType="touchUpInside" id="q7M-jq-lU5"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="c1a-ie-cz0" secondAttribute="trailing" constant="24" id="1FU-qa-2rp"/>
                        <constraint firstItem="28v-xT-cpN" firstAttribute="top" secondItem="pnO-Dw-bWT" secondAttribute="top" constant="30" id="5Tj-nb-Iqp"/>
                        <constraint firstItem="c1a-ie-cz0" firstAttribute="leading" secondItem="pnO-Dw-bWT" secondAttribute="leading" constant="24" id="7Na-3m-T0I"/>
                        <constraint firstAttribute="trailing" secondItem="kOa-RT-nuk" secondAttribute="trailing" constant="24" id="8PU-uB-ASr"/>
                        <constraint firstAttribute="trailing" secondItem="28v-xT-cpN" secondAttribute="trailing" constant="24" id="8jf-wR-QU8"/>
                        <constraint firstItem="Daf-U1-sjt" firstAttribute="top" secondItem="pnO-Dw-bWT" secondAttribute="top" constant="14" id="CCJ-Pg-BPh"/>
                        <constraint firstItem="c1a-ie-cz0" firstAttribute="top" secondItem="28v-xT-cpN" secondAttribute="bottom" constant="20" id="PTQ-Ps-BFI"/>
                        <constraint firstAttribute="trailing" secondItem="Daf-U1-sjt" secondAttribute="trailing" constant="24" id="Prz-Fk-ivR"/>
                        <constraint firstAttribute="bottom" secondItem="qEO-5q-TUO" secondAttribute="bottom" constant="34" id="XV6-vv-SGK"/>
                        <constraint firstAttribute="height" constant="328" id="Xuy-M1-DA7"/>
                        <constraint firstItem="28v-xT-cpN" firstAttribute="leading" secondItem="pnO-Dw-bWT" secondAttribute="leading" constant="24" id="ZBc-KE-87t"/>
                        <constraint firstAttribute="trailing" secondItem="qEO-5q-TUO" secondAttribute="trailing" constant="24" id="dKn-wH-FRf"/>
                        <constraint firstItem="qEO-5q-TUO" firstAttribute="leading" secondItem="pnO-Dw-bWT" secondAttribute="leading" constant="24" id="dwb-U0-zab"/>
                        <constraint firstItem="kOa-RT-nuk" firstAttribute="top" secondItem="c1a-ie-cz0" secondAttribute="bottom" constant="16" id="lXq-S1-9Sx"/>
                        <constraint firstItem="kOa-RT-nuk" firstAttribute="leading" secondItem="pnO-Dw-bWT" secondAttribute="leading" constant="24" id="tYb-h7-jlm"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="12"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="pnO-Dw-bWT" secondAttribute="bottom" id="3MC-Dk-Ufl"/>
                <constraint firstItem="FYB-Zj-AGK" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="4ab-bc-llK"/>
                <constraint firstItem="FYB-Zj-AGK" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="G6t-Va-Tjl"/>
                <constraint firstItem="MBK-Zd-8zl" firstAttribute="centerY" secondItem="fnl-2z-Ty3" secondAttribute="centerY" constant="-50" id="IeE-wX-uPT"/>
                <constraint firstItem="vqc-Tk-nvJ" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="UKU-0q-Tou"/>
                <constraint firstItem="OKt-Xr-gi9" firstAttribute="bottom" secondItem="MBK-Zd-8zl" secondAttribute="bottom" constant="58" id="X0b-CR-3mh"/>
                <constraint firstAttribute="bottom" secondItem="vqc-Tk-nvJ" secondAttribute="bottom" id="ZtF-Ze-zpt"/>
                <constraint firstItem="MBK-Zd-8zl" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="40" id="bI7-3x-odg"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="FYB-Zj-AGK" secondAttribute="bottom" id="cAk-OX-UUd"/>
                <constraint firstItem="pnO-Dw-bWT" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="dZh-dC-yhH"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="FYB-Zj-AGK" secondAttribute="trailing" id="hmH-6D-PqT"/>
                <constraint firstItem="vqc-Tk-nvJ" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="l4a-az-ka5"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="pnO-Dw-bWT" secondAttribute="trailing" id="o9d-3v-lyP"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="MBK-Zd-8zl" secondAttribute="trailing" constant="40" id="sID-qa-DJW"/>
                <constraint firstItem="vqc-Tk-nvJ" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="uCf-oE-WSA"/>
                <constraint firstItem="OKt-Xr-gi9" firstAttribute="centerX" secondItem="MBK-Zd-8zl" secondAttribute="centerX" id="wpG-hV-WBo"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-12.67605633802817"/>
        </view>
    </objects>
    <resources>
        <image name="checkMark" width="16" height="16"/>
        <image name="closeIcon" width="32" height="32"/>
        <image name="forwarIcon" width="16" height="16"/>
        <image name="messageViewBg" width="514" height="514"/>
        <image name="purpleClose" width="20" height="20"/>
        <image name="uncheckMarkDark" width="16" height="16"/>
    </resources>
</document>
