<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="WebViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
                <outlet property="webView" destination="eTa-ZH-azZ" id="I1Y-vP-aXg"/>
                <outlet property="webViewBottomConstraint" destination="Hnh-s0-B3y" id="EAj-gT-9JY"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <wkWebView contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="eTa-ZH-azZ">
                    <rect key="frame" x="0.0" y="59" width="393" height="793"/>
                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <wkWebViewConfiguration key="configuration">
                        <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                        <wkPreferences key="preferences"/>
                    </wkWebViewConfiguration>
                </wkWebView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="eTa-ZH-azZ" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="4b6-Sj-QJe"/>
                <constraint firstAttribute="bottom" secondItem="eTa-ZH-azZ" secondAttribute="bottom" id="Hnh-s0-B3y"/>
                <constraint firstItem="eTa-ZH-azZ" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="KIO-5m-Hc9"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
