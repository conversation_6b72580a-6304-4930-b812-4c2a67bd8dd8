//
//  TandCPopupViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/9.
//

import UIKit

protocol TandCPopupViewControllerDelegate: AnyObject {
    func didTapDisagree()
    func didTapAgree()
}

class TandCPopupViewController: UIViewController {

    @IBOutlet weak var termsContent: UITextView!
    @IBOutlet weak var disagreeBtn: UIButton!
    
    let privacyPolicy = "Privacy Policy"
    let tAndcString = "Terms of Conditions"
    
    weak var delegate: TandCPopupViewControllerDelegate?
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .black.withAlphaComponent(0.5)
        termsContent.delegate = self
        termsContent.underlineWords(words: [privacyPolicy, tAndcString])
        
        disagreeBtn.layer.borderWidth = 1
        disagreeBtn.layer.borderColor = UIColor.mainTextColor.cgColor
        disagreeBtn.layer.cornerRadius = 4
    }
    @IBAction func closeTap(_ sender: Any) {
        navigationController?.popViewController(animated: false)
    }
    
    @IBAction func disagreeTap(_ sender: Any) {
        closeTap(UIButton())
        delegate?.didTapDisagree()
    }
    
    @IBAction func agreeTap(_ sender: Any) {
        closeTap(UIButton())
        delegate?.didTapAgree()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = true
    }
}

extension TandCPopupViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        
        let range = (textView.text as NSString).range(of: privacyPolicy)
        if characterRange == range {
            AppInfoInteractor.fetchAppInfo(2) { result in
                guard let appInfo = result else { return }
                let webView = WebViewController(appInfo.url, contentString: appInfo.content)
                webView.title = self.privacyPolicy
                self.navigationController?.pushViewController(webView, animated: true)
            }
        } else {
            AppInfoInteractor.fetchAppInfo(3) { result in
                guard let appInfo = result else { return }
                let webView = WebViewController(appInfo.url, contentString: appInfo.content)
                webView.title = self.tAndcString
                self.navigationController?.pushViewController(webView, animated: true)
            }
        }
        
        return false
    }
}
