<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="TandCPopupViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="disagreeBtn" destination="J8k-of-miG" id="ik9-2g-R30"/>
                <outlet property="termsContent" destination="BAy-hE-GOf" id="sWi-YF-SSV"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7Uu-rb-cDo">
                    <rect key="frame" x="40" y="166" width="313" height="400"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="bottom" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="messageViewBg" translatesAutoresizingMaskIntoConstraints="NO" id="qYC-1p-G7g">
                            <rect key="frame" x="0.0" y="0.0" width="313" height="400"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </imageView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="yio-3R-6Kn">
                            <rect key="frame" x="30" y="40" width="253" height="324"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ras-eF-ZMM">
                                    <rect key="frame" x="0.0" y="0.0" width="253" height="57.333333333333336"/>
                                    <attributedString key="attributedText">
                                        <fragment content="Privacy Policy and Terms of Service Consent Request">
                                            <attributes>
                                                <color key="NSColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <font key="NSFont" size="16" name="Gilroy-SemiBold"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                    <nil key="highlightedColor"/>
                                </label>
                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BAy-hE-GOf">
                                    <rect key="frame" x="0.0" y="73.3333333333333" width="253" height="186.66666666666663"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <string key="text">Thank you for using our services! We value your privacy and want to ensure that you understand how your information is used. Before proceeding, please take a moment to review and agree to our Privacy Policy and Terms of Conditions.</string>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <fontDescription key="fontDescription" name=".AppleSystemUIFont" family=".AppleSystemUIFont" pointSize="14"/>
                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                    <dataDetectorType key="dataDetectorTypes" link="YES"/>
                                </textView>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="b2N-q8-dkw">
                                    <rect key="frame" x="0.0" y="276" width="253" height="48"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="2Pz-59-mO1">
                                            <rect key="frame" x="0.0" y="0.0" width="253" height="48"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="J8k-of-miG">
                                                    <rect key="frame" x="0.0" y="0.0" width="130.66666666666666" height="48"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Disagree">
                                                        <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    </state>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="disagreeTap:" destination="-1" eventType="touchUpInside" id="51W-JM-MQ0"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KTK-4C-So0">
                                                    <rect key="frame" x="148.66666666666666" y="0.0" width="104.33333333333334" height="48"/>
                                                    <color key="backgroundColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Agree">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="agreeTap:" destination="-1" eventType="touchUpInside" id="ure-Vc-Lwg"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="2Pz-59-mO1" secondAttribute="trailing" id="72V-MT-NNG"/>
                                        <constraint firstAttribute="bottom" secondItem="2Pz-59-mO1" secondAttribute="bottom" id="9VA-MV-Fzi"/>
                                        <constraint firstItem="2Pz-59-mO1" firstAttribute="leading" secondItem="b2N-q8-dkw" secondAttribute="leading" id="Iqc-bD-GaE"/>
                                        <constraint firstItem="2Pz-59-mO1" firstAttribute="top" secondItem="b2N-q8-dkw" secondAttribute="top" id="S3o-KL-Off"/>
                                        <constraint firstAttribute="height" constant="48" id="wer-wS-MRN"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="b2N-q8-dkw" secondAttribute="bottom" id="DZa-Tc-I7H"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="gwM-0m-eoo"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="400" id="28P-3q-CqQ"/>
                        <constraint firstItem="yio-3R-6Kn" firstAttribute="leading" secondItem="7Uu-rb-cDo" secondAttribute="leading" constant="30" id="3nL-lN-G8J"/>
                        <constraint firstAttribute="bottom" secondItem="qYC-1p-G7g" secondAttribute="bottom" id="9CD-CZ-YDz"/>
                        <constraint firstAttribute="trailing" secondItem="yio-3R-6Kn" secondAttribute="trailing" constant="30" id="BpA-75-5G7"/>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="400" id="Fc4-G7-WAz"/>
                        <constraint firstAttribute="trailing" secondItem="qYC-1p-G7g" secondAttribute="trailing" id="Vri-sQ-HVs"/>
                        <constraint firstItem="yio-3R-6Kn" firstAttribute="top" secondItem="7Uu-rb-cDo" secondAttribute="top" constant="40" id="WwD-Gs-XhH"/>
                        <constraint firstItem="qYC-1p-G7g" firstAttribute="leading" secondItem="7Uu-rb-cDo" secondAttribute="leading" id="XI2-Bp-1MA"/>
                        <constraint firstItem="qYC-1p-G7g" firstAttribute="top" secondItem="7Uu-rb-cDo" secondAttribute="top" id="ihC-kJ-ANM"/>
                        <constraint firstAttribute="bottom" secondItem="yio-3R-6Kn" secondAttribute="bottom" constant="36" id="yY1-tT-hTA"/>
                    </constraints>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GxD-CM-1Om">
                    <rect key="frame" x="183.66666666666666" y="596" width="26" height="26"/>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="26" id="6q3-uV-eyY"/>
                        <constraint firstAttribute="width" constant="26" id="Bhg-ck-JQc"/>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="26" id="Lae-fd-htu"/>
                        <constraint firstAttribute="height" constant="26" id="Ty5-Iq-nM5"/>
                    </constraints>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" image="closeIcon"/>
                    <connections>
                        <action selector="closeTap:" destination="-1" eventType="touchUpInside" id="BC8-92-ra4"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="GxD-CM-1Om" firstAttribute="top" secondItem="7Uu-rb-cDo" secondAttribute="bottom" constant="30" id="IXI-Gg-6v4"/>
                <constraint firstItem="7Uu-rb-cDo" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="40" id="Pxa-3N-Ngh"/>
                <constraint firstItem="GxD-CM-1Om" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="T3P-9C-u7n"/>
                <constraint firstItem="7Uu-rb-cDo" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" constant="-60" id="i63-aa-iiZ"/>
                <constraint firstItem="7Uu-rb-cDo" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="vKC-tx-w7F"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="7Uu-rb-cDo" secondAttribute="trailing" constant="40" id="xDU-Fp-oux"/>
            </constraints>
            <point key="canvasLocation" x="-68" y="-12"/>
        </view>
    </objects>
    <resources>
        <image name="closeIcon" width="32" height="32"/>
        <image name="messageViewBg" width="514" height="514"/>
    </resources>
</document>
