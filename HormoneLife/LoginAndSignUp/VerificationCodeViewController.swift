import UIKit

// MARK: - 自定义输入框：支持空文本退格回调
private class CodeTextField: UITextField {
    /// 当在空文本状态下按下退格键时触发
    var onDeleteBackward: (() -> Void)?

    override func deleteBackward() {
        if (text ?? "").isEmpty {
            onDeleteBackward?()
        }
        super.deleteBackward()
    }
}

class VerificationCodeViewController: BaseViewController {
    // MARK: - 传入参数
    var account: String = ""
    var firstName: String = ""
    var lastName: String = ""
    var password: String = ""
    var invitationCode: String = ""
    var loginType: LoginType = .phone
    
    // MARK: - 私有视图
    private let infoLabel = UILabel()
    private let emailLabel = UILabel()
    private let codeStackView = UIStackView()
    // 新增顶部图标和描述文本
    private let iconImageView = UIImageView()
    private let descriptionLabel = UILabel()
    private var codeFields: [CodeTextField] = []
    private let errorLabel = UILabel()
    private let confirmButton = UIButton(type: .system)
    private let resendButton = UIButton(type: .system)
    // 添加倒计时标签，避免按钮文字闪烁
    private let countdownLabel = UILabel()
    // 添加验证码分隔线
    private let separatorLine = UIView()
    
    private var timer: Timer?
    private var countDown: Int = 60
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Sign Up"
        view.backgroundColor = UIColor("#F1EDF9")
        setupUI()
        sendValidateCode()
    }
    
    deinit {
        timer?.invalidate()
    }
    
    // MARK: - UI 构建
    private func setupUI() {
        // 顶部图标
        iconImageView.image = UIImage(named: "ic_verification")
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.heightAnchor.constraint(equalToConstant: 90).isActive = true
        iconImageView.widthAnchor.constraint(equalToConstant: 90).isActive = true
        
        infoLabel.text = "Verification Code"
        infoLabel.font = .mediumGilroyFont(22)
        infoLabel.textColor = .mainTextColor
        infoLabel.textAlignment = .center
        
        // 账号文本加粗
        emailLabel.text = account
        emailLabel.font = .boldGilroyFont(16)
        emailLabel.textColor = .mainTextColor
        emailLabel.textAlignment = .center
        emailLabel.numberOfLines = 0
        
        // 描述文字
        descriptionLabel.text = "Enter the 6-digit code sent to your registered email or phone."
        descriptionLabel.font = .regularGilroyFont(16)
        descriptionLabel.textColor = .mainTextColor.withAlphaComponent(0.6)
        descriptionLabel.textAlignment = .center
        descriptionLabel.numberOfLines = 0
        
        // 验证码输入框
        codeStackView.axis = .horizontal
        codeStackView.alignment = .fill
        codeStackView.distribution = .fillEqually
        codeStackView.spacing = 12
        
        for i in 0..<6 {
            let tf = CodeTextField()
            tf.delegate = self
            tf.tag = i
            tf.keyboardType = .numberPad
            tf.textAlignment = .center
            tf.font = .mediumGilroyFont(24)
            tf.layer.borderColor = UIColor.mainTextColor.cgColor
            tf.layer.borderWidth = 1
            tf.layer.cornerRadius = 8
            tf.backgroundColor = .white
            tf.addTarget(self, action: #selector(textFieldDidChange(_:)), for: .editingChanged)
            // 退格回调：空文本时删除前一位并跳转
            tf.onDeleteBackward = { [weak self, weak tf] in
                guard let self = self, let tf = tf else { return }
                let previousIndex = tf.tag - 1
                if previousIndex >= 0 {
                    let prevField = self.codeFields[previousIndex]
                    prevField.text = ""
                    prevField.becomeFirstResponder()
                }
                self.updateConfirmButtonState()
            }

            codeFields.append(tf)
            codeStackView.addArrangedSubview(tf)
            tf.widthAnchor.constraint(equalToConstant: 48).isActive = true
            tf.heightAnchor.constraint(equalToConstant: 56).isActive = true
        }
        
        // 添加验证码分隔线
        separatorLine.backgroundColor = .mainTextColor
        separatorLine.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(separatorLine)
        
        errorLabel.textColor = .systemRed
        errorLabel.font = .regularGilroyFont(14)
        errorLabel.textAlignment = .center
        errorLabel.numberOfLines = 0
        
        // 确认按钮
        confirmButton.setTitle("Confirm", for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.backgroundColor = UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)
        confirmButton.layer.cornerRadius = 8
        confirmButton.isEnabled = false
        confirmButton.addTarget(self, action: #selector(confirmAction), for: .touchUpInside)
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(confirmButton)
        
        // 重发按钮
        resendButton.setTitle("Resend Code", for: .normal)
        resendButton.setTitleColor(.mainTextColor, for: .normal)
        resendButton.addTarget(self, action: #selector(resendAction), for: .touchUpInside)
        resendButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(resendButton)
        
        // 设置倒计时标签
        countdownLabel.font = .regularGilroyFont(16)
        countdownLabel.textColor = UIColor.mainTextColor.withAlphaComponent(0.6)
        countdownLabel.textAlignment = .center
        countdownLabel.translatesAutoresizingMaskIntoConstraints = false
        countdownLabel.isHidden = true
        view.addSubview(countdownLabel)
        
        // 初始展示带下划线的样式
        updateResendButtonTitle()
        
        // 组装主要内容的垂直栈视图
        let contentStack = UIStackView(arrangedSubviews: [infoLabel, descriptionLabel, emailLabel, codeStackView, errorLabel])
        contentStack.axis = .vertical
        contentStack.alignment = .center
        contentStack.spacing = 24
        contentStack.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(contentStack)
        
        // 添加图标到视图
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(iconImageView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            // 顶部图标距离导航栏40pt
            iconImageView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 40),
            iconImageView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            
            // 内容栈视图
            contentStack.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: 24),
            contentStack.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 24),
            contentStack.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -24),
            
            // 验证码分隔线位于第3个和第4个输入框之间，宽度改为8pt，并确保居中
            separatorLine.centerYAnchor.constraint(equalTo: codeStackView.centerYAnchor),
            separatorLine.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            separatorLine.widthAnchor.constraint(equalToConstant: 8),
            separatorLine.heightAnchor.constraint(equalToConstant: 1),
            
            // 确认按钮放在上方
            confirmButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            confirmButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            confirmButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -82),
            confirmButton.heightAnchor.constraint(equalToConstant: 52),
            
            // 重发按钮放在确认按钮下方
            resendButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            resendButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -30)
            ,
            // 倒计时标签与重发按钮位置相同
            countdownLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            countdownLabel.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -30)
        ])
    }
    
    // MARK: - 输入变化
    @objc private func textFieldDidChange(_ textField: UITextField) {
        guard let text = textField.text else { return }
        if text.count > 1 {
            textField.text = String(text.prefix(1))
        }
        if text.count == 1 {
            let nextIndex = textField.tag + 1
            if nextIndex < codeFields.count {
                codeFields[nextIndex].becomeFirstResponder()
            } else {
                textField.resignFirstResponder()
            }
        }
        updateConfirmButtonState()
    }
    
    private func updateConfirmButtonState() {
        let enabled = codeFields.allSatisfy { ($0.text ?? "").count == 1 }
        confirmButton.isEnabled = enabled
        confirmButton.backgroundColor = enabled ? .mainTextColor : UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)
    }
    
    // MARK: - 按钮动作
    @objc private func confirmAction() {
        let code = codeFields.compactMap { $0.text }.joined()
        UserInteractor.registerByCode(account: account, code: code, codeType: loginType, firstName: firstName, lastName: lastName, password: password, invitationCode: invitationCode) { [weak self] data in
            guard let self = self else { return }
            guard let token = data else {
                DispatchQueue.main.async {
                    self.errorLabel.text = "Verification code error, please re-enter"
                }
                return
            }
            UserDefaults.standard.userToken = token
            UserDefaults.standard.hasSeemGuidelinePage = false
            UserDefaults.standard.isLoginAsGuestWithoutAccount = false
            UserDefaults.standard.isFristTimeChooseGoal = true
            DispatchQueue.main.async {
                let roleSwitchVC = GoalSettingViewController()
                self.navigationController?.pushViewController(roleSwitchVC, animated: true)
            }
        }
    }
    
    @objc private func resendAction() {
        guard timer == nil else { return }
        sendValidateCode()
    }
    
    // MARK: - 网络请求 & 计时器
    // 统一的验证码请求方法：进入页面和点击“Resend Code”都会调用此方法
    private func sendValidateCode() {
        let sendValidation = SendValidate(account: account, codeType: loginType, messageCodeType: .REGISTER)

        // DEBUG: 打印请求参数，便于排查问题
        print("[VerificationCode] 🚀 发送验证码请求 → account: \(account), loginType: \(loginType), firstName: \(firstName), lastName: \(lastName)")

        UserInteractor.sendValidateCode(sendValidation) { [weak self] success in
            // DEBUG: 打印接口返回结果
            print("[VerificationCode] 🎯 获取验证码接口返回 → success: \(String(describing: success))")

            guard let self = self else { return }
            guard let s = success, s else {
                // 如果接口返回失败，保留在此处继续显示错误 HUD 的逻辑（假设网络层已处理）
                return
            }
            DispatchQueue.main.async {
                self.startTimer()
            }
        }
    }
    
    private func startTimer() {
        countDown = 60
        resendButton.isEnabled = false
        // 隐藏按钮，显示倒计时标签
        resendButton.isHidden = true
        countdownLabel.isHidden = false
        countdownLabel.text = "Resend in \(countDown)s"
        
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.countDown -= 1
            self.countdownLabel.text = "Resend in \(self.countDown)s"
            
            if self.countDown == 0 {
                self.timer?.invalidate()
                self.timer = nil
                self.resendButton.isEnabled = true
                // 显示按钮，隐藏倒计时标签
                self.resendButton.isHidden = false
                self.countdownLabel.isHidden = true
                self.updateResendButtonTitle()
            }
        }
        // 移除立即触发，避免 UI 闪烁
    }
    
    private func updateResendButtonTitle() {
        // 只设置可点击状态的按钮样式，倒计时使用单独的标签
        let attr = NSAttributedString(string: "Resend Code", attributes: [
            .underlineStyle: NSUnderlineStyle.single.rawValue,
            .font: UIFont.regularGilroyFont(16),
            .foregroundColor: UIColor.mainTextColor
        ])
        resendButton.setAttributedTitle(attr, for: .normal)
    }
}

// MARK: - UITextFieldDelegate
extension VerificationCodeViewController: UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        // 如果是退格操作
        if string.isEmpty {
            // 当前字段已有内容 -> 允许正常删除
            if let text = textField.text, !text.isEmpty {
                // 删除当前字符后，如果用户继续退格并当前为空，会触发下面的逻辑
                return true
            }
            // 当前字段为空，光标自动跳到前一个输入框并删除其内容
            let previousIndex = textField.tag - 1
            if previousIndex >= 0 {
                let prevField = codeFields[previousIndex]
                prevField.text = ""
                // 使用异步切换焦点，避免在同一事件循环中修改 firstResponder 失效
                DispatchQueue.main.async {
                    prevField.becomeFirstResponder()
                }
            }
            // 更新按钮状态
            updateConfirmButtonState()
            return false
        }
        // 只允许输入数字
        guard string.rangeOfCharacter(from: CharacterSet.decimalDigits.inverted) == nil else { return false }
        // 计算替换后文本，确保始终最多 1 个字符
        let currentText = textField.text ?? ""
        let prospectiveText = (currentText as NSString).replacingCharacters(in: range, with: string)
        return prospectiveText.count <= 1
    }
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        textField.layer.borderColor = UIColor.labelBackColor.cgColor
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        textField.layer.borderColor = UIColor.mainTextColor.cgColor
    }
}
