<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="LoginViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="accountTextField" destination="Da1-Vi-76H" id="xEJ-n0-7nb"/>
                <outlet property="appleLoginBtn" destination="yAd-FK-djA" id="JbV-il-4dx"/>
                <outlet property="countryCodeButton" destination="ID2-cf-C52" id="75Y-dP-IFw"/>
                <outlet property="errorLabel" destination="ay3-uH-EX4" id="H9C-bI-FUY"/>
                <outlet property="errorView" destination="Dxh-Ns-uox" id="jxN-FD-t98"/>
                <outlet property="facebookBgView" destination="ew4-Z3-5uy" id="8L5-pK-s2N"/>
                <outlet property="facebookLoginBtn" destination="ALl-wC-sRf" id="jf8-aU-dBV"/>
                <outlet property="loginButton" destination="ReR-lE-KsZ" id="FYb-Bh-BX8"/>
                <outlet property="loginSelectedContainView" destination="c7f-5z-1Bf" id="cSd-tB-UwA"/>
                <outlet property="loginSelectedView" destination="vVO-iB-d7L" id="b7F-tg-7Rz"/>
                <outlet property="loginSelectedViewLeadingContraint" destination="cVp-N4-e4l" id="Xim-Dd-enD"/>
                <outlet property="privatePoliceLabel" destination="vw7-B8-b8P" id="CnI-K2-45D"/>
                <outlet property="pwdTextField" destination="Utw-xM-Bgh" id="in3-hb-FOZ"/>
                <outlet property="rememberAcountBtn" destination="Hr4-CL-pRU" id="dAZ-cv-KKu"/>
                <outlet property="signUpNow" destination="biL-vZ-sCm" id="RSr-Ol-Czj"/>
                <outlet property="tandcCheckBoxButton" destination="yhH-1i-4rI" id="6CT-td-zuY"/>
                <outlet property="termsOfConditionLabel" destination="su6-FD-bxA" id="T6j-MC-iJm"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="loginBg" translatesAutoresizingMaskIntoConstraints="NO" id="FSc-Rd-RT1">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="loginViewLogo" translatesAutoresizingMaskIntoConstraints="NO" id="kGI-8v-vuN">
                    <rect key="frame" x="66" y="99" width="261" height="40"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="pcy-b8-C3H"/>
                    </constraints>
                </imageView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="c7f-5z-1Bf">
                    <rect key="frame" x="90" y="179" width="213" height="30"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="y6Q-Te-zqH">
                            <rect key="frame" x="0.0" y="0.0" width="90" height="17"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="90" id="tgU-sC-HhP"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <inset key="titleEdgeInsets" minX="2" minY="0.0" maxX="0.0" maxY="0.0"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Mobile" image="phoneIcon">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="mobileButtonAction:" destination="-1" eventType="touchUpInside" id="IOo-bY-yQI"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="trailing" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TOz-0J-Nx0">
                            <rect key="frame" x="123" y="0.0" width="90" height="17"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="90" id="FlD-UU-566"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <inset key="titleEdgeInsets" minX="2" minY="0.0" maxX="1" maxY="0.0"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="6" maxY="0.0"/>
                            <state key="normal" title="Email" image="emailIcon">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="emailButtonAction:" destination="-1" eventType="touchUpInside" id="x2U-XT-PjP"/>
                            </connections>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vVO-iB-d7L">
                            <rect key="frame" x="11" y="28" width="40" height="2"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="2" id="WtZ-AI-ItF"/>
                                <constraint firstAttribute="width" constant="40" id="xhi-5T-rNG"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="6mD-na-XJo"/>
                        <constraint firstAttribute="trailing" secondItem="TOz-0J-Nx0" secondAttribute="trailing" id="AiH-Zs-rC5"/>
                        <constraint firstItem="vVO-iB-d7L" firstAttribute="leading" secondItem="c7f-5z-1Bf" secondAttribute="leading" constant="11" id="cVp-N4-e4l"/>
                        <constraint firstAttribute="width" constant="213" id="deo-yc-7y6"/>
                        <constraint firstItem="y6Q-Te-zqH" firstAttribute="top" secondItem="c7f-5z-1Bf" secondAttribute="top" id="iQ1-rK-Jk1"/>
                        <constraint firstItem="TOz-0J-Nx0" firstAttribute="top" secondItem="c7f-5z-1Bf" secondAttribute="top" id="rrJ-vr-PJh"/>
                        <constraint firstAttribute="bottom" secondItem="vVO-iB-d7L" secondAttribute="bottom" id="uA2-aN-b39"/>
                        <constraint firstItem="y6Q-Te-zqH" firstAttribute="leading" secondItem="c7f-5z-1Bf" secondAttribute="leading" id="zPF-cF-w9S"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bFK-SZ-QvS">
                    <rect key="frame" x="30" y="229" width="333" height="48"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="W0m-VJ-BKg">
                            <rect key="frame" x="20" y="0.0" width="293" height="48"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ID2-cf-C52">
                                    <rect key="frame" x="0.0" y="0.0" width="30" height="48"/>
                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" title="+1">
                                        <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </state>
                                    <connections>
                                        <action selector="countryCodeAction:" destination="-1" eventType="touchUpInside" id="bdu-c5-7QM"/>
                                    </connections>
                                </button>
                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mobile number" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Da1-Vi-76H">
                                    <rect key="frame" x="36" y="0.0" width="257" height="48"/>
                                    <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                    <textInputTraits key="textInputTraits" returnKeyType="done"/>
                                </textField>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="W0m-VJ-BKg" firstAttribute="top" secondItem="bFK-SZ-QvS" secondAttribute="top" id="9kM-Td-Teq"/>
                        <constraint firstAttribute="height" constant="48" id="Isy-Ae-lqr"/>
                        <constraint firstAttribute="trailing" secondItem="W0m-VJ-BKg" secondAttribute="trailing" constant="20" id="dgP-1I-5Fu"/>
                        <constraint firstAttribute="bottom" secondItem="W0m-VJ-BKg" secondAttribute="bottom" id="mAA-6e-3hU"/>
                        <constraint firstItem="W0m-VJ-BKg" firstAttribute="leading" secondItem="bFK-SZ-QvS" secondAttribute="leading" constant="20" id="sA7-lu-PAu"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ckL-Me-tHf">
                    <rect key="frame" x="30" y="297" width="333" height="48"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="o0b-ql-iHD">
                            <rect key="frame" x="297" y="16" width="16" height="16"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="16" id="4Sk-WS-Qwj"/>
                                <constraint firstAttribute="width" constant="16" id="6oy-2S-wTL"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="eyeClose"/>
                            <state key="selected" image="eyeOpen"/>
                            <connections>
                                <action selector="pwdVisibleButtonAction:" destination="-1" eventType="touchUpInside" id="nJk-xP-xSO"/>
                            </connections>
                        </button>
                        <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Password" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Utw-xM-Bgh">
                            <rect key="frame" x="20" y="0.0" width="257" height="48"/>
                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <textInputTraits key="textInputTraits" returnKeyType="done" secureTextEntry="YES"/>
                        </textField>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="48" id="30p-pM-HqN"/>
                        <constraint firstAttribute="height" constant="48" id="4hZ-6W-l5j"/>
                        <constraint firstItem="o0b-ql-iHD" firstAttribute="leading" secondItem="Utw-xM-Bgh" secondAttribute="trailing" constant="20" id="5d2-Ib-vqj"/>
                        <constraint firstAttribute="trailing" secondItem="o0b-ql-iHD" secondAttribute="trailing" constant="20" id="Klw-SO-VcM"/>
                        <constraint firstItem="Utw-xM-Bgh" firstAttribute="leading" secondItem="ckL-Me-tHf" secondAttribute="leading" constant="20" id="Pvf-4c-hbg"/>
                        <constraint firstItem="o0b-ql-iHD" firstAttribute="centerY" secondItem="ckL-Me-tHf" secondAttribute="centerY" id="ZIo-B6-1m0"/>
                        <constraint firstItem="Utw-xM-Bgh" firstAttribute="top" secondItem="ckL-Me-tHf" secondAttribute="top" id="av5-Rq-S2k"/>
                        <constraint firstItem="Utw-xM-Bgh" firstAttribute="centerY" secondItem="ckL-Me-tHf" secondAttribute="centerY" id="guh-aJ-q3l"/>
                        <constraint firstAttribute="bottom" secondItem="Utw-xM-Bgh" secondAttribute="bottom" id="ugl-P2-el6"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sW8-kd-cGa">
                    <rect key="frame" x="30" y="353" width="298.66666666666669" height="24"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yhH-1i-4rI">
                            <rect key="frame" x="0.0" y="0.0" width="24" height="24"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="24" id="A4Q-ac-5kC"/>
                                <constraint firstAttribute="width" constant="24" id="XlR-IU-XBo"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title=" " image="uncheckMark"/>
                            <state key="selected" image="checkMark"/>
                            <connections>
                                <action selector="tAndcCheckBoxAction:" destination="-1" eventType="touchUpInside" id="THk-az-xRT"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="I agree with " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7u0-fo-YMV">
                            <rect key="frame" x="29" y="5" width="67.666666666666671" height="14"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vw7-B8-b8P">
                            <rect key="frame" x="96.666666666666686" y="0.0" width="75" height="24"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                            <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Privacy Policy">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="privatePolicyAction:" destination="-1" eventType="touchUpInside" id="uxy-Ox-4QV"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="&amp;" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="knW-hp-iQV">
                            <rect key="frame" x="173.**************" y="0.0" width="8" height="24"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="su6-FD-bxA">
                            <rect key="frame" x="183.**************" y="0.0" width="114.99999999999997" height="24"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Terms of Condictions">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="termsAction:" destination="-1" eventType="touchUpInside" id="eSn-LL-Fvg"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="7u0-fo-YMV" firstAttribute="centerY" secondItem="yhH-1i-4rI" secondAttribute="centerY" id="1fa-H6-HyP"/>
                        <constraint firstItem="knW-hp-iQV" firstAttribute="leading" secondItem="vw7-B8-b8P" secondAttribute="trailing" constant="2" id="3lq-gr-Kuf"/>
                        <constraint firstAttribute="bottom" secondItem="knW-hp-iQV" secondAttribute="bottom" id="5wR-tU-Vio"/>
                        <constraint firstItem="su6-FD-bxA" firstAttribute="top" secondItem="sW8-kd-cGa" secondAttribute="top" id="7na-Oc-a7X"/>
                        <constraint firstAttribute="bottom" secondItem="su6-FD-bxA" secondAttribute="bottom" id="Lx4-tz-0Mj"/>
                        <constraint firstItem="vw7-B8-b8P" firstAttribute="leading" secondItem="7u0-fo-YMV" secondAttribute="trailing" id="O6M-cm-6NH"/>
                        <constraint firstItem="7u0-fo-YMV" firstAttribute="leading" secondItem="yhH-1i-4rI" secondAttribute="trailing" constant="5" id="V9S-gv-pBU"/>
                        <constraint firstAttribute="height" constant="24" id="WtE-yH-y2B"/>
                        <constraint firstItem="yhH-1i-4rI" firstAttribute="centerY" secondItem="sW8-kd-cGa" secondAttribute="centerY" id="nvV-KY-Kow"/>
                        <constraint firstAttribute="bottom" secondItem="vw7-B8-b8P" secondAttribute="bottom" id="o3P-vn-J6V"/>
                        <constraint firstItem="knW-hp-iQV" firstAttribute="top" secondItem="sW8-kd-cGa" secondAttribute="top" id="obS-Sl-9jh"/>
                        <constraint firstAttribute="trailing" secondItem="su6-FD-bxA" secondAttribute="trailing" id="smH-U3-sge"/>
                        <constraint firstItem="yhH-1i-4rI" firstAttribute="leading" secondItem="sW8-kd-cGa" secondAttribute="leading" id="wNO-Gq-mAM"/>
                        <constraint firstItem="vw7-B8-b8P" firstAttribute="top" secondItem="sW8-kd-cGa" secondAttribute="top" id="z9s-eL-H7E"/>
                        <constraint firstItem="su6-FD-bxA" firstAttribute="leading" secondItem="knW-hp-iQV" secondAttribute="trailing" constant="2" id="zC5-M0-bqH"/>
                    </constraints>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ReR-lE-KsZ">
                    <rect key="frame" x="30" y="397" width="333" height="48"/>
                    <color key="backgroundColor" red="0.24308595059999999" green="0.123568885" blue="0.37619215249999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="48" id="rs0-2i-M6I"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" title="Login">
                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="loginButtonAction:" destination="-1" eventType="touchUpInside" id="DXn-pz-uhk"/>
                    </connections>
                </button>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" translatesAutoresizingMaskIntoConstraints="NO" id="cqJ-Ap-BlY">
                    <rect key="frame" x="30" y="455" width="333" height="20"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AwK-YQ-wyj">
                            <rect key="frame" x="0.0" y="0.0" width="203" height="20"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Hr4-CL-pRU">
                                    <rect key="frame" x="2" y="3" width="14" height="14"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="14" id="kaB-Ht-Di0"/>
                                        <constraint firstAttribute="height" constant="14" id="tHi-1w-334"/>
                                    </constraints>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" title="Button" image="uncheckMark"/>
                                    <state key="selected" image="checkMark"/>
                                    <connections>
                                        <action selector="rememberMeCheckBtnAction:" destination="-1" eventType="touchUpInside" id="cQk-Xp-J7x"/>
                                    </connections>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Remember Me" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bJR-vW-aQ5">
                                    <rect key="frame" x="26.000000000000007" y="3" width="79.666666666666686" height="14"/>
                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <color key="highlightedColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="203" id="1Bq-EM-egM"/>
                                <constraint firstItem="Hr4-CL-pRU" firstAttribute="leading" secondItem="AwK-YQ-wyj" secondAttribute="leading" constant="2" id="47u-mJ-vIb"/>
                                <constraint firstItem="bJR-vW-aQ5" firstAttribute="centerY" secondItem="AwK-YQ-wyj" secondAttribute="centerY" id="8qE-sI-3qr"/>
                                <constraint firstItem="Hr4-CL-pRU" firstAttribute="centerY" secondItem="AwK-YQ-wyj" secondAttribute="centerY" id="jqi-vR-IJ4"/>
                                <constraint firstItem="bJR-vW-aQ5" firstAttribute="leading" secondItem="Hr4-CL-pRU" secondAttribute="trailing" constant="10" id="nKN-eU-WzT"/>
                            </constraints>
                        </view>
                        <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Kq8-6n-WFv">
                            <rect key="frame" x="203" y="0.0" width="0.0" height="20"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="13"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Face ID">
                                <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                            </state>
                            <connections>
                                <action selector="faceIDAction:" destination="-1" eventType="touchUpInside" id="S0Q-7N-WL1"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZxU-4o-zvW">
                            <rect key="frame" x="221" y="0.0" width="112" height="20"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="13"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="Forgot Password？">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="forgotPwdAction:" destination="-1" eventType="touchUpInside" id="XRb-nL-3ee"/>
                            </connections>
                        </button>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="ABr-hk-c4Q"/>
                    </constraints>
                </stackView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nAt-Z6-O1M">
                    <rect key="frame" x="75.***************" y="491" width="242.**************" height="17"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Don't have an account?" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KKc-5e-Rpe">
                            <rect key="frame" x="0.0" y="0.0" width="153.**************" height="17"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sign Up Now" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="biL-vZ-sCm">
                            <rect key="frame" x="158.66666666666669" y="0.0" width="83.666666666666686" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <color key="highlightedColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Qzu-0M-wgC">
                            <rect key="frame" x="164.66666666666669" y="-9" width="72" height="35"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="18"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="signUpNowButtonAction:" destination="-1" eventType="touchUpInside" id="zTw-MY-GZ4"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="KKc-5e-Rpe" firstAttribute="top" secondItem="nAt-Z6-O1M" secondAttribute="top" id="9Kv-4S-D0p"/>
                        <constraint firstAttribute="bottom" secondItem="KKc-5e-Rpe" secondAttribute="bottom" id="ACm-PG-yK0"/>
                        <constraint firstItem="Qzu-0M-wgC" firstAttribute="width" secondItem="biL-vZ-sCm" secondAttribute="width" multiplier="0.860558" id="HV3-Ry-bPe"/>
                        <constraint firstItem="biL-vZ-sCm" firstAttribute="leading" secondItem="KKc-5e-Rpe" secondAttribute="trailing" constant="5" id="JbM-va-ser"/>
                        <constraint firstItem="Qzu-0M-wgC" firstAttribute="centerY" secondItem="biL-vZ-sCm" secondAttribute="centerY" id="LUH-1u-4Ry"/>
                        <constraint firstItem="Qzu-0M-wgC" firstAttribute="centerX" secondItem="biL-vZ-sCm" secondAttribute="centerX" id="LiD-tH-QbS"/>
                        <constraint firstAttribute="trailing" secondItem="biL-vZ-sCm" secondAttribute="trailing" id="PCJ-KW-EER"/>
                        <constraint firstItem="KKc-5e-Rpe" firstAttribute="leading" secondItem="nAt-Z6-O1M" secondAttribute="leading" id="SwJ-fN-pDo"/>
                        <constraint firstItem="biL-vZ-sCm" firstAttribute="top" secondItem="nAt-Z6-O1M" secondAttribute="top" id="acy-OF-0Tl"/>
                        <constraint firstAttribute="bottom" secondItem="biL-vZ-sCm" secondAttribute="bottom" id="jhI-Tg-6OZ"/>
                        <constraint firstItem="Qzu-0M-wgC" firstAttribute="height" secondItem="biL-vZ-sCm" secondAttribute="height" multiplier="2.05882" id="rlj-Z2-2au"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iFZ-gs-lj7">
                    <rect key="frame" x="136.**************" y="524" width="119.99999999999997" height="24"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="guestIcon" translatesAutoresizingMaskIntoConstraints="NO" id="sYY-Ge-6wH">
                            <rect key="frame" x="0.0" y="4" width="16" height="16"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="16" id="OGf-wt-Y8u"/>
                                <constraint firstAttribute="width" constant="16" id="sRr-01-Tph"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Login as Guest" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xhr-aG-a2F">
                            <rect key="frame" x="24" y="0.0" width="96" height="24"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kv6-Aj-6Kq">
                            <rect key="frame" x="-2" y="-6" width="122" height="35"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="18"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="loginAsGuestButtonAction:" destination="-1" eventType="touchUpInside" id="fA5-88-qpp"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="sYY-Ge-6wH" firstAttribute="centerY" secondItem="iFZ-gs-lj7" secondAttribute="centerY" id="57H-lu-2AF"/>
                        <constraint firstItem="xhr-aG-a2F" firstAttribute="top" secondItem="iFZ-gs-lj7" secondAttribute="top" id="92d-TQ-ffj"/>
                        <constraint firstItem="sYY-Ge-6wH" firstAttribute="leading" secondItem="iFZ-gs-lj7" secondAttribute="leading" id="ESF-Wl-lXa"/>
                        <constraint firstAttribute="bottom" secondItem="kv6-Aj-6Kq" secondAttribute="bottom" constant="-5" id="Gmr-SE-17Z"/>
                        <constraint firstItem="kv6-Aj-6Kq" firstAttribute="leading" secondItem="iFZ-gs-lj7" secondAttribute="leading" constant="-2" id="b4H-Pp-Tw6"/>
                        <constraint firstAttribute="trailing" secondItem="xhr-aG-a2F" secondAttribute="trailing" id="b8o-QW-TfX"/>
                        <constraint firstAttribute="height" constant="24" id="fH8-EL-u37"/>
                        <constraint firstAttribute="bottom" secondItem="xhr-aG-a2F" secondAttribute="bottom" id="iGw-Qw-7SN"/>
                        <constraint firstItem="kv6-Aj-6Kq" firstAttribute="top" secondItem="iFZ-gs-lj7" secondAttribute="top" constant="-6" id="o7Y-Hj-0dL"/>
                        <constraint firstItem="xhr-aG-a2F" firstAttribute="leading" secondItem="sYY-Ge-6wH" secondAttribute="trailing" constant="8" id="rR7-Nk-ob7"/>
                        <constraint firstAttribute="trailing" secondItem="kv6-Aj-6Kq" secondAttribute="trailing" id="szv-hd-WdQ"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9ym-TL-zUS">
                    <rect key="frame" x="30" y="566" width="333" height="24"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" distribution="equalCentering" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="AUg-kZ-8Cd">
                            <rect key="frame" x="0.0" y="0.0" width="333" height="24"/>
                            <subviews>
                                <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="myR-wS-bPV">
                                    <rect key="frame" x="0.0" y="11.666666666666629" width="102" height="1"/>
                                    <color key="backgroundColor" red="0.58294159170000004" green="0.51056110860000004" blue="0.65468281510000004" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="yqS-eK-sd3"/>
                                    </constraints>
                                </view>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Other Login" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Wwa-7L-nQp">
                                    <rect key="frame" x="129" y="4" width="75" height="16.333333333333332"/>
                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="zCH-T1-6db">
                                    <rect key="frame" x="231.33333333333329" y="11.666666666666629" width="101.**************" height="1"/>
                                    <color key="backgroundColor" red="0.58294159170000004" green="0.51056110860000004" blue="0.65468281510000004" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="X7N-G9-mRZ"/>
                                    </constraints>
                                </view>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="AUg-kZ-8Cd" firstAttribute="leading" secondItem="9ym-TL-zUS" secondAttribute="leading" id="80x-St-JUK"/>
                        <constraint firstAttribute="height" constant="24" id="8lP-PC-qCk"/>
                        <constraint firstItem="AUg-kZ-8Cd" firstAttribute="top" secondItem="9ym-TL-zUS" secondAttribute="top" id="m4W-8f-KIX"/>
                        <constraint firstAttribute="trailing" secondItem="AUg-kZ-8Cd" secondAttribute="trailing" id="mhS-bB-ppt"/>
                        <constraint firstAttribute="bottom" secondItem="AUg-kZ-8Cd" secondAttribute="bottom" id="qJ1-w0-lYH"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0Ic-BI-Pdv">
                    <rect key="frame" x="30" y="608" width="333" height="36"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="Ikt-5M-qtr">
                            <rect key="frame" x="0.0" y="0.0" width="333" height="36"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XRp-Ac-Y6e">
                                    <rect key="frame" x="0.0" y="0.0" width="111" height="36"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yAd-FK-djA">
                                            <rect key="frame" x="37.666666666666671" y="0.0" width="36" height="36"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="36" id="Lg8-8P-HH2"/>
                                                <constraint firstAttribute="height" constant="36" id="pse-oB-JNW"/>
                                            </constraints>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" image="appleIcon"/>
                                            <connections>
                                                <action selector="appleLoginAction:" destination="-1" eventType="touchUpInside" id="oVY-HG-T1e"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="yAd-FK-djA" firstAttribute="centerY" secondItem="XRp-Ac-Y6e" secondAttribute="centerY" id="K05-9i-w5k"/>
                                        <constraint firstAttribute="height" constant="36" id="OkU-8t-55l"/>
                                        <constraint firstItem="yAd-FK-djA" firstAttribute="centerX" secondItem="XRp-Ac-Y6e" secondAttribute="centerX" id="kai-O4-e7x"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bFk-ZR-kxY">
                                    <rect key="frame" x="111" y="0.0" width="111" height="36"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Bf2-6g-fAF">
                                            <rect key="frame" x="37.666666666666657" y="0.0" width="36" height="36"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="36" id="iVj-GQ-46f"/>
                                                <constraint firstAttribute="width" constant="36" id="wln-Cn-myA"/>
                                            </constraints>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" image="googleIcon"/>
                                            <connections>
                                                <action selector="googleLoginAction:" destination="-1" eventType="touchUpInside" id="XLi-bY-A2d"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="Bf2-6g-fAF" firstAttribute="centerY" secondItem="bFk-ZR-kxY" secondAttribute="centerY" id="HtF-EF-H85"/>
                                        <constraint firstAttribute="height" constant="36" id="jRc-AS-WzC"/>
                                        <constraint firstItem="Bf2-6g-fAF" firstAttribute="centerX" secondItem="bFk-ZR-kxY" secondAttribute="centerX" id="w3G-Te-1Uc"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ew4-Z3-5uy" userLabel="facebookView">
                                    <rect key="frame" x="222" y="0.0" width="111" height="36"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ALl-wC-sRf">
                                            <rect key="frame" x="37.666666666666686" y="0.0" width="36" height="36"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="36" id="79V-n3-f9o"/>
                                                <constraint firstAttribute="width" constant="36" id="Dqg-AH-fxR"/>
                                            </constraints>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" image="facebookIcon"/>
                                            <connections>
                                                <action selector="facebookLoginAction:" destination="-1" eventType="touchUpInside" id="Wo5-wS-Law"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="ALl-wC-sRf" firstAttribute="centerX" secondItem="ew4-Z3-5uy" secondAttribute="centerX" id="3cC-6l-IBL"/>
                                        <constraint firstItem="ALl-wC-sRf" firstAttribute="centerY" secondItem="ew4-Z3-5uy" secondAttribute="centerY" id="N3K-nU-f9p"/>
                                        <constraint firstAttribute="height" constant="36" id="ScS-qN-m9k"/>
                                    </constraints>
                                </view>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="Ikt-5M-qtr" firstAttribute="top" secondItem="0Ic-BI-Pdv" secondAttribute="top" id="JTl-AR-RPD"/>
                        <constraint firstAttribute="bottom" secondItem="Ikt-5M-qtr" secondAttribute="bottom" id="iej-MS-eCJ"/>
                        <constraint firstAttribute="height" constant="36" id="lO9-dv-sVI"/>
                        <constraint firstAttribute="trailing" secondItem="Ikt-5M-qtr" secondAttribute="trailing" id="mrE-xJ-xw2"/>
                        <constraint firstItem="Ikt-5M-qtr" firstAttribute="leading" secondItem="0Ic-BI-Pdv" secondAttribute="leading" id="rlB-SF-FYn"/>
                    </constraints>
                </view>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Dxh-Ns-uox">
                    <rect key="frame" x="30" y="71" width="333" height="28"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="errorIcon" translatesAutoresizingMaskIntoConstraints="NO" id="L6n-Oe-07D">
                            <rect key="frame" x="65.666666666666671" y="6" width="16" height="16"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="16" id="DdE-do-Djm"/>
                                <constraint firstAttribute="height" constant="16" id="oJk-yv-S7d"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="&quot;Username or Password Incorrect&quot;" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ay3-uH-EX4">
                            <rect key="frame" x="89.666666666666671" y="0.0" width="183.66666666666663" height="28"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.85980004070000005" green="0.25615042449999997" blue="0.3958691359" alpha="0.40459356244826161" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstItem="ay3-uH-EX4" firstAttribute="centerX" secondItem="Dxh-Ns-uox" secondAttribute="centerX" constant="15" id="Db3-k8-qHO"/>
                        <constraint firstAttribute="bottom" secondItem="ay3-uH-EX4" secondAttribute="bottom" id="Lx3-En-S57"/>
                        <constraint firstItem="ay3-uH-EX4" firstAttribute="top" secondItem="Dxh-Ns-uox" secondAttribute="top" id="PIc-sD-jxV"/>
                        <constraint firstItem="ay3-uH-EX4" firstAttribute="leading" secondItem="L6n-Oe-07D" secondAttribute="trailing" constant="8" id="Vok-2A-jNT"/>
                        <constraint firstItem="L6n-Oe-07D" firstAttribute="centerY" secondItem="Dxh-Ns-uox" secondAttribute="centerY" id="n3P-qy-TkV"/>
                        <constraint firstAttribute="height" constant="28" id="tcC-4E-IwP"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="kGI-8v-vuN" secondAttribute="trailing" constant="66" id="2td-tk-Yx7"/>
                <constraint firstItem="ckL-Me-tHf" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="30" id="5hw-Dg-S8I"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="Dxh-Ns-uox" secondAttribute="trailing" constant="30" id="6aw-Is-WsW"/>
                <constraint firstItem="nAt-Z6-O1M" firstAttribute="top" secondItem="cqJ-Ap-BlY" secondAttribute="bottom" constant="16" id="73x-Lw-Ih3"/>
                <constraint firstItem="ReR-lE-KsZ" firstAttribute="trailing" secondItem="ckL-Me-tHf" secondAttribute="trailing" id="DzR-ot-0m7"/>
                <constraint firstItem="kGI-8v-vuN" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="40" id="EZb-pP-crW"/>
                <constraint firstItem="kGI-8v-vuN" firstAttribute="top" secondItem="Dxh-Ns-uox" secondAttribute="bottom" id="GPV-1A-9Wd"/>
                <constraint firstItem="sW8-kd-cGa" firstAttribute="top" secondItem="ckL-Me-tHf" secondAttribute="bottom" constant="8" id="H64-gf-WOI"/>
                <constraint firstItem="Dxh-Ns-uox" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="30" id="Ifg-s7-5AD"/>
                <constraint firstItem="cqJ-Ap-BlY" firstAttribute="trailing" secondItem="ReR-lE-KsZ" secondAttribute="trailing" id="KTA-Rv-ON2"/>
                <constraint firstItem="ReR-lE-KsZ" firstAttribute="leading" secondItem="ckL-Me-tHf" secondAttribute="leading" id="M9T-Ux-f9P"/>
                <constraint firstItem="9ym-TL-zUS" firstAttribute="trailing" secondItem="ReR-lE-KsZ" secondAttribute="trailing" id="OZR-9a-Vpd"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="bFK-SZ-QvS" secondAttribute="trailing" constant="30" id="OmT-xR-d7f"/>
                <constraint firstItem="kGI-8v-vuN" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="66" id="PJU-Ud-BMJ"/>
                <constraint firstItem="ReR-lE-KsZ" firstAttribute="top" secondItem="sW8-kd-cGa" secondAttribute="bottom" constant="20" id="Q0G-oq-yyZ"/>
                <constraint firstItem="c7f-5z-1Bf" firstAttribute="top" secondItem="kGI-8v-vuN" secondAttribute="bottom" constant="40" id="QsH-P2-hOD"/>
                <constraint firstAttribute="bottom" secondItem="FSc-Rd-RT1" secondAttribute="bottom" id="U6j-aH-IZH"/>
                <constraint firstItem="sW8-kd-cGa" firstAttribute="leading" secondItem="ckL-Me-tHf" secondAttribute="leading" id="W0k-g2-Pdd"/>
                <constraint firstItem="FSc-Rd-RT1" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="W9e-iu-zZr"/>
                <constraint firstItem="nAt-Z6-O1M" firstAttribute="centerX" secondItem="fnl-2z-Ty3" secondAttribute="centerX" id="WyA-VF-rFu"/>
                <constraint firstItem="FSc-Rd-RT1" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="X9g-z8-ibD"/>
                <constraint firstItem="0Ic-BI-Pdv" firstAttribute="top" secondItem="9ym-TL-zUS" secondAttribute="bottom" constant="18" id="Yeb-eT-bZP"/>
                <constraint firstItem="cqJ-Ap-BlY" firstAttribute="leading" secondItem="ReR-lE-KsZ" secondAttribute="leading" id="cus-eP-gHe"/>
                <constraint firstItem="cqJ-Ap-BlY" firstAttribute="top" secondItem="ReR-lE-KsZ" secondAttribute="bottom" constant="10" id="eWj-M2-FQg"/>
                <constraint firstItem="0Ic-BI-Pdv" firstAttribute="leading" secondItem="9ym-TL-zUS" secondAttribute="leading" id="eji-bD-PhJ"/>
                <constraint firstItem="ckL-Me-tHf" firstAttribute="top" secondItem="bFK-SZ-QvS" secondAttribute="bottom" constant="20" id="iV4-XP-fe2"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="ckL-Me-tHf" secondAttribute="trailing" constant="30" id="k9R-nL-gT0"/>
                <constraint firstItem="9ym-TL-zUS" firstAttribute="leading" secondItem="ReR-lE-KsZ" secondAttribute="leading" id="lV7-Bn-LNk"/>
                <constraint firstItem="9ym-TL-zUS" firstAttribute="top" secondItem="iFZ-gs-lj7" secondAttribute="bottom" constant="18" id="nNz-To-cM9"/>
                <constraint firstItem="FSc-Rd-RT1" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="ojP-8Z-4z2"/>
                <constraint firstItem="bFK-SZ-QvS" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="30" id="tBa-Hj-okL"/>
                <constraint firstItem="iFZ-gs-lj7" firstAttribute="centerX" secondItem="fnl-2z-Ty3" secondAttribute="centerX" id="ty4-eW-Plu"/>
                <constraint firstItem="c7f-5z-1Bf" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="wl6-zX-dyE"/>
                <constraint firstItem="0Ic-BI-Pdv" firstAttribute="trailing" secondItem="9ym-TL-zUS" secondAttribute="trailing" id="yIG-Kh-h0E"/>
                <constraint firstItem="iFZ-gs-lj7" firstAttribute="top" secondItem="nAt-Z6-O1M" secondAttribute="bottom" constant="16" id="zLz-Kq-Qap"/>
                <constraint firstItem="bFK-SZ-QvS" firstAttribute="top" secondItem="c7f-5z-1Bf" secondAttribute="bottom" constant="20" id="zvc-U9-HRT"/>
            </constraints>
            <point key="canvasLocation" x="139.69465648854961" y="19.718309859154932"/>
        </view>
    </objects>
    <resources>
        <image name="appleIcon" width="36" height="36"/>
        <image name="checkMark" width="16" height="16"/>
        <image name="emailIcon" width="16" height="16"/>
        <image name="errorIcon" width="16" height="16"/>
        <image name="eyeClose" width="16" height="16"/>
        <image name="eyeOpen" width="16" height="16"/>
        <image name="facebookIcon" width="36" height="36"/>
        <image name="googleIcon" width="36" height="36"/>
        <image name="guestIcon" width="16" height="16"/>
        <image name="loginBg" width="375" height="812"/>
        <image name="loginViewLogo" width="239.33332824707031" height="40"/>
        <image name="phoneIcon" width="16" height="16"/>
        <image name="uncheckMark" width="14" height="14"/>
    </resources>
</document>
