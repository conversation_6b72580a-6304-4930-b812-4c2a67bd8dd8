//
//  LuanchAdView.swift
//  HormoneLife
//
//  Created by bm on 2024/10/16.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit

class LuanchAdView: UIView {
    
    let adImageview = UIImageView()
    
    func refresh(img: String) {
        adImageview.kf.setImage(with: URL(string: img), placeholder: UIImage(named: "start_up_icon"))
    }
    
    init(frame: CGRect, img: String) {
        super.init(frame: CGRect(x: 0, y: 0, width: kScreenWidth, height: kScreenHeight))
        
        adImageview.contentMode = .scaleAspectFill
        adImageview.layer.masksToBounds = true
        if img.count > 0 {
            adImageview.kf.setImage(with: URL(string: img), placeholder: UIImage(named: "start_up_icon"))
        } else {
            adImageview.image = UIImage(named: "start_up_icon")
        }
        
        self.addSubview(adImageview)
        adImageview.snp.makeConstraints { make in
            make.left.top.right.bottom.equalToSuperview()
        }
        
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 3) {
            self.removeFromSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
