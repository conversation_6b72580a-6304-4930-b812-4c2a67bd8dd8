//
//  WebViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/26.
//

import UIKit
import WebKit

class WebViewController: BaseViewController {

    @IBOutlet weak var webView: WKWebView!
    @IBOutlet weak var webViewBottomConstraint: NSLayoutConstraint!
    var urlString: String?
    
    var isTransparent = false
    var isShopPage = false
    
    let headerString = "<header><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0,background-color: transparent;, user-scalable=no'><style>img{max-width:100%}</style></header>";
    var content: String?
    
    init(_ urlString: String? = nil, contentString: String? = nil) {
        self.urlString = urlString
        self.content = contentString
        // 检查是否是商城页面
        if urlString == kHomeLiftShopUrlKey {
            self.isShopPage = true
        }
        super.init(nibName: "WebViewController", bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .themeColor
        self.webView.isOpaque = !self.isTransparent
        self.webView.snp.makeConstraints { make in
            make.left.top.bottom.right.equalToSuperview()
        }
        
        // 设置WKWebView的导航代理
        webView.navigationDelegate = self
        
        // 如果是商城页面，设置自定义返回按钮
        if isShopPage {
            setupCustomBackButton()
        }
        
        guard let urlStr = urlString, let url = URL(string: urlStr) else {
            refresh(content: content)
            return
        }
        webView.load(URLRequest(url: url))
    }

    func refresh(content: String?) {
        if let content = content {
            self.webView.loadHTMLString("\(headerString)\(content)", baseURL: nil)
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = false
    }
    
    // 设置自定义返回按钮
    private func setupCustomBackButton() {
        // 使用系统返回按钮图标，确保在白色背景上可见
        let backButton = UIBarButtonItem(image: UIImage(systemName: "chevron.left")?.withRenderingMode(.alwaysTemplate), style: .plain, target: self, action: #selector(handleBackButtonTapped))
        backButton.tintColor = .mainTextColor // 使用应用的主文本颜色，确保可见性
        navigationItem.leftBarButtonItem = backButton
    }
    
    // 处理返回按钮点击
    @objc private func handleBackButtonTapped() {
        if isShopPage && webView.canGoBack {
            webView.goBack()
        } else {
            // 如果不能网页回退或不是商城页面，则执行正常的返回操作
            navigationController?.popViewController(animated: true)
        }
    }
}

// MARK: - WKNavigationDelegate
extension WebViewController: WKNavigationDelegate {
    // 可以在这里添加网页加载状态的处理
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        // 网页加载完成
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        // 网页加载失败
    }
}
