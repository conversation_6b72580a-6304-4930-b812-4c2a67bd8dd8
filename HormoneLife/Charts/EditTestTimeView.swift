//
//  EditTestTimeView.swift
//  HormoneLife
//
//  Created by Tank on 2024/7/29.
//

import UIKit

class EditTestTimeView: UIView {

    let title: UILabel = {
        let label = UILabel()
        label.font = .regularGilroyFont(16)
        label.textColor = .mainTextColor
        label.text = "Test Time"
        return label
    }()
    
    var monthAndDay: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Sep 03", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(14)
        b.setTitleColor(.mainTextColor, for: .normal)
        return b
    }()
    
    var year: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("2024", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(14)
        b.setTitleColor(.mainTextColor, for: .normal)
        return b
    }()
    
    var time: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("19:30", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(14)
        b.setTitleColor(.mainTextColor, for: .normal)
        return b
    }()
    
    let line1: UILabel = {
        let label = UILabel()
        label.font = .regularGilroyFont(14)
        label.textColor = .mainTextColor
        label.textAlignment = .center
        label.text = "/"
        return label
    }()
    
    let line2: UILabel = {
        let label = UILabel()
        label.font = .regularGilroyFont(14)
        label.textColor = .mainTextColor
        label.textAlignment = .center
        label.text = "/"
        return label
    }()
    
    
    let backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        addSubview(title)
        addSubview(backView)
        [monthAndDay, line1, year, line2, time].forEach(backView.addSubview)
        
        title.snp.makeConstraints {
            $0.centerX.equalToSuperview()
            $0.top.equalToSuperview()
        }
        
        backView.snp.makeConstraints {
            $0.left.right.bottom.equalToSuperview()
            $0.top.equalTo(title.snp.bottom).offset(8)
            $0.height.equalTo(48)
        }
        backView.layer.cornerRadius = 8
        backView.layer.masksToBounds = true
        
        let buttonWidth = (UIScreen.main.bounds.width - 50) / 3
        year.snp.makeConstraints { make in
            make.center.height.equalToSuperview()
            make.width.equalTo(buttonWidth)
        }
        
        line1.snp.makeConstraints { make in
            make.width.equalTo(5)
            make.height.centerY.equalToSuperview()
            make.right.equalTo(year.snp.left)
        }
        
        monthAndDay.snp.makeConstraints { make in
            make.width.equalTo(buttonWidth)
            make.centerY.height.equalToSuperview()
            make.right.equalTo(line1.snp.left)
        }
        
        line2.snp.makeConstraints { make in
            make.width.equalTo(5)
            make.height.centerY.equalToSuperview()
            make.left.equalTo(year.snp.right)
        }
        
        time.snp.makeConstraints { make in
            make.width.equalTo(buttonWidth)
            make.centerY.height.equalToSuperview()
            make.left.equalTo(line2.snp.right)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
