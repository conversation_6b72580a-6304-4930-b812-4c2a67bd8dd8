//
//  LineChartFilledView.swift
//  HormoneLife
//
//  Created by <PERSON> on 2024/6/23.
//

import UIKit
import DGCharts

class LineChartFilledView: UIView {
    
    let backGroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 4.0
        return view
    }()
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "LH ULTRA"
        label.textColor = "#043433".uiColor
        label.font = .mediumGilroyFont(12)
        return label
    }()
        
    private lazy var chartView: LineChartView = {
        let chart = LineChartView()
         chart.backgroundColor = .white
        // chart.gridBackgroundColor = .yellow
        chart.alpha = 0.8
        chart.chartDescription.enabled = false
        chart.dragEnabled = true
        chart.setScaleEnabled(false)
        chart.pinchZoomEnabled = false
        // x-axis limit line
//        let llXAxis = ChartLimitLine(limit: 10, label: "Index 10")
//        llXAxis.lineWidth = 4
//        llXAxis.lineDashLengths = [10, 10, 0]
//        llXAxis.labelPosition = .rightBottom
//        llXAxis.valueFont = .systemFont(ofSize: 10)

        chart.xAxis.gridLineDashLengths = [0, 10]
        chart.xAxis.gridLineDashPhase = 0
        chart.xAxis.labelPosition = .bottom
        chart.xAxis.yOffset = 10
        
        chart.xAxis.granularity = 1
        chart.xAxis.granularityEnabled = true
        chart.xAxis.setLabelCount(10, force: true)

        let leftAxis = chart.leftAxis
        leftAxis.removeAllLimitLines()
        leftAxis.axisMaximum = 55
        leftAxis.axisMinimum = 0
        // leftAxis.gridLineDashLengths = [5, 5]
        leftAxis.drawLimitLinesBehindDataEnabled = true
        leftAxis.drawAxisLineEnabled = false
        leftAxis.granularity = 5
        leftAxis.granularityEnabled = true
        leftAxis.setLabelCount((55/5 + 1), force: true)
        leftAxis.valueFormatter = LeftAxisValueFormatter()
        leftAxis.xOffset = 10

        chart.rightAxis.enabled = false

        //[_chartView.viewPortHandler setMaximumScaleY: 2.f];
        //[_chartView.viewPortHandler setMaximumScaleX: 2.f];
        
        let marker = BalloonMarker(color: UIColor(white: 180/255, alpha: 1),
                                   font: .systemFont(ofSize: 12),
                                   textColor: .black,
                                   insets: UIEdgeInsets(top: 8, left: 8, bottom: 20, right: 8))
        marker.chartView = chart
        marker.color = .themeColor
        marker.minimumSize = CGSize(width: 80, height: 40)
        chart.marker = marker


        chart.legend.form = .line

        chart.animate(xAxisDuration: 2.5)
        
        return chart
    }()
    
    let container = UIView()
    
    init() {
        super.init(frame: .zero)
        setupViews()
        chartView.delegate = self
        self.setDataCount(10, range: UInt32(50))
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(backGroundView)
        backGroundView.addSubview(chartView)
        backGroundView.addSubview(titleLabel)
        
        backGroundView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(16)
            make.bottom.lessThanOrEqualTo(chartView.snp.top)
        }
 
        chartView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(52)
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(288)
        }
    }
}

extension LineChartFilledView: ChartViewDelegate {
    
    @objc func chartValueSelected(_ chartView: ChartViewBase, entry: ChartDataEntry, highlight: Highlight) {
        NSLog("chartValueSelected");
    }
    
    @objc func chartValueNothingSelected(_ chartView: ChartViewBase) {
        NSLog("chartValueNothingSelected");
    }
    
    @objc func chartScaled(_ chartView: ChartViewBase, scaleX: CGFloat, scaleY: CGFloat) {
        
    }
    
    @objc func chartTranslated(_ chartView: ChartViewBase, dX: CGFloat, dY: CGFloat) {
        
    }

    func setDataCount(_ count: Int, range: UInt32) {
        // self.setDataCount(10, range: UInt32(55))
        let values = (0..<count).map { (i) -> ChartDataEntry in
            let val = Double(arc4random_uniform(range))
            let icon = #imageLiteral(resourceName: "hua2")
            return ChartDataEntry(x: Double(i), y: val, icon: icon)
        }
        
        let set1 = LineChartDataSet(entries: values, label: "DataSet 1")
        set1.drawIconsEnabled = false
        set1.isDrawLineWithGradientEnabled = true
        set1.drawCirclesEnabled = false
        set1.drawVerticalHighlightIndicatorEnabled = false
        set1.drawHorizontalHighlightIndicatorEnabled = false
        setup(set1)

        let value = ChartDataEntry(x: Double(3), y: 3)
        set1.addEntryOrdered(value)
        
        let color1 = UIColor(red: 0.956, green: 0.521, blue: 0.832, alpha: 1)
        let color2 = UIColor(red: 0.399, green: 0.49, blue: 0.941, alpha: 0)
        
        // let gradientColors = [ChartColorTemplates.colorFromString("#F485D411").cgColor,
        //                      ChartColorTemplates.colorFromString("#667DF011").cgColor]
        
        let gradientColors = [
            color1.cgColor,
            color2.cgColor
        ]
        let gradient = CGGradient(colorsSpace: nil, colors: gradientColors as CFArray, locations: [0, 1])!

        set1.fillAlpha = 1
        set1.fill = LinearGradientFill(gradient: gradient, angle: -90)
        set1.drawFilledEnabled = true
        set1.drawValuesEnabled = false
        set1.highlightEnabled = true

        let data = LineChartData(dataSet: set1)

        chartView.data = data
    }
    
    private func setup(_ dataSet: LineChartDataSet) {
        if dataSet.isDrawLineWithGradientEnabled {
            dataSet.lineDashLengths = nil
            dataSet.highlightLineDashLengths = nil
            dataSet.setColors("#E5C2DB".uiColor, "#E5C2DB".uiColor, "#E5C2DB".uiColor)
            dataSet.setCircleColor(.black)
            dataSet.gradientPositions = [0, 40, 100]
            dataSet.lineWidth = 1
            dataSet.circleRadius = 3
            dataSet.drawCircleHoleEnabled = false
            dataSet.valueFont = .systemFont(ofSize: 9)
            dataSet.formLineDashLengths = nil
            dataSet.formLineWidth = 1
            dataSet.formSize = 15
        } else {
            dataSet.lineDashLengths = [5, 2.5]
            dataSet.highlightLineDashLengths = [5, 2.5]
            dataSet.setColor(.black)
            dataSet.setCircleColor(.black)
            dataSet.gradientPositions = nil
            dataSet.lineWidth = 1
            dataSet.circleRadius = 3
            dataSet.drawCircleHoleEnabled = false
            dataSet.valueFont = .systemFont(ofSize: 9)
            dataSet.formLineDashLengths = [5, 2.5]
            dataSet.formLineWidth = 1
            dataSet.formSize = 15
        }
    }
    
}

class LeftAxisValueFormatter: AxisValueFormatter {
    func stringForValue(_ value: Double, axis: AxisBase?) -> String {
        "\(Int(value))"
    }
}
