//
//  ChartDetailView.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/31.
//

import UIKit

class ChartDetailView: UIView, UIScrollViewDelegate {

    let colorLabelView = ChartDetailColorLabelView()
    lazy var descriptionView = ChartDetailDescriptionView(title: "", cycleDay: "", date: "", descriptionStr: pageType.detailChartViewBottomDescWithValue(value: "0"))
    
    var title: String = ""
    var cycleDay: String = ""
    var date: String = ""
    
    lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.alwaysBounceVertical = true
        scrollView.tag = 101
        scrollView.delegate = self
        return scrollView
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .fill
        stackView.distribution = .fill
        stackView.spacing = 10
        stackView.backgroundColor = .white
        return stackView
    }()
    
    private lazy var tempBottomBlackView: UIView = {
        let v = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 20))
        v.backgroundColor = .white
        return v
    }()
    
    let container = UIView()
    
    let pageType: LineChartDemoView.ChartType
    var lineView: LineChartWithTitleView?
    var cycleData: CycleFoldLineStatistic?

    init(cycleData: CycleFoldLineStatistic, pageType: LineChartDemoView.ChartType = .lhUltra, delegate: LineChartWithTitleViewDelegate? = nil, lineViewDelegate: LineChartDemoViewDelegate? = nil, temperatureUnit: Int = 1, isPreBtnHiddle: Bool = false, isNextBtnHiddle: Bool = false) {
        self.pageType = pageType
        self.cycleData = cycleData
        super.init(frame: .zero)
        lineView = LineChartWithTitleView(frame: .zero)
        lineView?.lineChartView.isLineChartScrollEnbled = LineChartConfig.isLineChartScrollEnbled
        lineView?.lineChartView.delegate = lineViewDelegate
        lineView?.lineChartView.preChartBtn.isHidden = isPreBtnHiddle
        lineView?.lineChartView.nextChartBtn.isHidden = isNextBtnHiddle
        lineView?.lineChartView.dataProtocol = self //to update detail chart view bottom desc
        lineView?.delegate = delegate //to control F/C switch
        lineView?.segmentControl.selectedSegmentIndex = temperatureUnit == 1 ? 1 : 0

        lineView?.lineChartView.temperatureUnit = temperatureUnit
        lineView?.chartType = pageType
        lineView?.lineChartView.cycleData = cycleData
        
        lineView?.titleLabel.text = ""
        setupUI()
        setupDescriptionView(with: cycleData)
    }
    
    func setupDescriptionView(with cycleData: CycleFoldLineStatistic) {
        if self.pageType == .lh || self.pageType == .lhUltra || self.pageType == .fsh {
            let maxValue = cycleData.resultList.max() ?? 0
            let index = cycleData.resultList.firstIndex(of: maxValue) ?? 0
            
            if cycleData.cdList.count > index {
                self.cycleDay = String(cycleData.cdList[index])
            }
            
            if cycleData.testPageResultList.count > index,
               let dateStr = cycleData.testPageResultList[index].markTime {
                let mon = dateStr.yyyyMMddHHmmss_enMM().prefix(3)
                let day = dateStr.yyyyMMddHHmmss_ddHHmm()
                self.date = mon + " " + day
            }
            
            if cycleData.testPageResultList.count > index {
                self.title = self.pageType.detailChartViewBottomTitle(with: String(maxValue), resultLabel: cycleData.testPageResultList[index].lastResultLabel?.lowercased())
                descriptionView.descLabel.text = cycleData.testPageResultList[index].tips
            }
            
            descriptionView.titleLabel.text = self.title
            descriptionView.cycleDayLabel.text = "Cycle day \(cycleDay)"
            descriptionView.timeLabel.text = date
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(scrollView)
        scrollView.addSubview(container)
        container.addSubview(stackView)
        
        stackView.addArrangedSubview(lineView!)
        stackView.setCustomSpacing(0, after: lineView!)
        stackView.addArrangedSubview(colorLabelView)
        stackView.setCustomSpacing(30, after: colorLabelView)
        if pageType != .temp {
            stackView.addArrangedSubview(descriptionView)
        } else {
            stackView.addArrangedSubview(tempBottomBlackView)
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.height.greaterThanOrEqualTo(scrollView)
        }
        
        stackView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(container)
            make.bottom.lessThanOrEqualTo(-16)
        }
    }
}

extension ChartDetailView: LineChartDemoViewDataProtocol {
    func didSelectValue(value: String, chartType: LineChartDemoView.ChartType, cycleDay: String, date: String, index: Int) {
        //guard chartType != .lh && chartType != .lhUltra else { return }
        
        var resultLabel: String? = nil
        var resultValue: Double? = nil
        var tipsLabel: String? = nil
        
        switch chartType {
        case .pdg, .hcg, .fsh, .lh, .lhUltra:
            self.date = "\(date) 00:00"
            if let list = self.cycleData?.testPageResultList,
               list.count > index {
                resultLabel = list[index].lastResultLabel ?? "null"
                resultValue = list[index].lastResult
                tipsLabel = list[index].tips
                
                if let dateStr = list[index].markTime {
                    let mon = dateStr.yyyyMMddHHmmss_enMM().prefix(3)
                    let day = dateStr.yyyyMMddHHmmss_ddHHmm()
                    self.date = mon + " " + day
                }
            }
            
            self.cycleDay = cycleDay
            self.title = chartType.detailChartViewBottomTitle(with: value, resultLabel: resultLabel, resultValue: resultValue)
            
        case .conception:
            self.cycleDay = cycleDay
            self.date = date
            self.title = chartType.detailChartViewBottomTitle(with: value)
        default:
            return
        }
        
        descriptionView.titleLabel.text = self.title
        descriptionView.cycleDayLabel.text = "Cycle day \(cycleDay)"
        descriptionView.timeLabel.text = self.date
        descriptionView.descLabel.text = pageType.detailChartViewBottomDescWithValue(value: value, tips: tipsLabel)
        
        if chartType != .conception {
            descriptionView.titleLabel.isHidden = tipsLabel == nil
            descriptionView.cycleDayLabel.isHidden = tipsLabel == nil
            descriptionView.timeLabel.isHidden = tipsLabel == nil
        }
//        guard chartType != .conception else { return }
//        let showNandALabel: Bool = value == "0" || value == "-0"
//        descriptionView.titleLabel.isHidden = showNandALabel
//        descriptionView.cycleDayLabel.isHidden = showNandALabel
//        descriptionView.timeLabel.isHidden = showNandALabel
    }
}
