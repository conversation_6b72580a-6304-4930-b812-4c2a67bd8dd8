//
//  ChartsHeaderView.swift
//  HormoneLife
//
//  Created by <PERSON> on 2024/6/23.
//

import UIKit

enum ChartsHeaderViewType: String {
    case chartList
    case chartDetail
    case historyRecord
    
    var rightPadding: Int {
        switch self {
        case .chartDetail: return 52
        case .historyRecord: return 104
        default:
            return 0
        }
    }
}

class ChartsHeaderView: UIView {
    
    var type: ChartsHeaderViewType = .chartList
        
    let backGroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 8.0
        return view
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .fill
        stackView.distribution = .fill
        stackView.spacing = 5
        return stackView
    }()
    
    var textField: UITextField = {
        let textField = UITextField()
        textField.borderStyle = .none
        textField.text = "Select Cycle"
        textField.font = .regularGilroyFont(14)
        textField.textColor = .mainTextColor
        return textField
    }()
    
    var isTextFieldCouldBecomeFirstResponse: Bool = false
    
    lazy var calendarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = #imageLiteral(resourceName: "calendarIcon")
        imageView.contentMode = .scaleAspectFit
        imageView.isUserInteractionEnabled = true
        imageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapCalendarIcon)))
        return imageView
    }()
    
    let downloadButton: UIButton = {
        let button = UIButton(type: .custom)
        let image = #imageLiteral(resourceName: "downloadIcon")
        button.setImage(image, for: .normal)
        button.backgroundColor = .white
        button.layer.cornerRadius = 8
        button.contentMode = .scaleAspectFit
        return button
    }()
    
    let chartButton: UIButton = {
        let button = UIButton(type: .custom)
        let image = #imageLiteral(resourceName: "homeTabBar3Btn")
        button.setImage(image, for: .normal)
        button.backgroundColor = .white
        button.layer.cornerRadius = 8
        button.contentMode = .scaleAspectFit
        return button
    }()

    init(type: ChartsHeaderViewType = .chartList) {
        super.init(frame: .zero)
        self.type = type
        setupViews()
        setupUI()
        textField.delegate = self
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        calendarImageView.image = type == .historyRecord ? #imageLiteral(resourceName: "history") : #imageLiteral(resourceName: "calendarIcon")
        downloadButton.isHidden = type == .chartList
        chartButton.isHidden = type != .historyRecord
    }
    
    @objc func didTapCalendarIcon() {
        textField.becomeFirstResponder()
    }
    
    func setupViews() {
        
        addSubview(backGroundView)
        backGroundView.addSubview(stackView)
        stackView.addArrangedSubview(textField)
        stackView.addArrangedSubview(calendarImageView)
        addSubview(downloadButton)
        addSubview(chartButton)
        
        backGroundView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.right.equalToSuperview().inset(type.rightPadding)
            make.top.bottom.equalToSuperview().inset(5)
        }
        
        stackView.snp.makeConstraints { make in
            make.top.bottom.equalTo(backGroundView)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(40)
        }
        
        downloadButton.snp.makeConstraints { make in
            make.height.width.equalTo(40)
            make.centerY.equalToSuperview()
            make.left.equalTo(backGroundView.snp.right).offset(12)
        }
        
        chartButton.snp.makeConstraints { make in
            make.height.width.equalTo(40)
            make.centerY.equalToSuperview()
            make.left.equalTo(downloadButton.snp.right).offset(12)
        }
    }
}

extension ChartsHeaderView: UITextFieldDelegate {
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        return isTextFieldCouldBecomeFirstResponse
    }
}
