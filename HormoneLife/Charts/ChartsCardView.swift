//
//  ChartsCardView.swift
//  HormoneLife
//
//  Created by <PERSON> on 2024/6/23.
//

import UIKit

class ChartsCardView: UIView {
        
    private var titles: [String] = []
    private var cards: [ChartsCard] = []
    private var colors: [UIColor] = [
        "#EB9F9F".uiColor,
        "#BEAFDD".uiColor,
        "#E2D77B".uiColor,
        "#EB9F9F".uiColor,
        "#BEAFDD".uiColor
    ]

    let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .fill
        stackView.distribution = .equalSpacing
        stackView.spacing = 10
        return stackView
    }()
    
    let container = UIView()
    
    init(titles: [String]) {
        super.init(frame: .zero)
        self.titles = titles
        setupViews()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(scrollView)
        scrollView.addSubview(container)
        container.addSubview(stackView)
        
        for i in titles.enumerated() {
            let card = ChartsCard.init(title: "\(i.element)", color: colors[i.offset])
            cards.append(card)
            stackView.addArrangedSubview(card)
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(self)
            make.height.equalTo(container)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.width.greaterThanOrEqualTo(scrollView)
        }
        
        stackView.snp.makeConstraints { make in
            make.left.equalTo(container).offset(20)
            make.right.equalTo(container).offset(-20)
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
}

class ChartsCard: UIView {
        
    let button: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitleColor("#043433".uiColor, for: .normal)
        button.titleLabel?.font = .regularGilroyFont(12)
        button.backgroundColor = .white
        button.layer.cornerRadius = 4.0
        return button
    }()
    
    let dotView: UIView = {
        let view = UIView()
        view.backgroundColor = "#EB9F9F".uiColor
        view.layer.cornerRadius = 4.5
        return view
    }()
    
    let titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = "#043433".uiColor
        label.font = .boldGilroyFont(12)
        return label
    }()
    
    let valueLabel: UILabel = {
        let label = UILabel()
        label.textColor = "#043433".uiColor
        label.font = .regularGilroyFont(16)
        return label
    }()
    
    init(title: String, color: UIColor) {
        super.init(frame: .zero)
        titleLabel.text = title
        dotView.backgroundColor = color
        let attr = getAttributeString(target: "3", original: "3/13")
        valueLabel.attributedText = attr
        setupViews()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(button)
        addSubview(valueLabel)
        addSubview(titleLabel)
        addSubview(dotView)
        button.snp.makeConstraints { make in
            make.width.height.equalTo(96)
            make.edges.equalToSuperview()
        }
        
        valueLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(titleLabel.snp.top).offset(-16)
            make.left.greaterThanOrEqualTo(0)
            make.right.lessThanOrEqualTo(0)
            make.top.greaterThanOrEqualTo(0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.bottom.equalTo(-16)
            make.centerX.equalToSuperview().offset(8.5)
            make.left.greaterThanOrEqualTo(0)
            make.right.lessThanOrEqualTo(0)
        }
        
        dotView.snp.makeConstraints { make in
            make.size.equalTo(9)
            make.right.equalTo(titleLabel.snp.left).offset(-8)
            make.centerY.equalTo(titleLabel)
        }
    }

}

extension ChartsCard {
    private func getAttributeString(target: String, original: String) -> NSAttributedString {
        let attr = NSMutableAttributedString(string: original)
        let range = (original as NSString).range(of: target)
        attr.addAttributes([
            .font : UIFont.mediumGilroyFont(24)
        ], range: range)
        return attr
    }
}
