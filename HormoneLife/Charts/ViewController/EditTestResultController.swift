//
//  EditTestResultController.swift
//  HormoneLife
//

import UIKit

class EditTestResultController: BaseViewController {
    
    let bgView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 8
        return view
    }()

    let testResultImageView: UIImageView = {
        let imageView = UIImageView(image: #imageLiteral(resourceName: "userTest"))
        imageView.contentMode = .scaleAspectFill
        return imageView
    }()
    
    let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .fill
        stackView.distribution = .fill
        stackView.spacing = 10
        return stackView
    }()
    
    private lazy var infoLabel: UILabel = {
        let label = UILabel();
        label.font = .regularGilroyFont(10)
        label.numberOfLines = 0;
        label.lineBreakMode = .byWordWrapping;
        label.textAlignment = .center;
        let paragraphStyle = NSMutableParagraphStyle();
        paragraphStyle.lineHeightMultiple = 1.43;
        paragraphStyle.alignment = .center
        
        // Line height: 20pt
        label.attributedText = NSMutableAttributedString(
            string: "If you are not satisfied with the automatic detection result, you can drag the annotation above to modify it.",
            attributes: [.paragraphStyle: paragraphStyle])
        ;
        return label
    }()
    
    let container = UIView()
    
    let bottomView = EditTestResultBottomView()
    
    let testTimeView = TestTimeView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .themeColor
        setNavigationBar()
        setupViews()
        bottomView.delegate = self
        testTimeView.delegate = self
    }
    
    private func setNavigationBar() {
        navigationItem.leftBarButtonItem = nil
        navigationItem.title = "Calendar"
    }
    
    func setupViews() {
        
        view.addSubview(scrollView)
        view.addSubview(bottomView)
        
        scrollView.addSubview(container)
        container.addSubview(stackView)
        
        stackView.addArrangedSubview(bgView)
        bgView.addSubview(testResultImageView)
        
        stackView.setCustomSpacing(6, after: bgView)
        stackView.addArrangedSubview(infoLabel)
        stackView.setCustomSpacing(10, after: infoLabel)
        stackView.addArrangedSubview(testTimeView)
        
        scrollView.snp.makeConstraints { make in
            make.left.right.equalTo(view)
            make.top.equalTo(view).offset(12)
            make.width.equalTo(container)
            make.bottom.equalTo(bottomView.snp.top)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.height.greaterThanOrEqualTo(scrollView)
        }
        
        stackView.snp.makeConstraints { make in
            make.left.equalTo(container).offset(20)
            make.right.equalTo(container).offset(-20)
            make.top.equalTo(container)
            // make.bottom.equalTo(container)
            make.bottom.lessThanOrEqualTo(-5)
        }
        
        testResultImageView.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(8)
            make.right.bottom.equalToSuperview().offset(-8)
        }
        
        bottomView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
        }
    }
}

extension EditTestResultController: EditTestResultBottomViewDelegate {
    func didTapDeleteButton() {
        print("🍎🍎🍎🍎\(#function)")
    }
    
    func didTapSaveButton() {
        print("🍎🍎🍎🍎\(#function)")
    }
}

extension EditTestResultController: TestTimeViewDelegate {
    func didSelecteDate() {
        print("🍎🍎🍎🍎\(#function)")
    }
    
    func didSelecteYear() {
        print("🍎🍎🍎🍎\(#function)")
    }
    
    func didSelecteTime() {
        print("🍎🍎🍎🍎\(#function)")
    }
}
