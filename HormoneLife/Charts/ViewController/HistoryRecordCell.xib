<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleAspectFill" selectionStyle="default" indentationWidth="10" rowHeight="78" id="KGk-i7-Jjw" customClass="HistoryRecordCell" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="77"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="77"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="userTest" translatesAutoresizingMaskIntoConstraints="NO" id="She-9E-nNR">
                        <rect key="frame" x="83.333333333333329" y="14" width="226.66666666666669" height="26"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="26" id="Pe5-Ww-czc"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="left" translatesAutoresizingMaskIntoConstraints="NO" id="DC4-2g-9GP">
                        <rect key="frame" x="10" y="12" width="86.666666666666671" height="30"/>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="right" translatesAutoresizingMaskIntoConstraints="NO" id="kbN-sB-5BQ">
                        <rect key="frame" x="223.33333333333334" y="12" width="86.666666666666657" height="30"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="25 (High)" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LFx-hp-52L">
                        <rect key="frame" x="243.99999999999997" y="19.666666666666668" width="53.666666666666657" height="15.000000000000004"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Cycle Day 8" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="h1z-8J-j4x">
                        <rect key="frame" x="15.666666666666664" y="19.666666666666668" width="67.666666666666686" height="15.000000000000004"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="04/10 13:59 " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6qp-at-ufw">
                        <rect key="frame" x="10" y="50" width="61" height="15"/>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ta4-qC-8Rh">
                        <rect key="frame" x="288" y="47" width="26" height="22"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="26" id="vB8-Vf-fnJ"/>
                        </constraints>
                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                        <state key="normal" image="delete"/>
                        <connections>
                            <action selector="didClickOnDeleteButton:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="IdP-Qb-TjB"/>
                        </connections>
                    </button>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sq0-1D-5uN">
                        <rect key="frame" x="250" y="47" width="26" height="22"/>
                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                        <state key="normal" image="edit"/>
                        <connections>
                            <action selector="didClickOnEditButton:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="BK5-x2-3Sg"/>
                        </connections>
                    </button>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iMa-eI-Ohs">
                        <rect key="frame" x="0.0" y="76" width="320" height="1"/>
                        <color key="backgroundColor" red="0.93725490199999995" green="0.94901960780000005" blue="0.94901960780000005" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="ffM-Uq-rgT"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="She-9E-nNR" firstAttribute="leading" secondItem="h1z-8J-j4x" secondAttribute="trailing" id="2PF-jC-YNz"/>
                    <constraint firstAttribute="trailing" secondItem="She-9E-nNR" secondAttribute="trailing" constant="10" id="3BW-l7-387"/>
                    <constraint firstItem="sq0-1D-5uN" firstAttribute="width" secondItem="Ta4-qC-8Rh" secondAttribute="width" id="3aU-Ep-pV2"/>
                    <constraint firstItem="DC4-2g-9GP" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="4dF-ja-QuR"/>
                    <constraint firstAttribute="bottom" secondItem="iMa-eI-Ohs" secondAttribute="bottom" id="6gP-Nn-KDC"/>
                    <constraint firstItem="LFx-hp-52L" firstAttribute="centerX" secondItem="kbN-sB-5BQ" secondAttribute="centerX" constant="4" id="BMC-wM-eNh"/>
                    <constraint firstAttribute="trailing" secondItem="kbN-sB-5BQ" secondAttribute="trailing" constant="10" id="C0T-Q4-GaU"/>
                    <constraint firstAttribute="trailing" secondItem="iMa-eI-Ohs" secondAttribute="trailing" id="GjU-wF-lCM"/>
                    <constraint firstItem="Ta4-qC-8Rh" firstAttribute="leading" secondItem="sq0-1D-5uN" secondAttribute="trailing" constant="12" id="Hms-hK-Psx"/>
                    <constraint firstAttribute="bottom" secondItem="6qp-at-ufw" secondAttribute="bottom" constant="12" id="JCW-bN-Bi8"/>
                    <constraint firstItem="sq0-1D-5uN" firstAttribute="centerY" secondItem="Ta4-qC-8Rh" secondAttribute="centerY" id="KxX-hi-GLc"/>
                    <constraint firstItem="DC4-2g-9GP" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="OJZ-N1-KiZ"/>
                    <constraint firstItem="h1z-8J-j4x" firstAttribute="centerY" secondItem="DC4-2g-9GP" secondAttribute="centerY" id="PpC-49-ODP"/>
                    <constraint firstItem="6qp-at-ufw" firstAttribute="top" secondItem="DC4-2g-9GP" secondAttribute="bottom" constant="8" id="SKJ-A1-6iz"/>
                    <constraint firstItem="She-9E-nNR" firstAttribute="centerY" secondItem="DC4-2g-9GP" secondAttribute="centerY" id="Wyr-Om-iJ1"/>
                    <constraint firstAttribute="trailing" secondItem="Ta4-qC-8Rh" secondAttribute="trailing" constant="6" id="dot-iY-n27"/>
                    <constraint firstItem="iMa-eI-Ohs" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="kUu-HA-NHs"/>
                    <constraint firstAttribute="bottom" secondItem="Ta4-qC-8Rh" secondAttribute="bottom" constant="8" id="n6G-pi-7rD"/>
                    <constraint firstItem="6qp-at-ufw" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="nxX-C7-zdG"/>
                    <constraint firstItem="LFx-hp-52L" firstAttribute="centerY" secondItem="kbN-sB-5BQ" secondAttribute="centerY" id="s09-n5-f9p"/>
                    <constraint firstItem="h1z-8J-j4x" firstAttribute="centerX" secondItem="DC4-2g-9GP" secondAttribute="centerX" constant="-4" id="tOb-nH-0S9"/>
                    <constraint firstItem="kbN-sB-5BQ" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="uUo-YK-Hel"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="dayLabel" destination="h1z-8J-j4x" id="c4U-vz-6cE"/>
                <outlet property="deleteBtn" destination="Ta4-qC-8Rh" id="eb5-Li-FBo"/>
                <outlet property="editBtn" destination="sq0-1D-5uN" id="jFi-so-d52"/>
                <outlet property="leftImageView" destination="DC4-2g-9GP" id="IQH-Fc-BlR"/>
                <outlet property="resultImageView" destination="She-9E-nNR" id="pDB-eR-UTz"/>
                <outlet property="resultValueLabel" destination="LFx-hp-52L" id="t1j-kP-A6k"/>
                <outlet property="rightImageView" destination="kbN-sB-5BQ" id="eSp-ZK-JiG"/>
                <outlet property="timeLabel" destination="6qp-at-ufw" id="hEZ-qK-PVe"/>
            </connections>
            <point key="canvasLocation" x="4.5801526717557248" y="8.8028169014084519"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="delete" width="16" height="16"/>
        <image name="edit" width="16" height="16"/>
        <image name="left" width="86.666664123535156" height="30"/>
        <image name="right" width="86.666664123535156" height="30"/>
        <image name="userTest" width="314" height="24"/>
    </resources>
</document>
