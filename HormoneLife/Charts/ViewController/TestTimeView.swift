//
//  TestTimeView.swift
//  HormoneLife
//

import UIKit

protocol TestTimeViewDelegate: AnyObject {
    func didSelecteDate()
    func didSelecteYear()
    func didSelecteTime()
}

class TestTimeView: UIView {
    
    weak var delegate: TestTimeViewDelegate?
    
    private lazy var testTimeLabel: UILabel = {
        let label = UILabel()
        label.text = "Test Time"
        label.textColor = "#043433".uiColor
        label.font = .regularGilroyFont(16)
        return label
    }()
    
    private lazy var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 4
        return view
    }()

    private lazy var dateButton: UIButton = {
        let button = UIButton()
        button.titleLabel?.font = .regularGilroyFont(14)
        button.setTitleColor("#043433".uiColor, for: .normal)
        button.setTitle("April 01", for: .normal)
        return button
    }()
    
    private lazy var yearButton: UIButton = {
        let button = UIButton()
        button.titleLabel?.font = .regularGilroyFont(14)
        button.setTitleColor("#043433".uiColor, for: .normal)
        button.setTitle("2024", for: .normal)
        return button
    }()
    
    private lazy var timeButton: UIButton = {
        let button = UIButton()
        button.titleLabel?.font = .regularGilroyFont(14)
        button.setTitleColor("#043433".uiColor, for: .normal)
        button.setTitle("17:30", for: .normal)
        return button
    }()
    
    init() {
        super.init(frame: .zero)
        setupViews()
        dateButton.addTarget(self, action: #selector(dateButtonClick), for: .touchUpInside)
        yearButton.addTarget(self, action: #selector(yearButtonClick), for: .touchUpInside)
        timeButton.addTarget(self, action: #selector(timeButtonClick), for: .touchUpInside)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(testTimeLabel)
        addSubview(bgView)
        bgView.addSubview(dateButton)
        bgView.addSubview(yearButton)
        bgView.addSubview(timeButton)
        
        testTimeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(5)
            make.centerX.equalToSuperview()
            make.bottom.equalTo(bgView.snp.top).offset(-10)
        }
        
        bgView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(10)
        }
        
        let cWidth = UIScreen.main.bounds.size.width
        let itemWidth = (cWidth - 80 - 15)/3.0
        
        dateButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview().offset(20)
            make.height.equalTo(48)
            make.width.equalTo(itemWidth)
        }
        
        let view1 = getSlashLable()
        bgView.addSubview(view1)
        view1.snp.makeConstraints { make in
            make.width.equalTo(5)
            make.centerY.equalTo(dateButton)
            make.left.equalTo(dateButton.snp.right)
        }
        
        yearButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(view1.snp.right)
            make.height.equalTo(48)
            make.width.equalTo(itemWidth)
        }
        
        let view2 = getSlashLable()
        bgView.addSubview(view2)
        view2.snp.makeConstraints { make in
            make.width.equalTo(5)
            make.centerY.equalTo(dateButton)
            make.left.equalTo(yearButton.snp.right)
        }
        
        timeButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(view2.snp.right)
            make.height.equalTo(48)
            make.width.equalTo(itemWidth)
        }
    }
    
    @objc func dateButtonClick() {
        delegate?.didSelecteDate()
    }
    
    @objc func yearButtonClick() {
        delegate?.didSelecteYear()
    }
    
    @objc func timeButtonClick() {
        delegate?.didSelecteTime()
    }
    
    func getSlashLable() -> UILabel {
        let label = UILabel()
        label.textColor = "#043433".uiColor
        label.font = .regularGilroyFont(14)
        label.text = "/"
        return label
    }

}
