//
//  HistoryRecordCell.swift
//  HormoneLife
//

import UIKit
import SDWebImage

protocol HistoryRecordCellDelegate: AnyObject {
    func didTapDeleteButton(testResultId: String)
    func didTapEditButton(testResultId: String)
}

class HistoryRecordCell : UITableViewCell {
    
    @IBOutlet weak var dayLabel: UILabel!
    @IBOutlet weak var resultValueLabel: UILabel!
    @IBOutlet weak var resultImageView: UIImageView!
    @IBOutlet weak var timeLabel: UILabel!
    @IBOutlet var leftImageView: UIImageView!
    @IBOutlet var rightImageView: UIImageView!
    
    @IBOutlet weak var deleteBtn: UIButton!
    @IBOutlet weak var editBtn: UIButton!
    var testResultId: String?
    weak var delegate: HistoryRecordCellDelegate?

    override func awakeFromNib() {
        super.awakeFromNib()
        backgroundColor = .themeColor
        contentView.backgroundColor = .white
        // Initialization code
        leftImageView.image = leftImageView.image?.withTintColor("#FF7777".uiColor)
        rightImageView.image = rightImageView.image?.withTintColor("#FF7777".uiColor)
        timeLabel.textColor = .mainTextColor
        
        var imageFraem = self.resultImageView.frame
        imageFraem.origin.x = imageFraem.origin.x + 40
        self.resultImageView.frame = imageFraem
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
    @IBAction func didClickOnDeleteButton(_ sender: Any) {
        guard let resultId = testResultId else { return }
        delegate?.didTapDeleteButton(testResultId: resultId)
    }
    
    @IBAction func didClickOnEditButton(_ sender: Any) {
        guard let resultId = testResultId else { return }
        delegate?.didTapEditButton(testResultId: resultId)
    }
    
    func setImageTintColor(color: UIColor?) {
        guard let tintColor = color else { return }
        leftImageView.image = leftImageView.image?.withTintColor(tintColor)
        rightImageView.image = rightImageView.image?.withTintColor(tintColor)
    }
    
    func configCell(testPaper: TestPaperList, delegate: HistoryRecordCellDelegate? = nil) {
        self.delegate = delegate
        resultImageView.sd_setImage(with: URL(string: testPaper.imageUrl ?? ""), placeholderImage: nil)
        dayLabel.text = "Cycle Day \(testPaper.days ?? 8)"
        
        print(self.timeLabel.frame.size.height)
        var resultValue = 0.0
        var resultLabel: String = testPaper.lastResultLabel ?? ""
        if let lastResult = testPaper.lastResult {
            resultValue = lastResult
            resultLabel = testPaper.lastResultLabel ?? resultLabel
        } else {
            resultValue = testPaper.resultValue ?? 0.0
            resultLabel = testPaper.lastResultLabel ?? resultLabel
        }
        
        if resultLabel == "" {
            resultValueLabel.text = "\(resultValue)(null)"
        } else {
            if testPaper.pageType == "PdG" || testPaper.pageType == "HCG" {
                resultValueLabel.text = "\(resultLabel)"
            } else {
                resultValueLabel.text = "\(resultValue)(\(resultLabel))"
            }
        }
        
        timeLabel.text = timeFormat(date: testPaper.markTime ?? "", format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy HH:mm") 
        testResultId = testPaper.id
    }
}
