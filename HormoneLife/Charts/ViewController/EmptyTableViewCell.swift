//
//  EmptyTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/10/22.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit
import SnapKit

class EmptyTableViewCell: UITableViewCell {

    var img: UIImageView = {
        let i = UIImageView(image: UIImage(named: "emptyImage"))
        i.contentMode = .scaleToFill
        return i
    }()
    
    var title: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(16)
        t.textColor = .mainTextColor
        t.textAlignment = .center
        t.text = "Nothing here"
        return t
    }()
    
    var imgTopConstraint: Constraint?
    var titleBottomConstraint: Constraint?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        
        contentView.backgroundColor = .white
        contentView.addSubview(img)
        contentView.addSubview(title)
        
        img.snp.makeConstraints { make in
            make.width.height.equalTo(250)
            imgTopConstraint = make.top.equalToSuperview().inset(20).constraint
            make.centerX.equalToSuperview()
        }
        img.contentMode = .scaleToFill
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(img.snp.bottom).offset(20)
            titleBottomConstraint = make.bottom.equalToSuperview().inset(20).constraint
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //(250 + 20 + 20 = 290
    func setupCell(height: CGFloat = UIScreen.main.bounds.size.height) {
        let topInset = (height - 290)/2
        imgTopConstraint?.update(inset: topInset)
        titleBottomConstraint?.update(inset: topInset)
    }
    
    func setupCellForSupportAndHelpVC(height: CGFloat = UIScreen.main.bounds.size.height) {
        let topInset = (height - 250)/2
        imgTopConstraint?.update(inset: topInset)
        titleBottomConstraint?.update(inset: topInset + 20)
    }
}
