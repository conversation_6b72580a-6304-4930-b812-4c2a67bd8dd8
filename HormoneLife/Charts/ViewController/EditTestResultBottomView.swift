//
//  EditTestResultBottomView.swift
//  HormoneLife
//

import UIKit

protocol EditTestResultBottomViewDelegate: AnyObject {
    func didTapDeleteButton()
    func didTapSaveButton()
}

class EditTestResultBottomView: UIView {
    
    weak var delegate: EditTestResultBottomViewDelegate?

    private lazy var deleteButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = .themeColor
        button.borderWidth = 0.5
        button.layer.borderColor = "#043433".cgColor
        button.layer.cornerRadius = 4.0
        button.titleLabel?.font = .regularGilroyFont(16)
        button.setTitleColor("#043433".uiColor, for: .normal)
        button.setTitle("Delete", for: .normal)
        return button
    }()
    
    private lazy var saveButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = "#043433".uiColor
        button.layer.cornerRadius = 4.0
        button.setTitle("Save", for: .normal)
        button.titleLabel?.font = .regularGilroyFont(16)
        button.setTitleColor(.white, for: .normal)
        return button
    }()
    
    init() {
        super.init(frame: .zero)
        setupViews()
        deleteButton.addTarget(self, action: #selector(deleteButtonClick), for: .touchUpInside)
        saveButton.addTarget(self, action: #selector(saveButtonClick), for: .touchUpInside)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(deleteButton)
        addSubview(saveButton)
        
        let cWidth = UIScreen.main.bounds.size.width
        let chartsButtonWidth = (cWidth - 60) * 129.0/315.0
        let logButtonWidth = (cWidth - 60) * 186.0/315.0
        
        
        deleteButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(20)
            make.height.equalTo(48)
            make.right.equalTo(saveButton.snp.left).offset(-20)
            make.bottom.equalToSuperview().offset(-28)
            make.width.equalTo(chartsButtonWidth)
        }
        
        saveButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.height.equalTo(48)
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(logButtonWidth)
        }
    }
    
    @objc func saveButtonClick() {
        delegate?.didTapSaveButton()
    }
    
    @objc func deleteButtonClick() {
        delegate?.didTapDeleteButton()
    }

}
