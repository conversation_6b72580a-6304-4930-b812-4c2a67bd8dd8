//
//  HistoryRecordFooterView.swift
//  HormoneLife
//
//  Created by lihegong on 2024/8/2.
//

import UIKit

class HistoryRecordFooterView: UIView {
    
    typealias HLResultFooterBuyBlock = () -> Void
    
    var buyBlock: HLResultFooterBuyBlock?
    
    let imageView: UIImageView = {
        let imageView = UIImageView(image: #imageLiteral(resourceName: "chartDownIcon"))
        imageView.contentMode = .top
        imageView.frame = CGRectMake(0, 0, 18, 18)
        return imageView
    }()
    
    let contentLabel: UILabel = {
        let label = UILabel()
        let paragraphStyle = NSMutableParagraphStyle();
        paragraphStyle.lineHeightMultiple = 2;
        paragraphStyle.alignment = .center
        let attrText = NSMutableAttributedString(
            string: "Suggestion: Begin using the LH Ultra test (Ovulation test) at least 2 days before your fertile window. We suggest testing for 10 days during this cycle. Buy more test kit now.",
            attributes: [
                .paragraphStyle: paragraphStyle,
                .foregroundColor: "#360C5E".uiColor
            ]
        )
        label.font = .regularGilroyFont(13)
        label.numberOfLines = 0
        label.isUserInteractionEnabled = true
        label.attributedText = attrText
        
        
        
        return label
    }()
    
    lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .fill
        stackView.distribution = .fillProportionally
        stackView.spacing = 20
        return stackView
    }()
    
    @objc func buyShop(_ sender: UITapGestureRecognizer) {
        let text = (self.contentLabel.attributedText?.string)!
        let tapLocation = sender.location(in: self.contentLabel) // 获取用户点击的位置
        let textStorage = NSTextStorage(attributedString: self.contentLabel.attributedText!)
        
        let layoutManager = NSLayoutManager()
        let textContainer = NSTextContainer(size: self.contentLabel.bounds.size)
        textContainer.lineFragmentPadding = 0.0
        textContainer.lineBreakMode = .byWordWrapping
        layoutManager.addTextContainer(textContainer)
        textStorage.addLayoutManager(layoutManager)
        
        // 获取点击位置的字符索引
        let characterIndex = layoutManager.characterIndex(for: tapLocation, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)
        
        // 判断用户点击的位置在不在特定文本范围内
        if characterIndex < text.count && characterIndex > (text.count - 25) {
            self.buyBlock?()
        }
    }
    
    init() {
        super.init(frame: .zero)
        setupViews()
        self.contentLabel.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(buyShop(_ :))))
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func refreshTip(type: TestType) {
        var tipText = "Suggestion: Begin using the LH Ultra test (Ovulation test) at least 2 days before your fertile window. We suggest testing for 10 days during this cycle."
        switch type {
        case .PdG:
            tipText = "Suggestion: It is advised to perform PdG tests on days 5, 6, 7, 8, and 9 after obtaining a positive/peak result with an LH Ultra/LH Test."
        case .FSH :
            tipText = "Suggestion: Begin testing on the third day of your menstrual cycle. It is recommended to test on the third（3）, fourth（4）, and fifth（5） days of your menstrual cycle."
            
        case .LH :
            tipText = "Suggestion: Begin using the LH test (Ovulation test) at least 2 days before your fertile window. We suggest testing for 10 days during this cycle."
            
        case .HCG :
            tipText = "                                                                                                                                                             "
        default:
            tipText = "Suggestion: Begin using the LH Ultra test (Ovulation test) at least 2 days before your fertile window. We suggest testing for 10 days during this cycle."
        }
        
        let paragraphStyle = NSMutableParagraphStyle();
        paragraphStyle.lineHeightMultiple = 1.14;
        paragraphStyle.alignment = .center
        let attrText = NSMutableAttributedString(
            string: tipText,
            attributes: [
                .paragraphStyle: paragraphStyle,
                .foregroundColor: "#360C5E".uiColor
            ]
        )
        let buyAttr = NSAttributedString(string: " Buy more test kit now.", attributes: [NSAttributedString.Key.foregroundColor : UIColor.blue, .paragraphStyle: paragraphStyle])
//        Buy more test kit now.
        attrText.append(buyAttr)
        self.contentLabel.attributedText = attrText
        
        
        
        
    }
    
    func setupViews() {
        addSubview(stackView)
        stackView.addArrangedSubview(imageView)
        stackView.addArrangedSubview(contentLabel)
        stackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
        }
    }
    
}
