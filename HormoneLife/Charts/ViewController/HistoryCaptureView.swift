//
//  HistoryCaptureView.swift
//  HormoneLife
//
//  Created by bm on 2024/10/16.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit

class HistoryCaptureView: UIView, HistoryRecordCellDelegate {
    func didTapDeleteButton(testResultId: String) {
        
    }
    
    func didTapEditButton(testResultId: String) {
        
    }
    
    var dataSource: [TestPaperList] = []
    
    var selectedType : TestType = .LhUltra
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(UINib(nibName: "HistoryRecordCell", bundle: nil), forCellReuseIdentifier: "HistoryRecordCell")
        
        tableView.dataSource = self
        tableView.delegate = self
//        tableView.rowHeight = (58 + 20)
        tableView.layer.cornerRadius = 4.0
        if #available(iOS 15.0, *) {
            tableView.sectionHeaderTopPadding = 0;
        } else {
            // Fallback on earlier versions
        }
        return tableView
    }()
    
    init(frame: CGRect, title:String, cycle: String, rowHeight: CGFloat = 0.0, selectedType : TestType = .LhUltra, dataSource: [TestPaperList] = []) {
        super.init(frame: frame)
        self.selectedType = selectedType
        self.dataSource = dataSource
        self.backgroundColor = .themeColor
        
        let imageView = UIImageView()
        imageView.image = UIImage(named: "capture_homelift_icon")
        self.addSubview(imageView)
        imageView.contentMode = .scaleAspectFit
        imageView.snp.makeConstraints { make in
            make.top.equalTo(80)
            make.height.equalTo(40)
            make.centerX.equalToSuperview()
            make.width.equalTo(206)
        }
        let containerView = UIView()
        containerView.layer.masksToBounds = true
        containerView.backgroundColor = .white
        self.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.equalTo(15)
            make.right.equalTo(-15)
            make.top.equalTo(imageView.snp.bottom).offset(20)
        }
        
        let titleLab = UILabel()
        titleLab.textAlignment = .center
        titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E)
        titleLab.font = .systemFont(ofSize: 16)
        titleLab.text = title
        containerView.addSubview(titleLab)
        titleLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(30)
            make.height.equalTo(20)
        }
        
        let cycleLab = UILabel()
        cycleLab.textAlignment = .center
        cycleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 0.6)
        cycleLab.font = .systemFont(ofSize: 14)
        cycleLab.text = cycle
        containerView.addSubview(cycleLab)
        cycleLab.snp.makeConstraints { make in
            make.top.equalTo(titleLab.snp.bottom).offset(15)
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
        }
        
        containerView.addSubview(self.tableView)
        self.tableView.backgroundColor = .white
        self.tableView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(cycleLab.snp.bottom).offset(15)
            make.height.equalTo(rowHeight)
            make.bottom.equalTo(-30)
        }
        
        let qrcode = LBXScanNative.createQR(with: UserDefaults.standard.downloadUrl, qrSize: CGSize(width: 100, height: 100))
        
        let qrImageview = UIImageView()
        qrImageview.image = qrcode
        qrImageview.contentMode = .scaleAspectFit
        addSubview(qrImageview)
        qrImageview.snp.makeConstraints { make in
            make.top.equalTo(containerView.snp.bottom).offset(20)
            make.height.width.equalTo(80)
            make.centerX.equalToSuperview()
        }
        
        let tipLab = UILabel()
        tipLab.textAlignment = .center
        tipLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 0.6)
        tipLab.font = .systemFont(ofSize: 14)
        tipLab.text = "Download, know more about yourself"
        self.addSubview(tipLab)
        tipLab.snp.makeConstraints { make in
            make.top.equalTo(qrImageview.snp.bottom).offset(15)
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
            make.bottom.equalTo(-40)
        }
        self.tableView.reloadData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}


extension HistoryCaptureView: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {

        return dataSource.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: "HistoryRecordCell") as? HistoryRecordCell,
              dataSource.count > indexPath.row else {
            return UITableViewCell()
        }
        
        cell.editBtn.isHidden = true
        cell.deleteBtn.isHidden = true
        
        let testPaper = dataSource[indexPath.row]
        cell.setImageTintColor(color: selectedType.titleColor)
        cell.configCell(testPaper: testPaper, delegate: self)
        return cell
    }
    
}
