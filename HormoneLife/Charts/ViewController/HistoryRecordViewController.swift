//
//  HistoryRecordViewController.swift
//  HormoneLife
//

import UIKit
import Photos

class HistoryRecordViewController: BaseViewController, CyclePickerViewDelegate, CreateAccountPopupViewControllerDelegate {
    func didTapCreateAccount() {
        let signUpVC = SignUpViewController()
        navigationController?.pushViewController(signUpVC, animated: true)
    }
    
    func didTapClosePopup() {
        
    }
    
    private lazy var titlesView: ChartsTitleView = {
        var types: [TestType] = [.LhUltra, .HCG, .PdG, .FSH]
        if !isHideLH {
            types.insert(.LH, at: 1)
        }
        let view = ChartsTitleView(types: types)
        return view
    }()
    
    let searchView = ChartsHeaderView(type: .historyRecord)
    
    let footerView = HistoryRecordFooterView()
    
    var isCapture = false
    
    var cycleList = [CycleSimple]()
    var hasGetCycleSimpleList = false
    var currentCycleIndex = 0
    private var selectedType: TestType = .LhUltra
    
    lazy var cyclePickerView: CyclePickerView = {
        let pickerView = CyclePickerView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 500))
        pickerView.delegate = self
        pickerView.dataSource = cycleListOptions
        pickerView.tag = 100
        return pickerView
    }()
    
    var cycleListOptions: [String] = [] {
        didSet {
            self.searchView.textField.inputView = cycleListOptions.isEmpty ? nil : cyclePickerView
            self.searchView.isTextFieldCouldBecomeFirstResponse = !cycleListOptions.isEmpty
        }
    }
    var fromDate: String? = nil
    var toDate: String? = nil
    
    let noContentImageView: UIImageView = {
        let imageView = UIImageView(image: #imageLiteral(resourceName: "NoContent"))
        imageView.backgroundColor = .clear
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 4
        imageView.isHidden = true
        return imageView
    }()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(UINib(nibName: "HistoryRecordCell", bundle: nil), forCellReuseIdentifier: "HistoryRecordCell")
        tableView.register(EmptyTableViewCell.self, forCellReuseIdentifier: "EmptyTableViewCell")
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        tableView.layer.cornerRadius = 4.0
        if #available(iOS 15.0, *) {
            tableView.sectionHeaderTopPadding = 0;
        } else {
            // Fallback on earlier versions
        }
        return tableView
    }()
    
    lazy var cameraButton: UIImageView = {
        let b = UIImageView(image: UIImage(named: "cameraButton"))
        b.isUserInteractionEnabled = true
        b.contentMode = .scaleAspectFill
        b.showShadow(shadowColor: .black, shadowOpacity: 0.3, shadowRadius: 10, shadowOffset: CGSize(width: 0, height: 10))
        b.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapCamera)))
        return b
    }()
    
    var dataSource: [TestPaperList] = []
    let viewModel = UserTestViewModel()

    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .themeColor
        setNavigationBar()
        setupViews()
        // bottomView.delegate = self
        titlesView.delegate = self
        searchView.downloadButton.addTarget(self, action: #selector(didDownloadChart), for: .touchUpInside)
        
        searchView.chartButton.addTarget(self, action: #selector(didViewChart), for: .touchUpInside)
        NotificationCenter.default.addObserver(self, selector: #selector(didReceiveNoti), name: NSNotification.Name("refreshHistoryRecordData"), object: nil)
        
        let rightImageView = UIImageView(frame: CGRect(x: 0, y: 0, width: 25, height: 25))
        rightImageView.image = UIImage(named: "quoteMark")
        rightImageView.contentMode = .scaleAspectFill
        rightImageView.isUserInteractionEnabled = true
        rightImageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(supportHelp)))
        let barItem = UIBarButtonItem(customView: rightImageView)
        navigationItem.rightBarButtonItem  = barItem
        
        footerView.buyBlock = { [weak self] in
            let webview = WebViewController(kHomeLiftShopUrlKey)
            webview.title = "Shop"
            self?.navigationController?.pushViewController(webview, animated: true)
        }
        self.getCycleSimpleList()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("refreshHistoryRecordData"), object: nil)
    }
    
    @objc func didReceiveNoti(sender: Notification) {
        if let paperType = sender.userInfo?["paperType"] as? String {
            if paperType == "PdG" {
                self.buttonDidClickWithType(type: .PdG)
            } else if paperType == "LH Ultra" {
                self.buttonDidClickWithType(type: .LhUltra)
            } else {
                loadData()
            }
        } else {
            loadData()
        }
    }
    
    @objc func didViewChart() {
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        let chartDetailVC = ChartDetailViewController()
        chartDetailVC.chartType = LineChartDemoView.ChartType(rawValue: selectedType.rawValue) ?? .lhUltra // TODO: 
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(chartDetailVC, animated: true)
        hidesBottomBarWhenPushed = false
    }
    
    @objc func didDownloadChart() {
        
        showActivityHUD()
        
        let tbHeight = (CGFloat(self.dataSource.count) * (58.0 + 15) + 30)
        
        let cycle = self.cycleList[self.currentCycleIndex]
        let startDay = timeFormat(date: cycle.startCycleTime ?? dateFormat(date: Date(), format: "yyyy-MM-dd HH:mm:ss"), format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy")
        let toDays = timeFormat(date: cycle.endCycleTime ?? dateFormat(date: Date(), format: "yyyy-MM-dd HH:mm:ss"), format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy")
        let captureView = HistoryCaptureView(frame: CGRect(x: -kScreenWidth, y: 0, width: kScreenWidth, height: 420 + tbHeight), title: "\(self.selectedType.description) Test", cycle: "Cycle: \(startDay)-\(toDays)", rowHeight: tbHeight, selectedType: selectedType,dataSource: self.dataSource)
        self.view.addSubview(captureView)
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 1) {
            if let screenshotd = takeScreenshotOfView(captureView) {
                self.saveImageToPhotosAlbum(screenshotd)
            }
            hideActivityHUD()
        }
        

    }
    
    func saveImageToPhotosAlbum(_ image: UIImage) {
        PHPhotoLibrary.shared().performChanges({
            let assetChangeRequest = PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { completed, error in
            if completed {
                showToachMessage(message: "Save success")
            } else {
                
            }
        }
    }
    
    
    @objc func didTapCamera() {
        guard !isRunningOnSimulator() else { return }
        let captureVC = CustomCameraViewController()
        
        captureVC.pageType = selectedType.homeTitle
        
        captureVC.paperType = selectedType
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(captureVC, animated: true)
        hidesBottomBarWhenPushed = false
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        setTableFooterView()
    }
    
    @objc func supportHelp() {
        let vc = SupportAndHelpHomeViewController()
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc func loadData() {
//        guard self.hasGetCycleSimpleList == false else {
//            ret
//        }
        
        viewModel.getTestPageList(pageType: selectedType.description, fromDate: self.fromDate, toDate: self.toDate) { papers in
            self.dataSource = papers

            self.tableView.scrollsToTop = true
            self.tableView.reloadData()
        }
    }
    
    func getCycleSimpleList() {
        
        viewModel.getCycleSimpleList(pageType: selectedType.description) { cycleSimpleList in
            self.hasGetCycleSimpleList = true
            guard !cycleSimpleList.isEmpty else { return }
            self.cycleListOptions.removeAll()
            self.cycleList = cycleSimpleList
            for (index, cycle) in cycleSimpleList.enumerated() {
                
                if let fromDate = cycle.startCycleTime?.yyyyMMddHHmmss_yyyyMMdd()?.replacingOccurrences(of: "-", with: "/") {
                    if index == 0 {
                        let cycleRound = "Current cycle        \(cycle.num) record\(cycle.num > 1 ? "s" : "")"
                        
                        self.cycleListOptions.append(cycleRound)
                        
                        self.searchView.textField.text = "Current cycle"
                        continue
                    }
                    
                    if let toDate = cycle.endCycleTime?.yyyyMMddHHmmss_yyyyMMdd()?.replacingOccurrences(of: "-", with: "/") {
                        let cycleRound = "\(fromDate)   -   \(toDate)        \(cycle.num) record\(cycle.num > 1 ? "s" : "")"
                        
                        self.cycleListOptions.append(cycleRound)
                    }
                }
                self.cyclePickerView.dataSource = self.cycleListOptions
                self.cyclePickerView.refresh()
            }

            let model = self.cycleList.first
            self.fromDate = model?.startCycleTime ?? ""
            self.toDate = hl_dateFormat(date: Date(), format: "yyyy-MM-dd HH:mm:ss")
    
            self.loadData()
        }
    }

    func setTableFooterView() {
        if self.selectedType != .HCG {
            footerView.frame.size.width = UIScreen.main.bounds.width - 40
            footerView.layoutIfNeeded()
            let size = footerView.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize)
            footerView.frame.size.height = size.height
            tableView.tableFooterView = footerView
            footerView.refreshTip(type: self.selectedType)
            
        } else {
            tableView.tableFooterView = nil
        }
    }
    
    private func setNavigationBar() {
        navigationItem.leftBarButtonItem = nil
        navigationItem.title = "History Record"
    }
    
    func setupViews() {
        
        view.addSubview(titlesView)
        view.addSubview(searchView)
        view.addSubview(tableView)
        view.addSubview(noContentImageView)
        view.addSubview(cameraButton)
        titlesView.snp.makeConstraints { make in
            make.top.left.trailingMargin.equalTo(5)
        }
        
        searchView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(titlesView.snp.bottom)
        }
        
        tableView.snp.makeConstraints { make in
            make.left.equalTo(view).offset(20)
            make.right.equalTo(view).offset(-20)
            make.top.equalTo(searchView.snp.bottom).offset(12)
            make.bottom.equalTo(view.snp.bottom)
        }
        
        noContentImageView.snp.makeConstraints { make in
            make.top.bottom.equalTo(tableView)
            make.left.equalTo(tableView)
            make.right.equalTo(tableView)
        }
        
        cameraButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(52)
            make.width.height.equalTo(64)
            make.right.equalToSuperview()
        }
    }
    
    func didTapSave(_ value: String) {
        didTapCancel()
        let durationDate = value.components(separatedBy: "        ")
        searchView.textField.text = durationDate.first
        
        guard let dates = durationDate.first?.components(separatedBy: "   -   "),
              let fromDate = dates.first,
              let toDate = dates.last?.prefix(10) else { return }
        self.fromDate = "\(fromDate.replacingOccurrences(of: "/", with: "-")) 00:00:00"
        self.toDate = "\(toDate.replacingOccurrences(of: "/", with: "-")) 00:00:00"
        
        if value.hasPrefix("Current cycle") == true {
            let model = self.cycleList.first
            self.fromDate = model?.startCycleTime ?? ""
            self.toDate = hl_dateFormat(date: Date(), format: "yyyy-MM-dd HH:mm:ss")
        }
        
        loadData()
    }
    
    func didTapSaveIndex(_ index: Int) {
        self.currentCycleIndex = index
    }
    
    func didTapCancel() {
        searchView.textField.resignFirstResponder()
    }
}

extension HistoryRecordViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        //noContentImageView.isHidden = dataSource.count > 0
        return dataSource.count > 0 ? dataSource.count : 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if dataSource.count > 0 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "HistoryRecordCell") as? HistoryRecordCell,
                  dataSource.count > indexPath.row else {
                return UITableViewCell()
            }
            
            cell.editBtn.isHidden = self.isCapture
            cell.deleteBtn.isHidden = self.isCapture
            
            let testPaper = dataSource[indexPath.row]
            cell.setImageTintColor(color: selectedType.titleColor)
            cell.configCell(testPaper: testPaper, delegate: self)
            return cell
        } else {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "EmptyTableViewCell") as? EmptyTableViewCell else {
                return UITableViewCell()
            }
            cell.setupCell(height: tableView.height())
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard dataSource.count > indexPath.row else { return }
        tableView.deselectRow(at: indexPath, animated: false)
        let testPaper = dataSource[indexPath.row]
        guard let resultId = testPaper.id else { return }
        didTapEditButton(testResultId: resultId)
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        CGFloat.leastNormalMagnitude
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        CGFloat.leastNormalMagnitude
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        nil
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        nil
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
       
        //print("scrollView.contentSize = \(self.tableView.contentSize.height), offset = \(self.tableView.contentOffset.y)")
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {

        if self.tableView.contentOffset.y > 0 {
            
            guard self.currentCycleIndex < (self.cycleListOptions.count - 1) else {
                return
            }
            
            //down
            if self.tableView.contentSize.height > self.tableView.frame.size.height {
                if 50 < ((self.tableView.contentOffset.y + self.tableView.frame.size.height) - self.tableView.contentSize.height) {
                    self.currentCycleIndex += 1
                    self.loadData()
                }
            } else {
                if self.tableView.contentOffset.y > 50 {
                    self.currentCycleIndex += 1
                    self.didTapSave(self.cycleListOptions[self.currentCycleIndex])
//                    self.loadData()
                }
            }
        } else {
            if self.tableView.contentOffset.y < -50 {
                //up
                guard self.currentCycleIndex > 0 else {
                    return
                }
                self.currentCycleIndex -= 1
                self.didTapSave(self.cycleListOptions[self.currentCycleIndex])
//                self.loadData()
            }
        }
    }
}

extension HistoryRecordViewController: HistoryRecordCellDelegate {
    func didTapDeleteButton(testResultId: String) {
        let vc = DeleteResultPopupViewController(testResultId: testResultId, title: "Delete test Result", description: "Are you sure want to delete this test result?", delegate: self)
        vc.modalPresentationStyle = .overFullScreen
        navigationController?.present(vc, animated: true)
    }
    
    func didTapEditButton(testResultId: String) {
        showActivityHUD()
        viewModel.getTestDetail(resultId: testResultId) { detail in
            hideActivityHUD()
            guard let testDetail = detail else { return }
            let vc = ResultViewController(testDetail: testDetail, circleViewType: self.selectedType, delegate: self)
            vc.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(vc, animated: true)
        }
    }
}

//extension HistoryRecordViewController: HistoryRecordBottomViewDelegate {
//    func didTapChartsButton() {
//        print("🍎🍎🍎🍎🍎\(#function)")
//    }
//    
//    func didTapLogTestButton() {
//        print("🍎🍎🍎🍎🍎\(#function)")
//    }
//}

extension HistoryRecordViewController: ChartsTitleViewDelegate {
    func buttonDidClickWithType(type: TestType) {
        selectedType = type
        self.fromDate = nil
        self.toDate = nil
        
        self.getCycleSimpleList()
//        loadData()
    }
}

extension HistoryRecordViewController: DeleteResultPopupViewControllerDelegate {
    func confirmDelete() {
        loadData()
    }
}

extension HistoryRecordViewController: ResultViewControllerDelegate {
    func didDeleteResult() {
        showToachMessage(message: "Delete success")
        loadData()
    }
    
    func didUpdateResult() {
        showToachMessage(message: "update success")
        loadData()
    }
}

protocol CyclePickerViewDelegate: AnyObject {
    func didSelectValue(_ value: String)
    func didTapSave(_ value: String)
    func didTapCancel()
    func didTapSaveIndex(_ index: Int)
}

extension CyclePickerViewDelegate {
    func didSelectValue(_ value: String) {}
    func didTapSaveIndex(_ index: Int) {}
    func didTapSave(_ value: String) {}
}

class CyclePickerView: UIView, PickerViewDelegate, PickerViewDataSource {
    
    weak var delegate: CyclePickerViewDelegate?
    var dataSource: [String] = []
    
    let pickeViewTitle: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(16)
        t.textColor = .mainTextColor
        t.textAlignment = .center
        t.text = "Select Cycle"
        return t
    }()
    
    lazy var pickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .default
//        pickerView.selectionStyle = .overlay
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()
    
    let pickerSelectedView = UIView()
    
    lazy var cancelButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .themeColor
        b.layer.cornerRadius = 4
        b.layer.borderColor = UIColor.mainTextColor.cgColor
        b.layer.borderWidth = 1
        b.setTitle("Cancel", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.addTarget(self, action: #selector(didTapCancel), for: .touchUpInside)
        return b
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.setTitle("Save", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.white, for: .normal)
        b.addTarget(self, action: #selector(didTapSave), for: .touchUpInside)
        return b
    }()
    
    var pickerSelectedRow: Int = 0
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .white
        showShadow()
        layer.cornerRadius = 12
        [pickeViewTitle, pickerSelectedView, pickerView, cancelButton, saveButton].forEach(addSubview)
        
        pickerSelectedView.backgroundColor = .lightGrayContentColor
        pickerSelectedView.layer.cornerRadius = 4
        
        pickeViewTitle.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(20)
            make.centerX.equalToSuperview()
        }
        
        pickerView.snp.makeConstraints { make in
            make.height.equalTo(300)
            make.left.right.equalToSuperview().inset(25)
            make.centerX.equalToSuperview()
            make.top.equalTo(pickeViewTitle.snp.bottom).offset(30)
        }
        
        pickerSelectedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(40)
            make.centerY.equalTo(pickerView.snp.centerY)
        }
        
        cancelButton.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.leading.equalToSuperview().inset(20)
            make.trailing.equalTo(snp.centerX).offset(-10)
            make.top.equalTo(pickerView.snp.bottom).offset(20)
        }
        
        saveButton.snp.makeConstraints { make in
            make.height.top.equalTo(cancelButton)
            make.trailing.equalToSuperview().inset(20)
            make.leading.equalTo(snp.centerX).offset(10)
        }
    }
    
    func refresh() {
        self.pickerView.reloadPickerView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func didTapCancel() {
        delegate?.didTapCancel()
    }
    
    @objc func didTapSave() {
        guard pickerSelectedRow >= 0 && pickerSelectedRow < dataSource.count else { return }
        delegate?.didTapSave(dataSource[pickerSelectedRow])
        delegate?.didTapSaveIndex(pickerSelectedRow)
    }
    
    func pickerViewNumberOfRows(_ pickerView: PickerView) -> Int {
        return dataSource.count
    }
    
    func pickerViewHeightForRows(_ pickerView: PickerView) -> CGFloat {
        40
    }
    
    func pickerView(_ pickerView: PickerView, titleForRow row: Int) -> String {
        dataSource[row]
    }
    
    func pickerView(_ pickerView: PickerView, didSelectRow row: Int) {
        guard row >= 0 && row < dataSource.count else { return }
        pickerSelectedRow = row
        delegate?.didSelectValue(dataSource[row])
    }
    
    func pickerView(_ pickerView: PickerView, styleForLabel label: UILabel, highlighted: Bool) {
        label.textAlignment = .center
        
        if highlighted {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor
        } else {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor.withAlphaComponent(0.3)
        }
    }
}
