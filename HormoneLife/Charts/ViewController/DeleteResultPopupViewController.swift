//
//  DeleteResultPopupViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/3.
//

import UIKit
import SDWebImage

protocol DeleteResultPopupViewControllerDelegate: AnyObject {
    func confirmDelete()
}

class DeleteResultPopupViewController: UIViewController {

    let popupBgView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    let titleLabel: UILabel = {
        let l = UILabel()
        l.text = "Result"
        l.textColor = .mainTextColor
        l.font = .mediumGilroyFont(17)
        l.textAlignment = .center
        return l
    }()
    
    let resultLabel: UILabel = {
        let l = UILabel()
        l.text = "Result: "
        l.textColor = .mainTextColor.withAlphaComponent(0.6)
        l.font = .mediumGilroyFont(16)
        l.textAlignment = .center
        l.numberOfLines = 0
        return l
    }()
    
    lazy var backButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Cancel", for: .normal)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.backgroundColor = .white
        b.layer.cornerRadius = 4
        b.layer.borderColor = UIColor.mainTextColor.cgColor
        b.layer.borderWidth = 1
        b.addTarget(self, action: #selector(didTapBack), for: .touchUpInside)
        return b
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Confirm", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.addTarget(self, action: #selector(didTapSave), for: .touchUpInside)
        return b
    }()
    
    let testResultId: String
    weak var delegate: DeleteResultPopupViewControllerDelegate?
    let model = UserTestViewModel()
    
    init(testResultId: String, title: String, description: String, delegate: DeleteResultPopupViewControllerDelegate?) {
        self.testResultId = testResultId
        super.init(nibName: nil, bundle: nil)
        self.delegate = delegate
        titleLabel.text = title
        resultLabel.text = description
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .black.withAlphaComponent(0.3)
        setupUI()
    }
    
    @objc func didTapBack() {
        dismiss(animated: true)
    }
    
    @objc func didTapSave() {
        // call API
        model.deleteTestResult(resultId: testResultId) { result in
            guard let isSuccess = result, isSuccess else { return }
            self.didTapBack()
            self.delegate?.confirmDelete()
        }
    }
    
    private func setupUI() {
        view.addSubview(popupBgView)
        popupBgView.snp.makeConstraints { make in
            make.height.equalTo(250)
            make.left.right.equalToSuperview().inset(40)
            make.center.equalToSuperview()
        }
        
        [titleLabel, resultLabel, backButton, saveButton].forEach(popupBgView.addSubview)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(40)
            make.centerX.equalToSuperview()
        }
        
        resultLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
        }
        
        backButton.snp.makeConstraints { make in
            make.width.equalTo(108)
            make.height.equalTo(48)
            make.top.equalTo(resultLabel.snp.bottom).offset(40)
            make.right.equalTo(view.snp.centerX).offset(-10)
        }
        
        saveButton.snp.makeConstraints { make in
            make.width.equalTo(108)
            make.height.equalTo(48)
            make.top.equalTo(resultLabel.snp.bottom).offset(40)
            make.left.equalTo(view.snp.centerX).offset(10)
        }
    }
}
