//
//  HistoryRecordBottomView.swift
//  HormoneLife
//

import UIKit

protocol HistoryRecordBottomViewDelegate: AnyObject {
    func didTapChartsButton()
    func didTapLogTestButton()
}

class HistoryRecordBottomView: UIView {
    
    weak var delegate: HistoryRecordBottomViewDelegate?

    private lazy var chartsButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = .themeColor
        button.borderWidth = 0.5
        button.layer.borderColor = "#043433".cgColor
        button.layer.cornerRadius = 4.0
        button.titleLabel?.font = .regularGilroyFont(16)
        button.setTitleColor("#043433".uiColor, for: .normal)
        button.setTitle("Charts", for: .normal)
        return button
    }()
    
    private lazy var logButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = "#043433".uiColor
        button.layer.cornerRadius = 4.0
        button.setTitle("Log Test", for: .normal)
        button.titleLabel?.font = .regularGilroyFont(16)
        button.setTitleColor(.white, for: .normal)
        return button
    }()
    
    init() {
        super.init(frame: .zero)
        setupViews()
        logButton.addTarget(self, action: #selector(logButtonClick), for: .touchUpInside)
        chartsButton.addTarget(self, action: #selector(chartsButtonClick), for: .touchUpInside)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(logButton)
        addSubview(chartsButton)
        
        let cWidth = UIScreen.main.bounds.size.width
        let chartsButtonWidth = (cWidth - 60) * 129.0/315.0
        let logButtonWidth = (cWidth - 60) * 186.0/315.0
        
        
        chartsButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(20)
            make.height.equalTo(48)
            make.right.equalTo(logButton.snp.left).offset(-20)
            make.bottom.equalToSuperview().offset(-28)
            make.width.equalTo(chartsButtonWidth)
        }
        
        logButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.height.equalTo(48)
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(logButtonWidth)
        }
    }
    
    @objc func chartsButtonClick() {
        delegate?.didTapChartsButton()
    }
    
    @objc func logButtonClick() {
        delegate?.didTapLogTestButton()
    }

}
