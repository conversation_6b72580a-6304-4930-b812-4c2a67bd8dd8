//
//  LineChartDownloadViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/10/16.
//

import UIKit
import Photos

class LineChartDownloadViewController: BaseViewController {

    lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.alwaysBounceVertical = true
        scrollView.tag = 1011
//        scrollView.delegate = self
        return scrollView
    }()
    
    let container = UIView()
    
    let logoImageView: UIImageView = {
        let imageView = UIImageView(image: #imageLiteral(resourceName: "downloadLogoIcon"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    let chartBackView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    let chartTitleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .mainTextColor
        label.font = .mediumGilroyFont(16)
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    let chartCycleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .mainTextColor
        label.font = .regularGilroyFont(12)
        label.numberOfLines = 0
        label.textAlignment = .center
        label.text = "Cycle: 07/17/2024-08/17/2024"
        return label
    }()
    
    let qrCodeImageView: UIImageView = {
        let imageView = UIImageView(image: #imageLiteral(resourceName: "qrcodeIcon"))
        imageView.contentMode = .scaleAspectFit
        imageView.image = LBXScanNative.createQR(with: UserDefaults.standard.downloadUrl, qrSize: CGSize(width: 100, height: 100))
        return imageView
    }()
    
    let qrCodeDescLabel: UILabel = {
        let label = UILabel()
        label.textColor = .mainTextColor
        label.font = .regularGilroyFont(13)
        label.numberOfLines = 0
        label.textAlignment = .center
        label.text = "Download, know more about yourself"
        return label
    }()
    
    var chartView: ChartDetailView?
    var cycleStatistic: CycleFoldLineStatistic?
    var chartType: LineChartDemoView.ChartType = .lhUltra
    
    init(chartView: ChartDetailView? = nil, cycleStatistic: CycleFoldLineStatistic? = nil, chartType: LineChartDemoView.ChartType, chartTitle: String? = nil, chartCycle: String? = nil) {
        super.init(nibName: nil, bundle: nil)
        self.chartView = chartView
        self.cycleStatistic = cycleStatistic
        self.chartType = chartType
        
        self.chartTitleLabel.text = chartTitle
        self.chartCycleLabel.text = chartCycle
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        navigationItem.title = "Export"
        view.backgroundColor = .themeColor
        navigationItem.rightBarButtonItem = UIBarButtonItem(image: UIImage(named: "downloadIcon"), style: .done, target: self, action: #selector(didTapNavRightButton))
        
        setupUI()
    }
    
    func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(container)
        [logoImageView, chartBackView, qrCodeImageView, qrCodeDescLabel].forEach(container.addSubview)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.height.greaterThanOrEqualTo(scrollView)
        }
        
        chartBackView.addSubview(chartTitleLabel)
        chartBackView.addSubview(chartCycleLabel)
        
        logoImageView.snp.makeConstraints { make in
            make.width.equalTo(206)
            make.height.equalTo(40)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(20)
        }
        
        //677+24+8+19+14 = 677+65
        chartBackView.snp.makeConstraints { make in
            make.height.equalTo(742)
            make.left.right.equalToSuperview().inset(10)
            make.centerX.equalToSuperview()
            make.top.equalTo(logoImageView.snp.bottom).offset(20)
        }
        
        qrCodeImageView.snp.makeConstraints { make in
            make.width.equalTo(60)
            make.height.equalTo(60)
            make.centerX.equalToSuperview()
            make.top.equalTo(chartBackView.snp.bottom).offset(16)
        }
        
        qrCodeDescLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.height.equalTo(16)
            make.left.right.bottom.equalToSuperview().inset(20)
            make.top.equalTo(qrCodeImageView.snp.bottom).offset(14)
        }
        
        chartTitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.top.equalToSuperview().inset(24)
        }
        
        chartCycleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(chartTitleLabel.snp.bottom).offset(8)
        }
        
        if let chart = self.chartView {
            chart.lineView?.lineChartView.preChartBtn.isHidden = true
            chart.lineView?.lineChartView.nextChartBtn.isHidden = true
            chart.scrollView.isScrollEnabled = false
            chartBackView.addSubview(chart)
            chart.snp.makeConstraints { make in
                make.top.equalTo(chartCycleLabel.snp.bottom)
                make.left.right.bottom.equalToSuperview()
            }
        }
    }

    @objc func didTapNavRightButton() {
        showActivityHUD()
        var screeshotWidth = kScreenWidth
        if let width = self.chartView?.lineView?.lineChartView.scrollView.contentSize.width,
           width > kScreenWidth {
            screeshotWidth = width + 50
        }
        let captureView = LineChartCaptureView(frame: CGRect(x: -kScreenWidth, y: 0, width: screeshotWidth, height: 974), chartView: self.chartView, chartTitle: self.chartTitleLabel.text, chartCycle: self.chartCycleLabel.text)
        self.view.addSubview(captureView)
        
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 1) {
            if let screenshotd = takeScreenshotOfView(captureView) {
                self.saveImageToPhotosAlbum(screenshotd)
            }
            self.navigationController?.popViewController(animated: false)
            hideActivityHUD()
        }
}
    
    func saveImageToPhotosAlbum(_ image: UIImage) {
        PHPhotoLibrary.shared().performChanges({
            let assetChangeRequest = PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { completed, error in
            if completed {
                showToachMessage(message: "Save success")
            } else {
                
            }
        }
    }
}

class LineChartCaptureView: UIView {
    
    let logoImageView: UIImageView = {
        let imageView = UIImageView(image: #imageLiteral(resourceName: "downloadLogoIcon"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    let chartBackView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    let chartTitleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .mainTextColor
        label.font = .mediumGilroyFont(16)
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    let chartCycleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .mainTextColor
        label.font = .regularGilroyFont(12)
        label.numberOfLines = 0
        label.textAlignment = .center
        label.text = "Cycle: 07/17/2024-08/17/2024"
        return label
    }()
    
    let qrCodeImageView: UIImageView = {
        let imageView = UIImageView(image: #imageLiteral(resourceName: "qrcodeIcon"))
        imageView.contentMode = .scaleAspectFit
        imageView.image = LBXScanNative.createQR(with: UserDefaults.standard.downloadUrl, qrSize: CGSize(width: 100, height: 100))
        return imageView
    }()
    
    let qrCodeDescLabel: UILabel = {
        let label = UILabel()
        label.textColor = .mainTextColor
        label.font = .regularGilroyFont(13)
        label.numberOfLines = 0
        label.textAlignment = .center
        label.text = "Download, know more about yourself"
        return label
    }()
    
    var chartView: ChartDetailView?
    
    init(frame: CGRect, chartView: ChartDetailView? = nil, chartTitle: String? = nil, chartCycle: String? = nil) {
        super.init(frame: frame)
        
        self.backgroundColor = .themeColor
        self.chartView = chartView
        
        self.chartTitleLabel.text = chartTitle
        self.chartCycleLabel.text = chartCycle
        setupUI()
        self.chartView?.layoutIfNeeded()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // total height = 832
    func setupUI() {
        [logoImageView, chartBackView, qrCodeImageView, qrCodeDescLabel].forEach(addSubview)
        
        chartBackView.addSubview(chartTitleLabel)
        chartBackView.addSubview(chartCycleLabel)
        
        //20 + 40
        logoImageView.snp.makeConstraints { make in
            make.width.equalTo(206)
            make.height.equalTo(40)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(20)
        }
        
        // 20 + 600
        chartBackView.snp.makeConstraints { make in
            make.height.equalTo(742)
            make.left.right.equalToSuperview().inset(10)
            make.centerX.equalToSuperview()
            make.top.equalTo(logoImageView.snp.bottom).offset(20)
        }
        
        // 20 + 80
        qrCodeImageView.snp.makeConstraints { make in
            make.width.equalTo(80)
            make.height.equalTo(80)
            make.centerX.equalToSuperview()
            make.top.equalTo(chartBackView.snp.bottom).offset(20)
        }
        
        //16+16+20
        qrCodeDescLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.height.equalTo(16)
            make.left.right.bottom.equalToSuperview().inset(20)
            make.top.equalTo(qrCodeImageView.snp.bottom).offset(16)
        }
        
        chartTitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.top.equalToSuperview().inset(24)
        }
        
        chartCycleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(chartTitleLabel.snp.bottom).offset(8)
        }
        
        if let chart = self.chartView {
            chart.lineView?.lineChartView.preChartBtn.isHidden = true
            chart.lineView?.lineChartView.nextChartBtn.isHidden = true
            chart.scrollView.isScrollEnabled = false
            chartBackView.addSubview(chart)
            chart.snp.makeConstraints { make in
                make.top.equalTo(chartCycleLabel.snp.bottom)
                make.left.right.bottom.equalToSuperview()
            }
        }
    }
}
