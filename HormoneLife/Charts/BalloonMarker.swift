//
//  BalloonMarker.swift
//  ChartsDemo
//
//  Copyright 2015 <PERSON> & <PERSON>
//  A port of MPAndroidChart for iOS
//  Licensed under Apache License 2.0
//
//  https://github.com/danielgindi/Charts
//

import Foundation
import DGCharts
#if canImport(UIKit)
    import UIKit
#endif

open class BalloonMarker: MarkerImage
{
    @objc open var color: UIColor
    @objc open var arrowSize = CGSize(width: 15, height: 11)
    @objc open var font: UIFont
    @objc open var textColor: UIColor
    @objc open var insets: UIEdgeInsets
    @objc open var minimumSize = CGSize()
    
    fileprivate var label: String?
    fileprivate var _labelSize: CGSize = CGSize()
    fileprivate var _paragraphStyle: NSMutableParagraphStyle?
    fileprivate var _drawAttributes = [NSAttributedString.Key : Any]()
    
    var valueDrawAttributes = [NSAttributedString.Key : Any]()
    var valueParagraphStyle: NSMutableParagraphStyle?
    var valueLabel: String = ""
    
    @objc public init(color: UIColor, font: UIFont, textColor: UIColor, insets: UIEdgeInsets)
    {
        self.color = color
        self.font = font
        self.textColor = textColor
        self.insets = insets
        
        valueParagraphStyle = NSParagraphStyle.default.mutableCopy() as? NSMutableParagraphStyle
        valueParagraphStyle?.alignment = .right
        valueDrawAttributes[.font] = UIFont.mediumGilroyFont(font.pointSize + 8)
        valueDrawAttributes[.paragraphStyle] = valueParagraphStyle
        valueDrawAttributes[.foregroundColor] = self.textColor
        
        
        _paragraphStyle = NSParagraphStyle.default.mutableCopy() as? NSMutableParagraphStyle
        _paragraphStyle?.alignment = .left
        _paragraphStyle?.lineSpacing = 10
        super.init()
    }
    
    open override func offsetForDrawing(atPoint point: CGPoint) -> CGPoint
    {
        var offset = self.offset
        var size = self.size

        if size.width == 0.0 && image != nil
        {
            size.width = image!.size.width
        }
        if size.height == 0.0 && image != nil
        {
            size.height = image!.size.height
        }

        let width = size.width
        let height = size.height
        let padding: CGFloat = 8.0

        var origin = point
        origin.x -= width / 2
        origin.y -= height

        if origin.x + offset.x < 0.0
        {
            offset.x = -origin.x + padding
        }
        else if let chart = chartView,
            origin.x + width + offset.x > chart.bounds.size.width
        {
            offset.x = chart.bounds.size.width - origin.x - width - padding
        }

        if origin.y + offset.y < 0
        {
            offset.y = height + padding;
        }
        else if let chart = chartView,
            origin.y + height + offset.y > chart.bounds.size.height
        {
            offset.y = chart.bounds.size.height - origin.y - height - padding
        }

        return offset
    }
    
    open override func draw(context: CGContext, point: CGPoint)
    {
        guard let label = label else { return }
        
        let offset = self.offsetForDrawing(atPoint: point)
        let size = self.size
        
        var rect = CGRect(
            origin: CGPoint(
                x: point.x + offset.x,
                y: point.y + offset.y),
            size: size)
        rect.origin.x -= size.width / 2.0
        rect.origin.y -= size.height
        
        context.saveGState()

        context.setFillColor(color.cgColor)

        if offset.y > 0
        {
            context.beginPath()
            context.move(to: CGPoint(
                x: rect.origin.x,
                y: rect.origin.y + arrowSize.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x + (rect.size.width - arrowSize.width) / 2.0,
                y: rect.origin.y + arrowSize.height))
            //arrow vertex
            context.addLine(to: CGPoint(
                x: point.x,
                y: point.y))
            context.addLine(to: CGPoint(
                x: rect.origin.x + (rect.size.width + arrowSize.width) / 2.0,
                y: rect.origin.y + arrowSize.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x + rect.size.width,
                y: rect.origin.y + arrowSize.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x + rect.size.width,
                y: rect.origin.y + rect.size.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x,
                y: rect.origin.y + rect.size.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x,
                y: rect.origin.y + arrowSize.height))
            context.fillPath()
        }
        else
        {
            context.beginPath()
            context.move(to: CGPoint(
                x: rect.origin.x,
                y: rect.origin.y))
            context.addLine(to: CGPoint(
                x: rect.origin.x + rect.size.width,
                y: rect.origin.y))
            context.addLine(to: CGPoint(
                x: rect.origin.x + rect.size.width,
                y: rect.origin.y + rect.size.height - arrowSize.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x + (rect.size.width + arrowSize.width) / 2.0,
                y: rect.origin.y + rect.size.height - arrowSize.height))
            //arrow vertex
            context.addLine(to: CGPoint(
                x: point.x,
                y: point.y))
            context.addLine(to: CGPoint(
                x: rect.origin.x + (rect.size.width - arrowSize.width) / 2.0,
                y: rect.origin.y + rect.size.height - arrowSize.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x,
                y: rect.origin.y + rect.size.height - arrowSize.height))
            context.addLine(to: CGPoint(
                x: rect.origin.x,
                y: rect.origin.y))
            context.fillPath()
        }
        
        if offset.y > 0 {
            rect.origin.y += self.insets.top + arrowSize.height
        } else {
            rect.origin.y += self.insets.top
        }

        rect.size.height -= self.insets.top + self.insets.bottom
        
        // valueRect
        var valueRect = rect
        valueRect.origin.x -= (rect.size.width * 1 / 3) + 45
        let valueFontHeight = self.font.pointSize + 10
        valueRect.origin.y += (rect.size.height / 2) - (valueFontHeight / 2)
        
        // cycle day/ date label rect
        var cyCleDateRect = rect
        cyCleDateRect.origin.x += (rect.size.width * 1 / 3) + 16
        let labelFontHeight = self.font.pointSize + 10 + self.font.pointSize
        cyCleDateRect.origin.y += (rect.size.height / 2) - (labelFontHeight / 2)  // y.alignment = .center
        
        UIGraphicsPushContext(context)
        
        valueLabel.draw(in: valueRect, withAttributes: valueDrawAttributes)
        label.draw(in: cyCleDateRect, withAttributes: _drawAttributes)
        
        UIGraphicsPopContext()
        
        context.restoreGState()
    }
    
    open override func refreshContent(entry: ChartDataEntry, highlight: Highlight)
    {
        setLabel(String(entry.y))
    }
    
    @objc open func setLabel(_ newLabel: String)
    {
        label = newLabel
        
        _drawAttributes.removeAll()
        _drawAttributes[.font] = self.font
        _drawAttributes[.paragraphStyle] = _paragraphStyle
        _drawAttributes[.foregroundColor] = self.textColor
        
        _labelSize = label?.size(withAttributes: _drawAttributes) ?? CGSize.zero
        
        var size = CGSize()
        size.width = _labelSize.width + self.insets.left + self.insets.right
        size.height = _labelSize.height + self.insets.top + self.insets.bottom
        size.width = max(minimumSize.width, size.width)
        size.height = max(minimumSize.height, size.height)
        self.size = size
    }
}


public class XYMarkerView: BalloonMarker {
    public var xAxisValueFormatter: AxisValueFormatter
    var yFormatter = NumberFormatter()
    var monthDayList: [String] = []
    var chartType: LineChartDemoView.ChartType = .lh
    var testResultList: [TestPageResultList] = []
    var temperatureUnit: Int = 0
    var resultList: [Double] = []
    
    public init(color: UIColor, font: UIFont, textColor: UIColor, insets: UIEdgeInsets,
                xAxisValueFormatter: AxisValueFormatter) {
        self.xAxisValueFormatter = xAxisValueFormatter
        super.init(color: color, font: font, textColor: textColor, insets: insets)
    }
    
    public override func refreshContent(entry: ChartDataEntry, highlight: Highlight) {
        let percentMark = chartType == .conception ? "%" : ""
        var dateStr = ""
        
        if monthDayList.count > Int(entry.x) - 1 {
            dateStr = monthDayList[Int(entry.x) - 1]
        }
        
        if chartType != .conception,
           testResultList.count > Int(entry.x) - 1,
           let dateString = testResultList[Int(entry.x) - 1].markTime {
            let mon = dateString.yyyyMMddHHmmss_enMM().prefix(3)
            let day = dateString.yyyyMMddHHmmss_ddHHmm()
            dateStr = mon + " " + day
        }
            
        let string = "Cycle Day  "
            + xAxisValueFormatter.stringForValue(entry.x, axis: XAxis())
            + "\n"
            + dateStr
        
        setLabel(string)
        
        
        if let value = yFormatter.string(from: NSNumber(floatLiteral: entry.y)) {
//            valueLabel = value == "0" || value == "-0" || chartType == .pdg || chartType == .hcg ? "" : "\(value)\(percentMark)"
            valueLabel = chartType == .pdg || chartType == .hcg ? "" : "\(value)\(percentMark)"
            
            if testResultList.count > Int(entry.x) - 1 {
                let result = testResultList[Int(entry.x) - 1]
                if value == "0" && result.lastResult == nil {
                    valueLabel = ""
                }
            }
            
            if chartType == .temp, resultList.count > Int(entry.x) - 1 {
                if temperatureUnit == 1 && resultList[Int(entry.x) - 1] == 0 {
                    valueLabel = ""
                }
                
                if temperatureUnit == 2 && resultList[Int(entry.x) - 1] == 0 {
                    valueLabel = ""
                }
            }
        }
    }
}
