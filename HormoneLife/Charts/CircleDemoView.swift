//
//  CircleDemoView.swift
//  TestDemo
//
//  Created by Tank on 2024/7/23.
//

import UIKit
import SnapKit

class TestResultView: UIView {
    
    static let bottomDescPadding: CGFloat = 60
    static let leadingPadding: CGFloat = 20 + 34
    static let topTandCPadding: CGFloat = 80
    static let circleRadius: CGFloat = (UIScreen.main.bounds.width - TestResultView.leadingPadding * 2) / 2
    static let viewHeight = TestResultView.circleRadius + TestResultView.topTandCPadding + TestResultView.bottomDescPadding
    
    // height: topTandCPadding + circleRadius
    let circleBackView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 8
        v.layer.masksToBounds = true
        return v
    }()

    // height: let circleRadius = (UIScreen.main.bounds.width - leadingPadding * 2) / 2
    lazy var circleView: CircleDemoView = {
        let leadingPading = 34.0
        return CircleDemoView(frame: CGRect(x: leadingPading, y: 0, width: self.frame.width - leadingPading * 2, height: self.frame.width - leadingPading * 2))
    }()
    
    // height: 80
    lazy var descriptionLabel: UILabel = {
        let d = UILabel()
        d.font = .regularGilroyFont(10)
        d.textColor = .mainTextColor
        d.numberOfLines = 0
        d.textAlignment = .center
        d.text = tipsDescription
        d.setLineHeight(8)
        return d
    }()
    
    var tipsDescription: String {
        if testType == .HCG {
            return "You need to manually select the result as Positive or Negative, no auto-result here!"
        } else {
            return "If you are not satisfied with the automatic detection result, you can drag the annotation above to modify it."
        }
    }
    
    var testType: TestType = .FSH
    
    override init(frame: CGRect) {
        let width: CGFloat = UIScreen.main.bounds.width - 40
        let height = Self.viewHeight
        super.init(frame: CGRect(x: 0, y: 0, width: width, height: height))
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUIWith(_ style: TestType, testDetail: TestPaperDetail) {
        self.testType = style
        backgroundColor = .clear
        
        circleView.setupUIWith(style, testDetail: testDetail)
        addSubview(circleBackView)
        circleBackView.addSubview(circleView)
        addSubview(descriptionLabel)
        
        
        circleBackView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(TestResultView.topTandCPadding + TestResultView.circleRadius)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(circleBackView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
        }
    }
}

// 先init(frame: 给frame), 再setupUIWith传参数
class CircleDemoView: UIView {
    
    var bigCircle: UIView!
    var smallCircle: UIView!
    var squardView: UIView!
    var movingView: UIView!
    var cView: UIView!
    var tView: UIView = {
        let v = UIView()
        v.layer.borderColor = UIColor("#FADBE9").cgColor
        v.layer.borderWidth = 1
        return v
    }()
    var pinView: UIImageView!
    
    var radius: CGFloat = 0
    var pinRadius: CGFloat = 0
    var centerX: CGFloat = 0
    var centerY: CGFloat = 0
    var circleWidth: CGFloat = 30
    let innerPadding: CGFloat = 6
    let topOutPadding: CGFloat = 55
    
    var style: TestType = .PdG
    var value: CGFloat = 0.0 {
        didSet {
            self.ratio = CGFloat(value) / CGFloat(style.maxValue)
            tView.backgroundColor = style.setTintColor(alpha: self.ratio)
            //print(self.ratio)
        }
    }
    var ratio: CGFloat = 0.5
    
    let resultLabel = UILabel()
    let resultLevelLabel = UILabel()
    var testDetail: TestPaperDetail?
    
    func setupUIWith(_ style: TestType, testDetail: TestPaperDetail) {
                
        self.testDetail = testDetail
        self.style = style
        if let lastValue = testDetail.lastResult {
            
            if style == .PdG {
                let doubleDGcriticalV = (HomeDataSingleton.shared().homeData?.pdgCriticalValue ?? 6) * 2
                let pdgV = CGFloat(lastValue) > CGFloat(doubleDGcriticalV) ? CGFloat(doubleDGcriticalV) : CGFloat(lastValue)
                self.value = pdgV
            } else if style == .HCG {
                let doubleDGcriticalV = (HomeDataSingleton.shared().homeData?.hcgCriticalValue ?? 3) * 2
                let hcgV = CGFloat(testDetail.lastResult ?? 0) > CGFloat(doubleDGcriticalV) ? CGFloat(doubleDGcriticalV) : CGFloat(lastValue)
                self.value = hcgV
            } else {
                self.value = CGFloat(lastValue)
            }
        } else {
            if style == .PdG {
                let doubleDGcriticalV = (HomeDataSingleton.shared().homeData?.pdgCriticalValue ?? 6) * 2
                let pdgV = CGFloat(testDetail.resultValue ?? 0) > CGFloat(doubleDGcriticalV) ? CGFloat(doubleDGcriticalV) : CGFloat(testDetail.resultValue ?? 0)
                self.value = pdgV
            } else if style == .HCG {
                let doubleDGcriticalV = (HomeDataSingleton.shared().homeData?.hcgCriticalValue ?? 3) * 2
                let hcgV = CGFloat(testDetail.resultValue ?? 0) > CGFloat(doubleDGcriticalV) ? CGFloat(doubleDGcriticalV) : CGFloat(testDetail.resultValue ?? 0)
                self.value = hcgV
            } else {
                self.value = CGFloat(testDetail.resultValue ?? 0)
            }
        }
//        backgroundColor = .lightGray
        clipsToBounds = true
        isUserInteractionEnabled = true
        let circleFrame = CGRect(x: 0, y: topOutPadding, width: self.frame.width, height: self.frame.height)
       
        bigCircle = UIView(frame: circleFrame)
        bigCircle.layer.cornerRadius = circleFrame.width / 2
        bigCircle.layer.masksToBounds = true
        bigCircle.backgroundColor = .systemPink
        bigCircle.isUserInteractionEnabled = false
        bigCircle.layer.addSublayer(style.circleColorLayerOn(bigCircle))
        bigCircle.layer.borderColor = style.boarderColor
        bigCircle.layer.borderWidth = style.isShowBoarder ? 1 : 0
        
        centerX = circleFrame.midX
        centerY = circleFrame.midY
        
        smallCircle = UIView(frame: CGRect(x: circleWidth, y: circleWidth, width: circleFrame.width - circleWidth * 2, height: circleFrame.height - circleWidth * 2))
        smallCircle.layer.cornerRadius = smallCircle.width() / 2
        smallCircle.layer.masksToBounds = true
        smallCircle.backgroundColor = .white
        smallCircle.isUserInteractionEnabled = false
        smallCircle.layer.borderColor = style.boarderColor
        smallCircle.layer.borderWidth = style.isShowBoarder ? 1 : 0
        
        squardView = UIView(frame: CGRect(x: 0, y: centerY, width: circleFrame.width, height: circleFrame.width / 2))
        squardView.backgroundColor = .white
        squardView.isUserInteractionEnabled = false
        
        let lineView = UIView(frame: CGRectMake(0, centerY, circleWidth + 1, 1))
        lineView.layer.borderColor = style.boarderColor
        lineView.layer.borderWidth = 1
        lineView.isHidden = !style.isShowBoarder
            
        let innerPadding: CGFloat = 6
        let movingViewWidth = circleWidth - innerPadding * 2
        movingView = UIView(frame: CGRect(x: circleFrame.midX - movingViewWidth / 2, y: circleFrame.minY + innerPadding, width: movingViewWidth, height: movingViewWidth))
        movingView.layer.cornerRadius = movingViewWidth / 2
        movingView.backgroundColor = .white
        movingView.layer.shadowColor = UIColor.black.cgColor
        movingView.layer.shadowOpacity = 0.3
        movingView.layer.shadowRadius = 6
        movingView.layer.shadowOffset = CGSize(width: 0, height: 6)
        movingView.isUserInteractionEnabled = false
        
        //movingView的轨迹圆半径
        radius = circleFrame.width / 2 - innerPadding - movingViewWidth / 2
        pinRadius = circleFrame.width / 2 - circleWidth - 12 - 8
        
        pinView = UIImageView(frame: CGRect(x: centerX - 8, y: circleWidth + 12 + 8, width: 16, height: 16))
        pinView.image = UIImage(named: "pinImage")
        pinView.contentMode = .scaleAspectFill
        pinView.isHidden = true // TODO:
        
        initMovingViewPosition(ratio)
        
        
        addSubview(bigCircle)
        bigCircle.addSubview(smallCircle)
        addSubview(squardView)
        bigCircle.addSubview(movingView)
        bigCircle.addSubview(pinView)
        addSubview(lineView)
        
        setupTandCView()
        setupTitleView()
    }
    
    func initMovingViewPosition(_ ratio: CGFloat) {
        let angleValue = 180 * ratio
        
        //求直角边长 sin cos
        let b = radius * cos(angleValue * .pi / 180)
        let a = radius * sin(angleValue * .pi / 180)
        
        movingView.center.x = centerX - b
        movingView.center.y = (ratio == 0 || ratio == 1) ? centerY - movingView.height() / 2 - topOutPadding : centerY - a - topOutPadding
        
        movingView.transform = CGAffineTransform(rotationAngle: angleValue * .pi / 180)
        
        // pin
        let pinB = pinRadius * cos(angleValue * .pi / 180)
        let pinA = pinRadius * sin(angleValue * .pi / 180)
        
//        pinView.center.x = centerX - pinB
//        pinView.center.y = (ratio == 0 || ratio == 1) ? centerY - pinView.height() / 2 - topOutPadding : centerY - pinA - topOutPadding
        
        //pinView.transform = CGAffineTransform(rotationAngle: -angleValue * .pi / 180)
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let touch = touches.first {
            let location = touch.location(in: self)

            guard squardView.frame.contains(location) == false else {
                return
            }
            if location.x > centerX - radius && location.x < centerX + radius {
                let distanceX = abs(centerX - location.x)
                let y = centerY - sqrt(radius * radius - distanceX * distanceX)
                movingView.center.x = location.x
                movingView.center.y = y - topOutPadding
                
                //求夹角
                let angle = asin(distanceX / radius)
                let angleValue = location.x <= centerX ? -angle : angle
                movingView.transform = CGAffineTransform(rotationAngle: angleValue)
                
                //updateValue
                var offset: CGFloat = style == .LhUltra ? 70 : 12
                if style == .FSH {
                    offset = 55
                } else if style == .LH {
                    offset = 2.7
                }
                let midValue: CGFloat = CGFloat(style.maxValue / 2)
                if location.x > centerX {
                    let v = CGFloat(offset * angleValue / .pi + midValue)
                    let dynamicRadio = v < CGFloat(style.maxValue) ? v : CGFloat(style.maxValue)
                    self.value = dynamicRadio
                } else {
                    let v = CGFloat(-offset * angleValue / .pi + midValue)
                    let dynamicRadio = v < CGFloat(style.maxValue) ? v : CGFloat(style.maxValue)
                    self.value = CGFloat(style.maxValue) - dynamicRadio
                }
                
                resultLabel.text = style.resultValue(value: value, ratio: ratio, testDetail: self.testDetail)
                resultLabel.textColor = style.resultValueColor(value: value, testDetail: testDetail, resultLabel: resultLabel.text ?? "")
                resultLevelLabel.text = style.resultLevel(lastResultLabel: testDetail?.lastResultLabel, value: value, ratio: ratio)
                
                //pin
                pinView.transform = CGAffineTransform(rotationAngle: angleValue)
                
            } else if location.x <= centerX - radius && location.y >= centerY {
                movingView.center.x = centerX - radius
                movingView.center.y = centerY - movingView.width() / 2 - topOutPadding
                movingView.transform = CGAffineTransform(rotationAngle: asin(1))
                pinView.transform = CGAffineTransform(rotationAngle: -asin(1))
                
                self.value = CGFloat(style.minValue)
                resultLabel.text = style.resultValue(value: value, ratio: ratio, testDetail: self.testDetail)
                resultLevelLabel.text = style.resultLevel(lastResultLabel: testDetail?.lastResultLabel, value: value, ratio: ratio)
            } else if location.x >= centerX + radius && location.y >= centerY {
                movingView.center.x = centerX + radius
                movingView.center.y = centerY - movingView.width() / 2 - topOutPadding
                movingView.transform = CGAffineTransform(rotationAngle: asin(1))
                pinView.transform = CGAffineTransform(rotationAngle: asin(1))
                
                self.value = CGFloat(style.maxValue)
                resultLabel.text = style.resultValue(value: value, ratio: ratio, testDetail: self.testDetail)
                resultLevelLabel.text = style.resultLevel(lastResultLabel: testDetail?.lastResultLabel, value: value, ratio: ratio)
            }
        }
    }
    
    private func setupTandCView() {
        let tLabel = UILabel()
        tLabel.font = .boldGilroyFont(12)
        tLabel.textColor = .mainTextColor
        tLabel.text = "T"
        tLabel.textAlignment = .right
        
        let cLabel = UILabel()
        cLabel.font = .boldGilroyFont(12)
        cLabel.textColor = .mainTextColor
        cLabel.text = "C"
        cLabel.textAlignment = .left
//        cLabel.isHidden = style != .LH
        cLabel.isHidden = true
        
        cView = UIView()
        cView.layer.borderColor = style.boarderColor
        cView.layer.borderWidth = 1
        cView.backgroundColor = style.setTintColor(alpha: 0.7)
//        cView.isHidden = style != .LH
        cView.isHidden = true
        
        [tLabel, tView, cLabel, cView].forEach(addSubview)
        
        tLabel.snp.makeConstraints { make in
            make.trailing.equalTo(snp.centerX).offset(style == .LH ? -10 : 0)
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(12)
        }
        
        tView.snp.makeConstraints { make in
            make.centerX.equalTo(tLabel)
            make.top.equalTo(tLabel.snp.bottom).offset(4)
            make.height.equalTo(19)
            make.width.equalTo(8)
        }
        
        cLabel.snp.makeConstraints { make in
            make.leading.equalTo(tLabel.snp.trailing).offset(20)
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(12)
        }
        
        cView.snp.makeConstraints { make in
            make.centerX.equalTo(cLabel)
            make.top.equalTo(cLabel.snp.bottom).offset(4)
            make.height.equalTo(19)
            make.width.equalTo(8)
        }
    }
    
    private func setupTitleView() {
        let titleLabel = UILabel()
        titleLabel.font = .regularGilroyFont(18)
        titleLabel.textColor = .mainTextColor
        titleLabel.text = style.title
        titleLabel.textAlignment = .center
        
        let leftValueLabel = UILabel()
        leftValueLabel.font = .regularGilroyFont(16)
        leftValueLabel.textColor = .mainTextColor
        leftValueLabel.text = "\(style.minValue)"
        leftValueLabel.textAlignment = .left
        leftValueLabel.isHidden = style.isHideMinMaxValue
        
        let rightValueLabel = UILabel()
        rightValueLabel.font = .regularGilroyFont(16)
        rightValueLabel.textColor = .mainTextColor
//        if style == .LH {
//            rightValueLabel.text = ">1"
//        } else {
            rightValueLabel.text = "\(style.maxValue)"
//        }
        rightValueLabel.textAlignment = .right
        rightValueLabel.isHidden = style.isHideMinMaxValue
        
        resultLabel.font = .regularGilroyFont(36)
        resultLabel.text = style.resultValue(value: value, ratio: ratio, testDetail: testDetail, isCreate: testDetail?.isCreate)
        resultLabel.textColor = style.resultValueColor(value: value, testDetail: testDetail, resultLabel: resultLabel.text ?? "")
        resultLabel.textAlignment = .center
        
        resultLevelLabel.font = .regularGilroyFont(16)
        resultLevelLabel.textColor = .mainTextColor
        resultLevelLabel.textAlignment = .left
        resultLevelLabel.text = style.resultLevel(lastResultLabel: testDetail?.lastResultLabel, value: value, ratio: ratio)
        
        let leftStateLabel = UILabel()
        leftStateLabel.font = .regularGilroyFont(10)
        leftStateLabel.textColor = .mainTextColor
        leftStateLabel.text = style.leftState
        leftStateLabel.textAlignment = .left
        
        let rightStateLabel = UILabel()
        rightStateLabel.font = .regularGilroyFont(10)
        rightStateLabel.textColor = .mainTextColor
        rightStateLabel.text = style.RightState
        rightStateLabel.textAlignment = .right
        
        [titleLabel, leftValueLabel, rightValueLabel, resultLabel, resultLevelLabel, leftStateLabel, rightStateLabel].forEach {
            addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(self.squardView.snp.top)
        }
        
        leftValueLabel.snp.makeConstraints { make in
            make.bottom.equalTo(titleLabel)
            make.left.equalToSuperview().inset(circleWidth + 16)
        }
        
        rightValueLabel.snp.makeConstraints { make in
            make.bottom.equalTo(titleLabel)
            make.right.equalToSuperview().inset(circleWidth + 16)
        }
        
        resultLabel.snp.makeConstraints { make in
            make.bottom.equalTo(titleLabel.snp.top).offset(-12)
            make.centerX.equalToSuperview().offset(resultLevelLabel.text == "" ? 0 : -25)
        }
        
        resultLevelLabel.snp.makeConstraints { make in
            make.centerY.equalTo(resultLabel).offset(6)
            make.left.equalTo(resultLabel.snp.right).offset(6)
        }
        
        rightStateLabel.snp.makeConstraints { make in
            make.centerY.equalTo(resultLabel).offset(6)
            make.left.equalTo(resultLabel.snp.right).offset(17)
        }
        
        leftStateLabel.snp.makeConstraints { make in
            make.centerY.equalTo(resultLabel).offset(6)
            make.right.equalTo(resultLabel.snp.left).offset(-17)
        }
    }
}
