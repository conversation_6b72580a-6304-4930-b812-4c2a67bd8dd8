//
//  ChartDetailColorLabelView.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/30.
//

import UIKit

class ChartDetailColorLabelView: UIView, CalendarLabelViewDelegate {
    func didTapSelf(_ label: CalendarLabel) {
        print(label.title)
        delegate?.didTapLabel(label)
    }

    lazy var periodView = CalendarLabelView(.period, delegate: self)
    lazy var fertileView = CalendarLabelView(.fertile, delegate: self)
    lazy var ovulationView = CalendarLabelView(.ovulation, delegate: self)
    lazy var sexView = CalendarLabelView(.sex, delegate: self)
    
    weak var delegate: CalendarLabelCellTableViewCellDelegate?

    init(delegate: CalendarLabelCellTableViewCellDelegate? = nil) {
        super.init(frame: .zero)
        self.delegate = delegate
        setupView()
    }
    
    private func setupView() {
        [periodView, fertileView, ovulationView, sexView].forEach(addSubview)
        
        periodView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview().inset(20)
        }
        
        fertileView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(periodView)
            make.leading.equalTo(periodView.snp.trailing).offset(20)
        }
        
        ovulationView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(fertileView)
            make.leading.equalTo(fertileView.snp.trailing).offset(20)
        }
        
        sexView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(ovulationView)
            make.leading.equalTo(ovulationView.snp.trailing).offset(20)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}


// TimeView
class ChartDetailDescriptionView: UIView {
    
    var titleLabel: UILabel = {
        let l = UILabel()
        l.font = .boldGilroyFont(12)
        l.textColor = .mainTextColor
        l.textAlignment = .center
        l.numberOfLines = 0
        l.text = "Today Your PdG Result is Negative"
        return l
    }()
    
    var cycleDayLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(12)
        l.textColor = .mainTextColor.withAlphaComponent(0.6)
        l.textAlignment = .center
        l.text = "Cycle day 7"
        return l
    }()
    
    var timeLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(12)
        l.textColor = .mainTextColor.withAlphaComponent(0.6)
        l.textAlignment = .center
        l.text = "Apr 10 10:30"
        return l
    }()
    
    var descLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(10)
        l.textColor = .mainTextColor
        l.textAlignment = .center
        l.numberOfLines = 0
        l.text = "Your result is Negative--this means that your PdG level is low and you may have not yet ovulated. lf confirmatory result is needed, please consult a doctor."
        return l
    }()
    
    init(title: String = "", cycleDay: String = "", date: String = "", descriptionStr: String = "") {
        super.init(frame: .zero)
        titleLabel.text = title
        cycleDayLabel.text = cycleDay
        timeLabel.text = date
        descLabel.text = descriptionStr
        setupUI()
        descLabel.setLineHeight(8)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        [titleLabel, cycleDayLabel, timeLabel, descLabel].forEach(addSubview)
        
        titleLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalToSuperview()
        }
        
        cycleDayLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(titleLabel.snp.bottom).offset(15)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(cycleDayLabel.snp.bottom).offset(2)
        }
        
        descLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(timeLabel.snp.bottom).offset(15)
            make.bottom.equalToSuperview().inset(30)
        }
    }
}
