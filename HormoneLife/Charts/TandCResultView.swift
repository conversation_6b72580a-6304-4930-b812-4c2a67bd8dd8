//
//  TandCResultView.swift
//  HormoneLife
//
//  Created by Tank on 2024/7/29.
//

import UIKit
import SnapKit

class TandCResultView: UIView {

    let title: UILabel = {
        let label = UILabel()
        label.font = .boldGilroyFont(16)
        label.textColor = .mainTextColor
        label.text = "T   C"
        return label
    }()

    let backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    var imageView: UIImageView = {
        let i = UIImageView(image: #imageLiteral(resourceName: "userTest"))
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        addSubview(title)
        addSubview(backView)
        backView.addSubview(imageView)
        
        title.snp.makeConstraints {
            $0.centerX.equalToSuperview()
            $0.top.equalToSuperview()
        }
        
        backView.snp.makeConstraints {
            $0.left.right.bottom.equalToSuperview()
            $0.top.equalTo(title.snp.bottom).offset(20)
            $0.height.equalTo(60)
        }
        backView.layer.cornerRadius = 8
        backView.layer.masksToBounds = true
        
        imageView.snp.makeConstraints {
            $0.edges.equalToSuperview().inset(8)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
