//
//  ChartsViewController.swift
//  HormoneLife
//
//  Created by <PERSON> on 2024/6/10.
//

import UIKit

class ChartsViewController: BaseViewController {

    private lazy var titlesView = ChartsTitleView(types: [.LhUltra, .PdG, .LH, .FSH, .HCG])
    
    let headerView = ChartsHeaderView()
    
    let cardView = ChartsCardView(titles: ["LH ULTRA", "PDG", "LH", "FSH", "HCG"])
    
    let chartView = LineChartFilledView()
    let chartView2 = LineChartFilledView()
    
    let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .fill
        stackView.distribution = .fill
        stackView.spacing = 10
        return stackView
    }()
    
    let container = UIView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .themeColor
        setNavigationBar()
        setupViews()
    }
    
    private func setNavigationBar() {
        navigationItem.leftBarButtonItem = nil
        navigationItem.title = "Calendar"
    }
    
    func setupViews() {
        view.addSubview(titlesView)
        titlesView.snp.makeConstraints { make in
            make.top.left.trailingMargin.equalTo(5)
        }
        
        view.addSubview(scrollView)
        scrollView.addSubview(container)
        container.addSubview(stackView)
        
        stackView.addArrangedSubview(headerView)
        stackView.setCustomSpacing(12, after: headerView)
        stackView.addArrangedSubview(cardView)
        stackView.setCustomSpacing(20, after: cardView)
        stackView.addArrangedSubview(chartView)
        stackView.setCustomSpacing(20, after: chartView)
        stackView.addArrangedSubview(chartView2)
        
        scrollView.snp.makeConstraints { make in
            make.left.right.bottom.equalTo(view)
            make.top.equalTo(titlesView.snp.bottom)
            make.width.equalTo(container)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.height.greaterThanOrEqualTo(scrollView)
        }
        
        stackView.snp.makeConstraints { make in
            make.left.equalTo(container)
            make.right.equalTo(container)
            make.top.equalTo(container)
            make.bottom.lessThanOrEqualTo(-5)
        }
    }
}
