//
//  ChartsTitleView.swift
//  HormoneLife
//
//  Created by <PERSON> on 2024/6/10.
//

import UIKit
import SnapKit

protocol ChartsTitleViewDelegate: AnyObject {
    func buttonDidClickWithType(type: TestType)
}

func testTypeByTypeString(typeString: String?) -> TestType {
    guard let str = typeString else {
        return .HCG
    }
    switch str.uppercased() {
    case "LH ULTRA":
        return .LhUltra
    case "PDG":
        return .PdG
    case "FSH":
        return .FSH
    case "LH":
        return .LH
    default:
        return .HCG
    }
}

enum TestType: Int, CustomStringConvertible, CaseIterable {
    case LhUltra = 0
    case LH
    case HCG
    case PdG
    case FSH
    
    var description: String {
        switch self {
        case .LhUltra:
           return "LH Ultra"
        case .PdG:
            return "PdG"
        case .FSH:
            return "FSH"
        case .LH:
            return "LH"
        case .HCG:
            return "HCG"
        }
    }
    
    var titleColor: UIColor {
        switch self {
        case .LhUltra:
           return "#5A5AA2".uiColor
            //return UIColor.rgba(90, 88, 165)
        case .PdG:
            return "#F6AD3C".uiColor
            //return UIColor.rgba(246, 173, 58)
        case .FSH:
            return "#DC83B3".uiColor
            //return UIColor.rgba(221, 130, 179)
        case .LH:
            return "#3ABEEF".uiColor
            //return UIColor.rgba(56, 190, 239)
        case .HCG:
            return "#ED6E67".uiColor
            //return UIColor.rgba(236, 109, 101)
        }
    }
    
    var isShowBoarder: Bool {
        switch self {
        case .HCG, .PdG:
            return true
        default:
            return false
        }
    }
    
    var boarderColor: CGColor {
        //TODO:
        UIColor("#FADBE9").cgColor
    }
    
    func setTintColor(alpha: CGFloat) -> UIColor {
        switch self {
        case .LH, .LhUltra, .FSH: return UIColor.rgba(133, 18, 27).withAlphaComponent(alpha)
        default:
            if alpha < 0.5 {
                return .white
            } else {
                let ratio = (alpha - 0.5) / 0.5
                return UIColor.rgba(167, 38, 80).withAlphaComponent(ratio)
            }
        }
    }
    
    var chartOffset: CGPoint {
        switch self {
        case .LhUltra:
            return CGPoint(x: 0, y: 0)
        case .PdG:
            return CGPoint(x: 0, y: 422)
        case .LH:
            return CGPoint(x: 0, y: 790)
        case .FSH:
            return CGPoint(x: 0, y: 1156)
        case .HCG:
            return CGPoint(x: 0, y: 1276)
        }
    }
    
    func circleColorLayerOn(_ view: UIView) -> CALayer {
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = view.bounds
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        
        switch self {
        case .LhUltra, .LH, .FSH:
            // TODO:
            gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
            gradientLayer.endPoint = CGPoint(x: 1, y: 0.7)
//            gradientLayer.colors = [UIColor("#FCF8F8").cgColor, UIColor("#9D2429").cgColor]
            gradientLayer.colors = [
                UIColor.rgba(255, 255, 255).cgColor,
                UIColor.rgba(254, 244, 246).cgColor,
                UIColor.rgba(245, 221, 228).cgColor,
                UIColor.rgba(234, 196, 208).cgColor,
                UIColor.rgba(225, 173, 190).cgColor,
                UIColor.rgba(207, 129, 155).cgColor,
                UIColor.rgba(197, 109, 138).cgColor,
                UIColor.rgba(181, 71, 106).cgColor,
                UIColor.rgba(167, 38, 80).cgColor,
                UIColor.rgba(133, 18, 27).cgColor
            ]
            return gradientLayer
        default:
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.1)
            gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
            gradientLayer.colors = [
                UIColor.rgba(255, 255, 255).cgColor,
                UIColor.rgba(167, 38, 80).cgColor
            ]
            return gradientLayer
        }
    }
    
    var minValue: Int {
        switch self {
        case .LH, .LhUltra, .FSH: return 0
        default: return 0
        }
    }
    
    var maxValue: CGFloat {
        switch self {
        case .LhUltra: return 65
        case .FSH: return 50
        case .LH: return 2.5
        case .HCG: return CGFloat(HomeDataSingleton.shared().homeData?.hcgCriticalValue ?? 3) * 2.0
        case .PdG: return CGFloat(HomeDataSingleton.shared().homeData?.pdgCriticalValue ?? 6) * 2.0
        }
    }
    
    var isHideMinMaxValue: Bool {
        switch self {
        case .PdG, .HCG: return true
            
        default: return false
        }
    }
    
    var title: String {
        switch self {
        case .LhUltra: return "LH Ultra Result"
        case .LH: return "LH Result"
        case .PdG: return "PdG Result"
        case .FSH: return "FSH Result"
        case .HCG: return "HCG Result"
        }
    }
    
    func resultValue(value: CGFloat = 0, ratio: CGFloat = 0.0, testDetail: TestPaperDetail? = nil, isCreate: Bool? = false) -> String {
        switch self {
        case .LhUltra, .FSH: return "\(Int(value))"
        case .LH: return String(format: "%.2f", value)
        case .PdG:
//            if value != 0,
//               let result = testDetail?.resultValue,
//               let lastResult = testDetail?.lastResult,
//               let lastResultLabel = testDetail?.lastResultLabel,
//               result != lastResult {
//                return "\(lastResultLabel.prefix(3).capitalized)"
//            }
            
//            if value == 0 {
//                if self == .PdG {
//                    return "Neg"
//                }
//                return "N/A"
//            } else {
            // == 0.5 => Neg
                return ratio < 0.5 ? "Pos" : "Neg"
//            }
        case .HCG:
            
            if isCreate == true {
                return "N/A"
            }
            // == 0.5 => Neg
            NotificationCenter.default.post(Notification(name: Notification.Name("HCGResultEdited")))
            return ratio > 0.5 ? "Pos" : "Neg"
        }
    }
    
    func resultValueColor(value: Double = 0, ratio: CGFloat = 0.0, testDetail: TestPaperDetail? = nil, resultLabel: String = "") -> UIColor {
        switch self {
        case .LhUltra, .LH, .FSH: return .mainTextColor
        case .HCG, .PdG:
            if value != 0.0,
               let result = testDetail?.resultValue,
               let lastResult = testDetail?.lastResult,
               let lastResultLabel = testDetail?.lastResultLabel,
               lastResultLabel != "N/A",
               result != lastResult {
                return .mainTextColor
            }
            
            if value == 0.0 && resultLabel == "N/A" {
                return .lightGray
            } else {
                return .mainTextColor
            }
        }
    }
    
    func resultLevel(lastResultLabel: String? = nil, value: CGFloat = 0, ratio: CGFloat = 0) -> String {
        switch self {
//        case .LhUltra, .LH, .FSH:
//            if ratio < 1/3 {
//                return "(LOW)"
//            } else if ratio >= 1/3 && ratio < 2/3 {
//                return "(HIGHT)"
//            } else {
//                return "PEAK"
//            }
        case .LhUltra:
            if value < 25 {
                return "(Low)"
            } else if value >= 25 && value < 45 {
                return "(High)"
            } else {
                return "(Peak)"
            }
        case .FSH:
            if value < 10 {
                return "(Low)"
            } else if value >= 10 && value < 25 {
                return "(High)"
            } else {
                return "(Peak)"
            }
        case .LH:
            if value < 0.75 {
                return "(Low)"
            } else if value >= 0.75 && value < 1.0 {
                return "(High)"
            } else {
                return "(Peak)"
            }
        default: return ""
        }
    }
    
    var leftState: String {
        switch self {
        case .HCG: return "NEGATIVE"
        case .PdG: return "POSITIVE"
        default: return ""
        }
    }
    
    var RightState: String {
        switch self {
        case .HCG: return "POSITIVE"
        case .PdG: return "NEGATIVE"
        default: return ""
        }
    }
    
    var homePaperName: String {
        switch self {
        case .LhUltra: return "ultraSamplePaper"
        case .PdG: return "pdgSamplePaper"
        case .FSH: return "fshSamplePaper"
        case .LH: return "lhSamplePaper"
        case .HCG: return "hcgSamplePaper"
        }
    }
    
    var homePaperReadTimeLabel: String {
        switch self {
        case .LhUltra, .HCG, .PdG: return "Read between 3~5 mins"
        case .LH, .FSH: return "Read between 5~10 mins"
        }
    }
    
    var homeTitle: String {
        switch self {
        case .LhUltra: return "LH Ultra"
        case .PdG: return "PdG"
        case .FSH: return "FSH"
        case .LH: return "LH"
        case .HCG: return "HCG"
        }
    }
}

class ChartsTitleView: UIView {
    enum UseInWhere {
        case chartList
        case `default`
    }
    
    private var useInWhere: UseInWhere = .default
    private var types: [TestType] = []
    private var buttons: [ChartsTitleButton] = []
    var selectedIndex = 0

    let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .fill
        stackView.distribution = .fillEqually
        stackView.spacing = 10
        return stackView
    }()
    
    let container = UIView()
    let barView = UIView()
    var barViewCenterXConstraint: Constraint?
    
    weak var delegate: ChartsTitleViewDelegate?
    
    init(types: [TestType], useInWhere: UseInWhere = .default) {
        super.init(frame: .zero)
        self.types = types
        self.useInWhere = useInWhere
        setupViews()
        NotificationCenter.default.addObserver(self, selector: #selector(didReceiveNoti), name: NSNotification.Name("refreshHistoryRecordData"), object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("refreshHistoryRecordData"), object: nil)
    }
    
    @objc func didReceiveNoti(sender: Notification) {
        if let paperType = sender.userInfo?["paperType"] as? String {
            
            for item in buttons {
                if item.button.titleLabel?.text == paperType {
                    buttonDidClick(sender: item)
                    break
                }
            }
        }
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(scrollView)
        scrollView.addSubview(container)
        container.addSubview(stackView)
        container.addSubview(barView)
        barView.backgroundColor = .mainTextColor
        
        for i in types.enumerated() {
            let button = ChartsTitleButton.init(title: "\(i.element.description)", color: i.element.titleColor, isUseByDefault: useInWhere == .default)
            button.delegate = self
            button.tag = i.offset + 100
            buttons.append(button)
            stackView.addArrangedSubview(button)
        }
        buttons.first?.setIsSelcted(isSelected: true, isUseByDefault: useInWhere == .default)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(self)
            make.height.equalTo(container)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.width.greaterThanOrEqualTo(scrollView)
        }
        
        stackView.snp.makeConstraints { make in
            make.left.equalTo(container).offset(15)
            make.right.equalTo(container).offset(-15)
            make.top.equalTo(20)
            make.bottom.equalTo(-5)
        }

        barView.snp.makeConstraints { make in
            make.top.equalTo(stackView.snp.bottom)
            make.height.equalTo(2)
            make.width.equalTo(24)
            barViewCenterXConstraint = make.left.equalTo(stackView).inset(20).constraint
        }
        barView.layer.cornerRadius = 1
        barView.isHidden = useInWhere == .default
    }
    
    func updateBarViewContraint(sender: ChartsTitleButton) {
        buttons.forEach {
            $0.setIsSelcted(isSelected: $0.isEqual(sender), isUseByDefault: useInWhere == .default)
        }
        
        UIView.animate(withDuration: 0.15) {
            self.barViewCenterXConstraint?.update(inset: sender.frame.midX - 12)
            self.container.layoutIfNeeded()
        }
    }
}

extension ChartsTitleView: ChartsTitleButtonDelegate {
    func buttonDidClick(sender: ChartsTitleButton) {
        updateBarViewContraint(sender: sender)
        
        let offset = Int(scrollView.contentSize.width - scrollView.bounds.width) / (buttons.count - 1)
        let index = buttons.firstIndex(of: sender) ?? 0
        delegate?.buttonDidClickWithType(type: types[index])
        scrollView.setContentOffset(CGPoint(x: Double(offset * index), y: 0), animated: true)
    }
}

protocol ChartsTitleButtonDelegate: AnyObject {
    func buttonDidClick(sender: ChartsTitleButton)
}

class ChartsTitleButton: UIView {
    
    weak var delegate: ChartsTitleButtonDelegate?
    
    private var color: UIColor = .white
    
    let button: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitleColor("#360C5E".uiColor, for: .normal)
        button.titleLabel?.font = .regularGilroyFont(12)
        return button
    }()
    
//    let line: UIView = {
//        let view = UIView()
//        view.backgroundColor = "#043433".uiColor
//        view.isHidden = true
//        return view
//    }()
    
    init(title: String, color: UIColor = .themeColor, isUseByDefault: Bool = true) {
        super.init(frame: .zero)
        button.setTitle(title, for: .normal)
        button.addTarget(self, action: #selector(buttonClick(sender:)), for: .touchUpInside)
        setupViews()
        layer.cornerRadius = 4.0
        backgroundColor = isUseByDefault ? .white : .clear
        self.color = color
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        addSubview(button)
//        addSubview(line)
        button.snp.makeConstraints { make in
            make.top.equalTo(2.5)
            make.left.equalTo(5)
            make.right.equalTo(-5)
            make.bottom.equalToSuperview().offset(-2.5)
        }
        
//        line.snp.makeConstraints { make in
//            make.centerX.equalTo(button)
//            make.top.equalTo(button.snp.bottom).offset(5)
//            make.width.equalTo(button).multipliedBy(0.6)
//            make.height.equalTo(3)
//            make.bottom.equalTo(self)
//        }
    }
    
    @objc func buttonClick(sender: ChartsTitleButton) {
        delegate?.buttonDidClick(sender: self)
    }
    
    func setIsSelcted(isSelected: Bool, isUseByDefault: Bool = true) {
        button.isSelected = isSelected
        guard isUseByDefault else { return }
        backgroundColor = isSelected ? color : .white
        // line.isHidden = !isSelected
        // button.titleLabel?.font = isSelected ? .boldGilroyFont(12) : .regularGilroyFont(12)
        let titleColor = isSelected ? .white : "#360C5E".uiColor
        button.setTitleColor(titleColor, for: .normal)
    }
}
